%%%-------------------------------------------------------------------
%%% @doc
%%% MongoDB插件的共享头文件
%%% 包含所有模块共享的宏定义和记录定义
%%% @end
%%%-------------------------------------------------------------------

%%% @doc MongoDB插件头文件
%%% 定义插件使用的宏和常量
-ifndef(EMQX_PLUGIN_MONGODB_HRL).
-define(EMQX_PLUGIN_MONGODB_HRL, true).

%% 引入EMQX消息记录定义
-include_lib("emqx_utils/include/emqx_message.hrl").
%% 引入EMQX日志头文件
-include_lib("emqx/include/logger.hrl").

%% 定义共享订阅前缀宏
-define(SHARED_PREFIX, <<"$share">>).
-define(QUEUE_PREFIX, <<"$queue">>).

%%% @doc 插件资源组名称（二进制字符串，用于MongoDB连接）
-define(PLUGIN_MONGODB_RESOURCE_GROUP, <<"emqx_plugin_mongodb">>).

%%% @doc 插件资源ID（原子，用于EMQX资源标识）
-define(PLUGIN_MONGODB_RESOURCE_ID, mongodb_resource).

%%% @doc 插件使用的ETS表名（原子，用于ETS表标识）
-define(PLUGIN_MONGODB_TAB, emqx_plugin_mongodb_tab).

%% 钩子优先级
-define(HP_HIGHEST, 1000).
-define(HP_HIGH, 500).
-define(HP_MEDIUM, 0).
-define(HP_LOW, -500).
-define(HP_LOWEST, -1000).

%% 批处理相关宏
-define(DEFAULT_BATCH_SIZE, 1000).
-define(MIN_BATCH_SIZE, 100).
-define(MAX_BATCH_SIZE, 5000).
-define(DEFAULT_BATCH_TIMEOUT, 100). % ms
-define(MIN_BATCH_TIMEOUT, 10). % ms
-define(MAX_BATCH_TIMEOUT, 1000). % ms

%% 缓存相关宏
-define(TOPIC_MATCH_CACHE_TAB, emqx_plugin_mongodb_topic_cache).
-define(TOPIC_MATCH_CACHE_RECENT_TAB, emqx_plugin_mongodb_topic_recent).
-define(TOPIC_MATCH_CACHE_SIZE, 1000).
-define(TOPIC_MATCH_CACHE_TTL, 60000). % 60秒

%% 连接相关宏
-define(CONNECTION_POOLS_TAB, emqx_plugin_mongodb_connection_pools).
-define(CONNECTION_HEALTH_TAB, emqx_plugin_mongodb_connection_health).
-define(CONNECTION_SHARDS, 8).  % 默认分片数
-define(PREHEAT_CONNECTIONS, 4).  % 预热连接数
-define(HEALTH_CHECK_INTERVAL, 10000).  % 10秒
-define(HEALTH_THRESHOLD_GOOD, 0.9).  % 90%健康率为良好
-define(HEALTH_THRESHOLD_FAIR, 0.7).  % 70%健康率为一般
-define(HEALTH_DECAY_FACTOR, 0.95).  % 健康度衰减因子
-define(PING_COLLECTION, <<"foo">>).

%% 自适应批处理相关宏
-define(BATCH_METRICS_TAB, emqx_plugin_mongodb_batch_metrics).
-define(BATCH_CONFIG_TAB, emqx_plugin_mongodb_batch_config).
-define(BATCH_HISTORY_TAB, emqx_plugin_mongodb_batch_history).
-define(TOPIC_PRIORITY_TAB, emqx_plugin_mongodb_topic_priority).
-define(METRICS_WINDOW, 60). % 60秒窗口
-define(PREDICTION_INTERVAL, 5000). % 5秒

%% 并行处理管道相关宏
-define(PIPELINE_WORKERS, 4).
-define(PARSE_WORKERS, 2).
-define(TRANSFORM_WORKERS, 2).
-define(BATCH_WORKERS, 2).
-define(SEND_WORKERS, 2).
-define(QUEUE_SIZE_LIMIT, 10000).
-define(STAGE_TIMEOUT, 5000).
-define(PIPELINE_SUPERVISOR, emqx_plugin_mongodb_pipeline_sup).
-define(PIPELINE_REGISTRY, emqx_plugin_mongodb_pipeline_registry).

%% 高级熔断器相关宏
-define(BREAKER_REGISTRY, emqx_plugin_mongodb_circuit_breakers).
-define(DEFAULT_FAILURE_THRESHOLD, 5).
-define(DEFAULT_RESET_TIMEOUT, 30000). % 30秒
-define(DEFAULT_HALF_OPEN_RATIO, 0.1). % 10%
-define(DEFAULT_RECOVERY_STEP, 0.1). % 10%
-define(DEFAULT_RECOVERY_INTERVAL, 5000). % 5秒

%% 监控数据存储相关宏 - 用于熔断器高级监控
-define(RESPONSE_TIME_TAB, emqx_plugin_mongodb_response_times).
-define(CONCURRENT_TAB, emqx_plugin_mongodb_concurrent_requests).
-define(ERROR_RATE_TAB, emqx_plugin_mongodb_error_rates).
-define(DEFAULT_ERROR_THRESHOLD_RATIO, 0.8). % 80%
-define(CIRCUIT_BREAKER_CHECK_INTERVAL, 5000). % 5秒
-define(MAX_ERROR_HISTORY, 100).

%% 错误严重程度
-define(SEVERITY_CRITICAL, critical).
-define(SEVERITY_MAJOR, major).
-define(SEVERITY_MINOR, minor).
-define(SEVERITY_WARNING, warning).
-define(SEVERITY_INFO, info).

%% 错误类型
-define(ERROR_TYPE_CONNECTION, connection).
-define(ERROR_TYPE_AUTHENTICATION, authentication).
-define(ERROR_TYPE_TIMEOUT, timeout).
-define(ERROR_TYPE_QUERY, query).
-define(ERROR_TYPE_NETWORK, network).
-define(ERROR_TYPE_SYSTEM, system).
-define(ERROR_TYPE_UNKNOWN, unknown).

%% 异常处理增强相关宏
-define(ERROR_REGISTRY, emqx_plugin_mongodb_error_registry).
-define(ERROR_STATS, emqx_plugin_mongodb_error_stats).
-define(RECOVERY_INTERVAL, 5000). % 5秒
-define(FAULT_INJECTION_INTERVAL, 60000). % 1分钟

%% 资源管理优化相关宏
-define(RESOURCE_STATS, emqx_plugin_mongodb_resource_stats).
-define(RESOURCE_LIMITS, emqx_plugin_mongodb_resource_limits).
-define(MONITOR_INTERVAL, 5000). % 5秒
-define(DEGRADATION_CHECK_INTERVAL, 1000). % 1秒
-define(DEFAULT_MEMORY_LIMIT, 0.8). % 80%内存使用率
-define(DEFAULT_CPU_LIMIT, 0.9). % 90%CPU使用率
-define(DEFAULT_CONNECTION_LIMIT, 10000). % 最大连接数
-define(DEFAULT_QUEUE_LIMIT, 100000). % 最大队列长度
-define(DEGRADATION_LEVELS, [normal, light, moderate, severe, critical]).

%% 背压机制相关宏
-define(BACKPRESSURE_TAB, emqx_plugin_mongodb_backpressure).
-define(BACKPRESSURE_MONITOR_INTERVAL, 1000). % 1秒
-define(BACKPRESSURE_DEFAULT_QUEUE_THRESHOLD_MILD, 1000).
-define(BACKPRESSURE_DEFAULT_QUEUE_THRESHOLD_MODERATE, 3000).
-define(BACKPRESSURE_DEFAULT_QUEUE_THRESHOLD_HIGH, 5000).
-define(BACKPRESSURE_DEFAULT_QUEUE_THRESHOLD_CRITICAL, 8000).
-define(BACKPRESSURE_DEFAULT_TIME_THRESHOLD_MILD, 100). % 毫秒
-define(BACKPRESSURE_DEFAULT_TIME_THRESHOLD_MODERATE, 300). % 毫秒
-define(BACKPRESSURE_DEFAULT_TIME_THRESHOLD_HIGH, 500). % 毫秒
-define(BACKPRESSURE_DEFAULT_TIME_THRESHOLD_CRITICAL, 1000). % 毫秒
-define(BACKPRESSURE_PRIORITY_HIGH, high).
-define(BACKPRESSURE_PRIORITY_MEDIUM, medium).
-define(BACKPRESSURE_PRIORITY_LOW, low).
-define(BACKPRESSURE_LEVEL_NORMAL, normal).
-define(BACKPRESSURE_LEVEL_MILD, mild).
-define(BACKPRESSURE_LEVEL_MODERATE, moderate).
-define(BACKPRESSURE_LEVEL_HIGH, high).
-define(BACKPRESSURE_LEVEL_CRITICAL, critical).

%% 会话持久化相关宏定义
-define(SESSION_PERSISTENCE_ENABLED, session_persistence_enabled).
-define(SESSION_AUTO_RESTORE, session_auto_restore).
-define(SESSION_RESTORE_ON_STARTUP, session_restore_on_startup).
-define(SESSION_EXPIRY, session_expiry).
-define(MESSAGE_EXPIRY, message_expiry).
-define(CLEANUP_INTERVAL, cleanup_interval).
-define(CLEANUP_BATCH_SIZE, cleanup_batch_size).

%%--------------------------------------------------------------------
%% MongoDB集合名称统一定义
%%--------------------------------------------------------------------

%% 会话持久化默认集合名称
-define(DEFAULT_SESSION_COLLECTION, <<"emqx_mqtt_sessions">>).
-define(DEFAULT_WILL_MESSAGE_COLLECTION, <<"emqx_mqtt_will_messages">>).
-define(DEFAULT_SESSION_TAKEOVER_COLLECTION, <<"emqx_mqtt_session_takeovers">>).

%% 消息持久化默认集合名称
-define(DEFAULT_MESSAGE_COLLECTION, <<"emqx_mqtt_messages">>).
-define(DEFAULT_DATA_COLLECTION, <<"emqx_mqtt_data">>).

%% 订阅持久化默认集合名称
-define(DEFAULT_SUBSCRIPTION_COLLECTION, <<"emqx_mqtt_subscriptions">>).

%% Inflight消息持久化默认集合名称
-define(DEFAULT_INFLIGHT_COLLECTION, <<"emqx_mqtt_inflight_messages">>).

%% 包ID持久化默认集合名称
-define(DEFAULT_PACKET_ID_COLLECTION, <<"emqx_mqtt_packet_ids">>).

%% 保留消息持久化默认集合名称
-define(DEFAULT_RETAINED_MESSAGE_COLLECTION, <<"emqx_mqtt_retained_messages">>).

%% 系统和健康检查相关集合
-define(DEFAULT_HEALTH_CHECK_COLLECTION, <<"foo">>).

-endif.
