plugin_mongodb {
  // MongoDB连接配置
  // 版本兼容性：支持MongoDB 4.x及以上版本
  // 推荐版本：MongoDB 4.4+（生产环境）
  // 完全支持：MongoDB 5.x, 6.x, 7.x
  // 最低要求：MongoDB 4.0（有功能限制）
  connection {
    // MongoDB部署类型: 单节点(single)、分片集群(sharded)或副本集(rs)
    mongo_type = rs

    // 副本集名称 (当mongo_type为rs时必填)
    replica_set_name = rs

    // MongoDB服务器地址列表
    bootstrap_hosts = ["127.0.0.1:37017", "127.0.0.1:37018"]

    // 写入模式设置
    w_mode = unsafe

    // MongoDB数据库名称
    database = "energy_monitor"

    // 数据库访问凭证
    username = "em_rw"
    password = "c40QUX*Tvm"

    // 认证数据库 (默认为admin，如果用户在其他数据库中创建则需要指定)
    auth_source = "admin"

    // 读偏好设置: primary, primaryPreferred, secondary, secondaryPreferred, nearest
    read_preference = "primary"

    // 读关注级别: local, available, majority, linearizable, snapshot
    read_concern_level = "local"

    // 写关注级别: 0, 1, majority, 或自定义标签
    write_concern = "majority"

    // 写关注超时时间 (毫秒)
    write_concern_timeout = 10000

    // SSL连接配置
    ssl {
      // 是否启用SSL连接
      enable = false

      // SSL证书文件路径 (PEM格式)
      certfile = ""

      // SSL私钥文件路径 (PEM格式)
      keyfile = ""

      // CA证书文件路径 (PEM格式)
      cacertfile = ""

      // 是否验证服务器证书
      verify = verify_peer

      // 服务器名称指示 (SNI)
      server_name_indication = ""

      // SSL协议版本
      versions = ["tlsv1.2", "tlsv1.3"]

      // 加密套件
      ciphers = []

      // 证书验证深度
      depth = 10

      // 是否验证证书主机名
      verify_hostname = true

      // 自定义验证函数选项
      customize_hostname_check = []

      // SSL会话重用
      reuse_sessions = true

      // SSL握手超时时间
      handshake_timeout = "15s"

      // 是否启用部分链验证
      partial_chain = false

      // 证书撤销列表检查
      crl_check = false

      // 安全重新协商
      secure_renegotiate = true

      // 客户端证书密码
      password = ""
    }

    // 拓扑配置 - 连接池优化
    topology {
      // 连接池大小 - 增大以支持更多并发连接
      pool_size = 32
      // 连接池溢出限制 - 增大以应对流量峰值
      max_overflow = 64
      // 本地阈值延迟时间
      local_threshold_ms = 1s
      // 连接超时时间
      connect_timeout_ms = 20s
      // Socket超时时间 - 减小以更快检测连接问题
      socket_timeout_ms = 50ms
      // 服务器选择超时时间
      server_selection_timeout_ms = 30s
      // 等待队列超时时间
      wait_queue_timeout_ms = 1s
      // 心跳检测频率 - 增加以更快检测连接问题
      heartbeat_frequency_ms = 5s
      // 最小心跳检测频率
      min_heartbeat_frequency_ms = 1s
    }

    // 健康检查配置
    health_check {
      // 健康检查间隔时间 - 减小以更快检测问题
      interval = 15s
      // 健康检查超时时间
      timeout = 5s
      // 健康检查重试次数
      retry_count = 3
      // 健康检查缓存TTL
      cache_ttl = 10s
    }

    // 高级熔断器配置 - 多级熔断策略和渐进式恢复
    circuit_breaker {
      // 是否启用断路器
      enable = true
      // 连续失败阈值
      failure_threshold = 5
      // 断路器重置超时时间
      reset_timeout = 30s
      // 半开状态下允许通过的请求比例
      half_open_ratio = 0.1
      // 恢复步长 - 每次恢复增加的健康比率
      recovery_step = 0.1
      // 恢复间隔 - 半开状态下尝试恢复的时间间隔
      recovery_interval = 5s
      // 按维度熔断 - 支持按不同维度进行熔断控制
      dimension_breakers = [
        {
          // 维度类型: topic(主题), client_id(客户端ID), collection(集合)
          type = "topic"
          // 维度匹配模式
          pattern = "/edge/#"
          // 特定维度的失败阈值
          failure_threshold = 3
          // 特定维度的重置超时
          reset_timeout = 15s
        },
        {
          type = "collection"
          pattern = "emqx_mqtt_msg"
          failure_threshold = 8
          reset_timeout = 45s
        }
      ]
      // 错误类型阈值 - 不同类型错误的熔断阈值
      error_thresholds {
        // 超时错误阈值比例
        timeout = 0.6
        // 网络错误阈值比例
        network = 0.7
        // 认证错误阈值比例
        auth = 0.9
        // 查询错误阈值比例
        query = 0.8
        // 默认错误阈值比例
        default = 0.8
      }
    }

    // 异常处理增强配置
    error_handler {
      // 是否启用增强的错误处理
      enable = true
      // 错误分类与隔离配置
      error_classification {
        // 是否启用详细错误分类
        enable = true
        // 错误隔离级别: none(不隔离), partial(部分隔离), full(完全隔离)
        isolation_level = "partial"
      }
      // 故障注入测试配置
      fault_injection {
        // 是否启用故障注入
        enable = false
        // 故障注入概率 (0.0-1.0)
        probability = 0.05
        // 故障注入间隔 (秒)
        interval = 60
        // 要注入的错误类型
        error_types = ["timeout", "network", "query"]
      }
      // 自动恢复策略配置
      auto_recovery {
        // 是否启用自动恢复
        enable = true
        // 恢复检查间隔 (秒)
        check_interval = 5
        // 最大恢复尝试次数
        max_attempts = 3
        // 恢复策略:
        // - immediate: 立即尝试恢复
        // - exponential_backoff: 指数退避策略
        // - scheduled: 定时恢复
        strategy = "exponential_backoff"
      }
    }

    // 资源管理优化配置
    resource_manager {
      // 是否启用资源管理
      enable = true
      // 资源监控配置
      monitoring {
        // 是否启用资源监控
        enable = true
        // 监控间隔 (秒)
        interval = 5
        // 监控指标: memory(内存), cpu(CPU), connections(连接数), queue(队列长度)
        metrics = ["memory", "cpu", "connections", "queue"]
      }
      // 优雅降级策略配置
      graceful_degradation {
        // 是否启用优雅降级
        enable = true
        // 降级阈值配置
        thresholds {
          // 轻度降级阈值
          light {
            // 内存使用率阈值
            memory = 0.7
            // CPU使用率阈值
            cpu = 0.7
            // 连接数阈值 (相对于最大连接数的比例)
            connections = 0.7
            // 队列长度阈值 (相对于最大队列长度的比例)
            queue = 0.7
          }
          // 中度降级阈值
          moderate {
            memory = 0.8
            cpu = 0.8
            connections = 0.8
            queue = 0.8
          }
          // 严重降级阈值
          severe {
            memory = 0.9
            cpu = 0.9
            connections = 0.9
            queue = 0.9
          }
          // 临界降级阈值
          critical {
            memory = 0.95
            cpu = 0.95
            connections = 0.95
            queue = 0.95
          }
        }
        // 降级动作配置
        actions {
          // 轻度降级动作
          light = [
            {action = "batch_size", value = 0.8},
            {action = "queue_ttl", value = 0.8}
          ]
          // 中度降级动作
          moderate = [
            {action = "batch_size", value = 0.5},
            {action = "queue_ttl", value = 0.5},
            {action = "reject_low_priority", value = true}
          ]
          // 严重降级动作
          severe = [
            {action = "batch_size", value = 0.3},
            {action = "queue_ttl", value = 0.3},
            {action = "reject_low_priority", value = true},
            {action = "reject_medium_priority", value = true}
          ]
          // 临界降级动作
          critical = [
            {action = "batch_size", value = 0.1},
            {action = "queue_ttl", value = 0.1},
            {action = "reject_low_priority", value = true},
            {action = "reject_medium_priority", value = true},
            {action = "persist_only", value = true}
          ]
        }
      }
      // 动态资源分配配置
      dynamic_allocation {
        // 是否启用动态资源分配
        enable = true
        // 资源调整间隔 (秒)
        adjust_interval = 30
        // 连接池配置
        connection_pool {
          // 最小连接池大小
          min_size = 8
          // 最大连接池大小
          max_size = 64
          // 连接池大小调整步长
          step_size = 4
          // 连接池使用率阈值 (触发扩容的使用率)
          usage_threshold = 0.7
        }
      }
      // 资源限制配置
      limits {
        // 内存使用率限制 (0.0-1.0)
        memory_limit = 0.8
        // CPU使用率限制 (0.0-1.0)
        cpu_limit = 0.9
        // 最大连接数限制
        connection_limit = 10000
        // 最大队列长度限制
        queue_limit = 100000
      }
    }

    // 批处理配置 - 智能批处理增强
    batch {
      // 是否启用批处理
      enable = true
      // 最小批处理大小
      min_size = 100
      // 最大批处理大小
      max_size = 5000
      // 批处理超时时间
      timeout = 100ms
      // 最大批处理队列大小
      max_queue_size = 10000
      // 是否启用自适应批处理
      adaptive = true
      // 批处理大小调整间隔
      adjust_interval = 30s
      // 批处理预测窗口大小
      prediction_window = 60s
    }

    // 流水线配置 - 并行处理管道
    pipeline {
      // 是否启用流水线处理
      enable = true
      // 解析阶段工作进程数
      parse_workers = 4
      // 转换阶段工作进程数
      transform_workers = 4
      // 批处理阶段工作进程数
      batch_workers = 2
      // 发送阶段工作进程数
      send_workers = 4
      // 流水线队列大小
      queue_size = 10000
      // 阶段处理超时时间
      stage_timeout = 5s
    }

    // 连接池分片配置
    connection_pool {
      // 是否启用连接池分片
      enable = true
      // 连接池分片数量
      shard_count = 8
      // 是否启用连接预热
      preload = true
      // 预热连接比例 (0.0-1.0)
      preload_ratio = 0.5
      // 是否启用健康度感知路由
      health_aware_routing = true
    }

    // 缓存配置
    cache {
      // 是否启用缓存
      enable = true
      // 主题匹配缓存大小
      topic_match_size = 2000
      // 主题匹配缓存TTL
      topic_match_ttl = 60s
      // 健康检查缓存TTL
      health_check_ttl = 10s
    }

    // 指标配置
    metrics {
      // 是否启用指标
      enable = true
      // 是否启用Prometheus导出
      prometheus_export = true
      // 指标收集间隔
      collection_interval = 15s
    }

    // 背压机制配置
    backpressure {
      // 是否启用背压机制
      enable = true

      // 队列长度阈值
      queue_thresholds {
        // 轻度背压队列长度阈值
        mild = 1000
        // 中度背压队列长度阈值
        moderate = 3000
        // 高度背压队列长度阈值
        high = 5000
        // 临界背压队列长度阈值
        critical = 8000
      }

      // 处理时间阈值 (毫秒)
      time_thresholds {
        // 轻度背压处理时间阈值
        mild = 100
        // 中度背压处理时间阈值
        moderate = 300
        // 高度背压处理时间阈值
        high = 500
        // 临界背压处理时间阈值
        critical = 1000
      }

      // 系统负载阈值
      load_thresholds {
        // 轻度背压系统负载阈值
        mild = 0.6
        // 中度背压系统负载阈值
        moderate = 0.7
        // 高度背压系统负载阈值
        high = 0.8
        // 临界背压系统负载阈值
        critical = 0.9
      }

      // 背压动作配置
      actions {
        // 轻度背压动作
        mild = [
          {throttle_rate = 0.9}
          {batch_size = 0.8}
        ]

        // 中度背压动作
        moderate = [
          {throttle_rate = 0.7}
          {batch_size = 0.6}
          {reject_low_priority = true}
        ]

        // 高度背压动作
        high = [
          {throttle_rate = 0.4}
          {batch_size = 0.3}
          {reject_low_priority = true}
          {reject_medium_priority = true}
        ]

        // 临界背压动作
        critical = [
          {throttle_rate = 0.1}
          {batch_size = 0.1}
          {reject_low_priority = true}
          {reject_medium_priority = true}
          {persist_only = true}
        ]
      }
    }
  }

  // 持久化配置 - 实现RocksDB类似功能
  // 会话持久化配置
  session_persistence {
    ## 是否启用会话持久化
    enabled = true

    ## 断开连接时是否自动恢复会话
    auto_restore = true

    ## 启动时是否恢复所有会话
    restore_on_startup = true

    ## 启动恢复延迟时间
    restore_delay = "5s"

    ## 最大并发恢复会话数
    max_concurrent_restores = 10

    ## 会话过期时间
    session_expiry = "2h"

    ## 定期清理过期数据间隔
    cleanup_interval = "5m"

    ## 清理批次大小
    cleanup_batch_size = 100

    ## 恢复批次大小 (EMQX重启后从MongoDB恢复会话时的批次大小)
    restore_batch_size = 500

    ## 断电恢复功能
    crash_recovery = true
    crash_recovery_dir = "data"
    force_restore_on_crash = true

    // MongoDB集合配置
    collection = "emqx_mqtt_sessions"
  }

  // 订阅持久化配置
  subscription_persistence {
    // 是否启用订阅持久化
    enabled = true

    // 启动时是否恢复所有订阅
    restore_on_startup = true

    // 是否自动恢复订阅(客户端重连时)
    auto_restore = true

    // 订阅过期时间
    subscription_expiry = "2d"

    // 定期清理过期订阅间隔
    cleanup_interval = "30m"

    // 清理批次大小
    cleanup_batch_size = 200

    // 最大存储订阅数量(每个客户端)
    max_subscriptions_per_client = 100

    // MongoDB集合配置
    collection = "emqx_mqtt_subscriptions"

    // 是否保存共享订阅
    save_shared_subscriptions = true

    ## 共享订阅配置
    shared_subscription {
      ## 分发策略: random | round_robin | hash | sticky
      strategy = random

      ## 共享订阅哈希键（当策略为hash时使用）
      ## 可选值：clientid | username | topic
      hash_key = clientid

      ## 共享订阅会话恢复优先级，数字越大优先级越高
      priority = 10

      ## 是否持久化共享订阅
      persistent = true

      ## 共享订阅消息派发时，如果所有成员都不在线，是否将消息保存为离线消息
      save_messages_for_offline = true

      ## 共享订阅会话过期时间（如果所有订阅者都长期不在线）
      expiry = "24h"
    }
  }

  // 消息持久化配置
  message_persistence {
    // 是否启用消息持久化
    enabled = true

    // 消息保留策略
    retain_policy = ["qos1", "qos2"]

    // 消息过期时间
    message_expiry = "4h"

    // 定期清理过期消息间隔
    cleanup_interval = "15m"

    // 清理批次大小
    cleanup_batch_size = 500

    // 恢复批次大小 (EMQX重启后从MongoDB恢复消息时的批次大小)
    restore_batch_size = 500

    // 最大恢复批次数 (防止内存溢出)
    restore_max_batches = 100

    // 最大存储消息数量(每个客户端)
    max_messages_per_client = 1000

    // 是否存储消息属性
    store_message_properties = true

    // 是否压缩消息内容
    compress_payload = false

    // 是否允许大于指定大小的消息存储
    large_message_threshold = "64KB"

    // 是否存储保留消息
    store_retained_messages = false

    // MongoDB集合配置
    collection = "emqx_mqtt_messages"
  }

  // 包ID持久化配置
  packet_id_persistence {
    ## 是否启用包ID持久化
    enabled = true

    ## 包ID过期时间
    packet_id_expiry = "1h"

    ## 定期清理过期包ID间隔
    cleanup_interval = "10m"

    // MongoDB集合配置
    collection = "emqx_mqtt_packet_ids"
  }

  // 遗嘱消息持久化配置
  will_persistence {
    // 是否启用遗嘱消息持久化
    enabled = true

    // 遗嘱消息过期时间
    will_expiry = "4h"

    // 定期清理过期遗嘱消息间隔
    cleanup_interval = "15m"

    // 清理批次大小
    cleanup_batch_size = 500

    // 是否存储消息属性
    store_message_properties = true

    // 是否压缩消息内容
    compress_payload = false

    // 是否允许大于指定大小的消息存储
    large_message_threshold = "64KB"

    // MongoDB集合配置
    collection = "emqx_mqtt_will_messages"
  }

  // 保留消息持久化配置
  retained_persistence {
    // 是否启用保留消息持久化
    enabled = true

    // 保留消息过期时间
    retained_expiry = "7d"

    // 定期清理过期保留消息间隔
    cleanup_interval = "1h"

    // 清理批次大小
    cleanup_batch_size = 1000

    // 最大保留消息数量
    max_retained_messages = 100000

    // 是否存储消息属性
    store_message_properties = true

    // 是否压缩消息内容
    compress_payload = false

    // 是否允许大于指定大小的消息存储
    large_message_threshold = "64KB"


    // 是否启用主题过滤器（如果为false，则保留所有保留消息）
    enable_topic_filters = false

    // 保留消息主题过滤器 - 只有匹配的主题才会被保留
    topic_filters = [
      "sensor/+/status",
      "device/+/config",
      "system/+/info"
    ]

    // 保留消息发送配置
    delivery {
      // 订阅时是否立即发送保留消息
      send_on_subscribe = true

      // 发送保留消息的最大并发数
      max_concurrent_sends = 10

      // 发送超时时间
      send_timeout = "5s"

      // 是否按QoS级别发送
      respect_qos = true
    }

    // MongoDB集合配置
    collection = "emqx_mqtt_retained_messages"

    // 索引配置
    indexes {
      // 是否自动创建索引
      auto_create = true

      // TTL索引配置
      ttl_index {
        // 是否启用TTL索引
        enabled = true
        // TTL字段名
        field = "expires_at"
      }

      // 主题索引配置
      topic_index {
        // 是否启用主题索引
        enabled = true
        // 是否为唯一索引
        unique = true
      }

      // 复合索引配置
      compound_indexes = [
        {
          // 发布时间和主题的复合索引
          fields = ["published_at", "topic"]
          name = "rtm_published_at_topic_index"
        },
        {
          // 发布者和主题的复合索引
          fields = ["publisher_id", "topic"]
          name = "rtm_publisher_topic_index"
        }
      ]
    }
  }

  // Inflight消息持久化配置 (QoS 1/2未确认消息状态管理)
  inflight_persistence {
    // 是否启用Inflight消息持久化
    enabled = true

    // Inflight消息过期时间 (超过此时间的未确认消息将被清理)
    inflight_expiry = "4h"

    // 消息重试间隔
    retry_interval = "30s"

    // 最大重试次数
    max_retries = 3

    // 定期重试检查间隔
    retry_check_interval = "1m"

    // 定期清理过期Inflight消息间隔
    cleanup_interval = "10m"

    // 清理批次大小
    cleanup_batch_size = 1000

    // 是否存储消息属性
    store_message_properties = true

    // 是否压缩消息内容
    compress_payload = false

    // 是否允许大于指定大小的消息存储
    large_message_threshold = "64KB"

    // MongoDB集合配置
    collection = "emqx_mqtt_inflight_messages"

    // 索引配置
    indexes {
      // 是否自动创建索引
      auto_create = true

      // TTL索引配置
      ttl_index {
        // 是否启用TTL索引
        enabled = true
        // TTL字段名
        field = "expires_at"
      }

      // 复合索引配置
      compound_indexes = [
        {
          // 客户端ID和包ID的复合索引
          fields = ["client_id", "packet_id"]
          name = "ifl_client_id_packet_id_index"
        },
        {
          // 状态和重试时间的复合索引
          fields = ["state", "next_retry_at"]
          name = "ifl_state_retry_index"
        }
      ]
    }
  }

  // 主题过滤功能
  topics = [
    {
      // Emqx topic pattern.
      // 1. Cannot match the system message;
      // 2. Cannot use filters that start with '+' or '#'.
      // MQTT主题过滤器,不能匹配系统消息,不能以'+'或'#'开头
      filter = "/edge/#"
      // Unique
      // 唯一的主题名称标识
      name = "emqx_topic"
      // MongoDB集合名称
      collection = "emqx_mqtt_data"
    }
  ]
}
