apps_dirs:
  - "src/*"
deps_dirs:
  - "_build/default/lib/*"
include_dirs:
  - "_build/default/lib/*/include"
exclude_unused_includes:
  - "typerefl/include/types.hrl"
  - "logger.hrl"
diagnostics:
  enabled:
    - bound_var_in_pattern
    - elvis
    - unused_includes
    - unused_macros
    - crossref
    # - dialyzer
    - compiler
  disabled:
    - dialyzer
    # - crossref
    # - compiler
lenses:
  disabled:
    # - show-behaviour-usages
    # - ct-run-test
    - server-info
  enable:
    - show-behaviour-usages
    - ct-run-test
macros:
  - name: EMQX_RELEASE_EDITION
    value: ce
code_reload:
  node: emqx@127.0.0.1
