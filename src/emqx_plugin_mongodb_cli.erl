%%%-------------------------------------------------------------------
%%% @doc MongoDB插件命令行接口模块
%%%
%%% 这个模块实现了MongoDB插件的命令行接口（CLI），用于管理和监控插件
%%% 通过EMQX的emqx_ctl工具提供用户友好的命令行操作界面
%%%
%%% 功能特性：
%%% 1. 配置重载：支持热重载插件配置，无需重启EMQX
%%% 2. 配置清理：提供配置文件清理和备份功能
%%% 3. 帮助信息：显示可用命令和使用说明
%%% 4. 错误处理：提供友好的错误信息和状态反馈
%%%
%%% Java等价概念：
%%% - 类似于Spring Boot Actuator的管理端点
%%% - 类似于Apache Commons CLI的命令行处理
%%% - 类似于JMX MBean的管理接口
%%% - 类似于Maven/Gradle的命令行插件
%%%
%%% 使用方式：
%%% 在EMQX控制台中执行：
%%% - emqx_ctl emqx_plugin_mongodb reload        # 重载配置
%%% - emqx_ctl emqx_plugin_mongodb clean_config  # 清理配置
%%% - emqx_ctl emqx_plugin_mongodb help          # 显示帮助
%%%
%%% 架构设计：
%%% 采用命令模式（Command Pattern），每个命令对应一个处理分支
%%% 使用模式匹配进行命令分发，类似于Java中的switch语句
%%%
%%% @end
%%%-------------------------------------------------------------------
-module(emqx_plugin_mongodb_cli).

%% API导出函数
%% 这个模块只导出一个公共函数，作为CLI命令的统一入口点
%% 类似于Java中的main方法或命令处理器的execute方法
-export([
    cmd/1    % 命令处理入口函数，接收命令参数列表并执行相应操作
]).

%% @doc 处理"reload"命令 - 热重载插件配置
%% 这个函数处理配置重载命令，实现插件配置的热更新功能
%%
%% 功能说明：
%% 1. 重新读取配置文件（emqx_plugin_mongodb.hocon）
%% 2. 验证新配置的有效性
%% 3. 应用新配置到运行中的插件实例
%% 4. 更新连接池、阈值、超时等运行时参数
%% 5. 保持现有连接和会话状态不受影响
%%
%% 命令格式：
%% emqx_ctl emqx_plugin_mongodb reload
%%
%% 使用场景：
%% - 调整MongoDB连接参数（主机、端口、认证信息）
%% - 修改批处理大小和超时设置
%% - 更新熔断器和背压阈值
%% - 启用或禁用某些功能模块
%%
%% Java等价概念：
%% 类似于Spring Boot的@RefreshScope注解或Actuator的/refresh端点
%% 或者JMX MBean的配置更新方法
%%
%% 注意事项：
%% - 重载过程中不会中断现有的MQTT连接
%% - 配置验证失败时会保持原有配置
%% - 某些配置项可能需要重启才能生效
cmd(["reload"]) ->
    %% 调用核心插件模块的reload/0函数来重新加载配置
    %% emqx_plugin_mongodb:reload/0会执行以下操作：
    %% 1. 读取最新的配置文件
    %% 2. 验证配置参数的有效性
    %% 3. 更新运行时配置
    %% 4. 重新初始化必要的组件
    %%
    %% 在Java中相当于：
    %% @RefreshScope
    %% @Component
    %% public class MongoDBPluginConfig {
    %%     @EventListener(RefreshEvent.class)
    %%     public void reload() { ... }
    %% }
    emqx_plugin_mongodb:reload(),

    %% 向控制台输出成功信息
    %% emqx_ctl:print/1类似于Java中的System.out.println()
    %% 但它是专门为EMQX控制台设计的输出函数
    %% 支持格式化输出和多语言显示
    emqx_ctl:print("MongoDB plugin configuration reloaded completely.\n");

%% @doc 处理"clean_config"命令 - 清理和备份配置文件
%% 这个函数处理配置清理命令，提供配置文件的安全清理和备份功能
%%
%% 功能说明：
%% 1. 创建当前配置文件的时间戳备份
%% 2. 清理或重置配置文件到默认状态
%% 3. 验证清理操作的完整性
%% 4. 提供操作结果的详细反馈
%%
%% 命令格式：
%% emqx_ctl emqx_plugin_mongodb clean_config
%%
%% 使用场景：
%% - 配置文件损坏需要重置
%% - 清理测试环境的配置
%% - 恢复到默认配置状态
%% - 解决配置冲突问题
%%
%% 安全特性：
%% - 自动创建备份文件（带时间戳）
%% - 操作前验证文件权限
%% - 支持操作回滚
%%
%% Java等价概念：
%% 类似于Spring Boot的配置重置功能
%% 或者Maven的clean插件
%%
%% 注意事项：
%% - 操作会影响插件的运行配置
%% - 建议在维护窗口期间执行
%% - 清理后可能需要重新配置插件
cmd(["clean_config"]) ->
    %% 调用插件模块的clean_config/0函数来清理配置文件
    %% 这个函数会执行以下操作：
    %% 1. 检查配置文件是否存在
    %% 2. 创建带时间戳的备份文件
    %% 3. 清理或删除原配置文件
    %% 4. 可选地创建默认配置文件
    %%
    %% 在Java中相当于：
    %% public class ConfigurationManager {
    %%     public Result cleanConfig() {
    %%         try {
    %%             backupCurrentConfig();
    %%             cleanConfigFile();
    %%             return Result.success();
    %%         } catch (Exception e) {
    %%             return Result.error(e.getMessage());
    %%         }
    %%     }
    %% }
    case emqx_plugin_mongodb:clean_config() of
        ok ->
            %% 清理成功，输出成功信息
            %% 告知用户配置已被清理并创建了备份
            emqx_ctl:print("MongoDB plugin configuration file has been cleaned up and backed up.\n");
        {error, Reason} ->
            %% 清理失败，输出错误信息
            %% ~p是Erlang的格式化占位符，类似于Java的%s
            %% [Reason]是参数列表，对应~p占位符
            %%
            %% 在Java中相当于：
            %% System.err.printf("Failed to clean configuration: %s%n", reason);
            emqx_ctl:print("Failed to clean MongoDB plugin configuration file: ~p\n", [Reason])
    end;

%% @doc 处理未知命令或显示帮助信息
%% 这是一个通配符函数，处理所有未匹配的命令参数
%%
%% 功能说明：
%% 1. 捕获所有未识别的命令参数
%% 2. 显示完整的命令使用帮助
%% 3. 提供每个命令的详细说明
%% 4. 指导用户正确使用CLI接口
%%
%% 触发条件：
%% - 用户输入了不存在的命令
%% - 用户请求帮助信息（如help、-h、--help）
%% - 用户输入了错误的命令格式
%%
%% Java等价概念：
%% 类似于Apache Commons CLI的HelpFormatter
%% 或者Spring Shell的help命令
%%
%% 设计模式：
%% 使用了默认处理器模式（Default Handler Pattern）
%% 通过模式匹配的通配符_来捕获所有未处理的情况
cmd(_) ->
    %% 显示命令使用帮助信息
    %% emqx_ctl:usage/1接受一个命令描述列表
    %% 每个元素是一个{Command, Description}元组
    %%
    %% 在Java中相当于：
    %% public class HelpFormatter {
    %%     public void printHelp(List<CommandInfo> commands) {
    %%         for (CommandInfo cmd : commands) {
    %%             System.out.printf("%-30s %s%n", cmd.getName(), cmd.getDescription());
    %%         }
    %%     }
    %% }
    emqx_ctl:usage([
        %% reload命令的帮助信息
        %% 提供配置热重载功能，无需重启EMQX服务
        {"emqx_plugin_mongodb reload", "Reload MongoDB plugin configuration"},

        %% clean_config命令的帮助信息
        %% 提供配置清理功能，包含安全备份机制
        %% 注意：这里提到了插件卸载时会自动删除配置，这是重要的使用提示
        {"emqx_plugin_mongodb clean_config", "Manually clean up MongoDB plugin configuration file (creates a backup first). Note: Configuration is automatically deleted when unloading the plugin."}
    ]).
