%%%-------------------------------------------------------------------
%%% @doc MongoDB事务管理模块 - 企业级事务处理和重试机制
%%% 这个模块是MongoDB事务处理的核心组件，提供完整的事务管理能力
%%%
%%% 功能概述：
%%% 1. 事务管理 - 提供完整的MongoDB事务开始、提交、回滚机制
%%% 2. 错误重试 - 智能的错误重试机制，支持指数退避算法
%%% 3. 异常处理 - 完善的异常捕获和处理机制
%%% 4. 错误分类 - 智能的错误分类，区分可重试和不可重试错误
%%% 5. 性能优化 - 优化的重试策略，避免系统过载
%%% 6. 日志记录 - 详细的事务和重试日志记录
%%%
%%% MongoDB事务机制：
%%% - ACID特性：原子性、一致性、隔离性、持久性
%%% - 多文档事务：支持跨多个集合的事务操作
%%% - 读写关注：支持配置读写关注级别
%%% - 事务隔离：支持不同的事务隔离级别
%%%
%%% 架构设计：
%%% - 函数式编程：使用高阶函数封装事务逻辑
%%% - 错误恢复：智能的错误分类和重试策略
%%% - 资源管理：自动的事务资源清理和释放
%%% - 性能监控：事务执行时间和重试次数监控
%%%
%%% Java等价概念：
%%% 类似于Spring Boot中的事务管理：
%%% @Service
%%% @Transactional
%%% public class TransactionService {
%%%     @Autowired private MongoTransactionManager transactionManager;
%%%
%%%     @Transactional(rollbackFor = Exception.class)
%%%     public <T> T executeInTransaction(Supplier<T> operation) {
%%%         return operation.get();
%%%     }
%%%
%%%     @Retryable(value = {TransientDataAccessException.class},
%%%                maxAttempts = 3,
%%%                backoff = @Backoff(delay = 100, multiplier = 2))
%%%     public <T> T executeWithRetry(Supplier<T> operation) {
%%%         return operation.get();
%%%     }
%%% }
%%%
%%% 设计模式：
%%% - 模板方法模式：统一的事务处理模板
%%% - 策略模式：不同的重试策略
%%% - 装饰器模式：为操作添加事务和重试能力
%%% - 责任链模式：错误处理和重试链
%%% @end
%%%-------------------------------------------------------------------

-module(emqx_plugin_mongodb_transaction).

-include("emqx_plugin_mongodb.hrl").

%% ============================================================================
%% 事务管理和重试机制API - 核心事务处理接口
%% 这些函数提供完整的事务管理和错误重试能力
%% 类似于Java中的事务管理器和重试框架
%% ============================================================================
-export([
    with_transaction/2,         % 在事务中执行操作
                               % 功能：在MongoDB事务中执行指定的操作函数
                               % Java等价：@Transactional public <T> T executeInTransaction(Supplier<T> operation)

    with_retry/2,              % 带重试的操作执行（使用默认重试次数）
                               % 功能：执行操作，失败时自动重试
                               % Java等价：@Retryable public <T> T executeWithRetry(Supplier<T> operation)

    with_retry/3,              % 带重试的操作执行（指定重试次数）
                               % 功能：执行操作，失败时按指定次数重试
                               % Java等价：@Retryable(maxAttempts = n) public <T> T executeWithRetry(Supplier<T> operation, int maxAttempts)

    retry_with_backoff/2,      % 带指数退避的重试（使用默认参数）
                               % 功能：执行操作，失败时使用指数退避算法重试
                               % Java等价：@Retryable(backoff = @Backoff(delay = 100, multiplier = 2))

    retry_with_backoff/3,      % 带指数退避的重试（指定参数）
                               % 功能：执行操作，失败时使用自定义指数退避算法重试
                               % Java等价：@Retryable(backoff = @Backoff(delay = initialDelay, multiplier = multiplier, maxDelay = maxDelay))

    classify_error/1           % 错误分类
                               % 功能：分析错误类型，判断是否可以重试
                               % Java等价：public boolean isRetryableException(Exception ex)
]).

%% ============================================================================
%% 配置常量定义 - 事务和重试的核心配置参数
%% 这些常量控制事务处理和重试机制的行为
%% 类似于Java中的配置常量或@ConfigurationProperties
%% ============================================================================

%% 默认最大重试次数：3次
%% 功能：控制操作失败时的最大重试次数
%% 平衡系统稳定性和性能，避免无限重试
%% Java等价：@Value("${transaction.retry.max-attempts:3}")
%% 或者：@Retryable(maxAttempts = 3)
-define(DEFAULT_MAX_RETRIES, 3).

%% 默认初始延迟：100毫秒
%% 功能：控制第一次重试前的等待时间
%% 给系统一定的恢复时间，避免立即重试
%% Java等价：@Value("${transaction.retry.initial-delay:100}")
%% 或者：@Backoff(delay = 100)
-define(DEFAULT_INITIAL_DELAY, 100).

%% 默认最大延迟：5000毫秒（5秒）
%% 功能：控制指数退避算法的最大延迟时间
%% 防止延迟时间过长，影响用户体验
%% Java等价：@Value("${transaction.retry.max-delay:5000}")
%% 或者：@Backoff(maxDelay = 5000)
-define(DEFAULT_MAX_DELAY, 5000).

%%%===================================================================
%%% 核心API实现 - 事务管理和重试机制的具体实现
%%%===================================================================

%% @doc 在MongoDB事务中执行操作函数
%% 这个函数提供完整的事务管理能力，确保操作的ACID特性
%%
%% 功能说明：
%% 1. 开始MongoDB事务
%% 2. 在事务上下文中执行指定的操作函数
%% 3. 根据操作结果自动提交或回滚事务
%% 4. 处理事务过程中的各种异常情况
%% 5. 提供详细的日志记录和错误信息
%%
%% 参数说明：
%% - Fun: 要在事务中执行的函数，第一个参数会是事务ID
%% - Args: 传递给函数的其他参数列表
%%
%% 返回值：
%% - {ok, Value}: 事务成功提交，返回操作结果
%% - {error, Reason}: 事务失败，返回错误原因
%%
%% Java等价概念：
%% @Transactional(rollbackFor = Exception.class)
%% public <T> T executeInTransaction(Function<String, T> operation) {
%%     TransactionStatus status = transactionManager.getTransaction(new DefaultTransactionDefinition());
%%     try {
%%         T result = operation.apply(getCurrentTransactionId());
%%         transactionManager.commit(status);
%%         return result;
%%     } catch (Exception e) {
%%         transactionManager.rollback(status);
%%         throw e;
%%     }
%% }
%%
%% 事务处理流程：
%% 1. 开始事务 -> 2. 执行操作 -> 3. 提交/回滚 -> 4. 清理资源
-spec with_transaction(function(), list()) -> {ok, term()} | {error, term()}.
with_transaction(Fun, Args) ->
    try
        %% 第一步：开始MongoDB事务
        %% 向MongoDB资源管理器发送开始事务请求
        %% 在Java中相当于：
        %% TransactionStatus status = transactionManager.getTransaction(new DefaultTransactionDefinition());
        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID, {start_transaction, []}) of
            {ok, TxnId} ->
                %% 事务开始成功，获得事务ID
                %% 现在在事务上下文中执行操作
                try
                    %% 第二步：在事务中执行用户提供的操作函数
                    %% 将事务ID作为第一个参数传递给函数
                    %% 在Java中相当于：
                    %% T result = operation.apply(getCurrentTransactionId());
                    Result = apply(Fun, [TxnId | Args]),

                    %% 第三步：根据操作结果决定提交或回滚事务
                    case Result of
                        {ok, Value} ->
                            %% 操作成功，提交事务
                            %% 在Java中相当于：
                            %% transactionManager.commit(status);
                            case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID, {commit_transaction, TxnId}) of
                                {ok, _} ->
                                    %% 事务提交成功，返回操作结果
                                    {ok, Value};
                                Error ->
                                    %% 事务提交失败，记录错误日志
                                    ?SLOG(error, #{msg => "transaction_commit_failed", error => Error}),
                                    {error, {commit_failed, Error}}
                            end;
                        {error, Reason} = Error ->
                            %% 操作失败，回滚事务
                            %% 在Java中相当于：
                            %% transactionManager.rollback(status);
                            _ = emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID, {abort_transaction, TxnId}),
                            ?SLOG(warning, #{msg => "transaction_aborted", reason => Reason}),
                            Error
                    end
                catch
                    E1:R1:S1 ->
                        %% 第四步：处理事务执行过程中的异常
                        %% 发生异常时自动回滚事务
                        %% 在Java中相当于：
                        %% catch (Exception e) { transactionManager.rollback(status); throw e; }
                        _ = emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID, {abort_transaction, TxnId}),
                        ?SLOG(error, #{
                            msg => "transaction_exception",
                            error => E1,
                            reason => R1,
                            stacktrace => S1
                        }),
                        {error, {transaction_exception, {E1, R1}}}
                end;
            Error ->
                %% 事务开始失败，记录错误并返回
                ?SLOG(error, #{msg => "start_transaction_failed", error => Error}),
                {error, {start_transaction_failed, Error}}
        end
    catch
        E2:R2:S2 ->
            %% 处理外层异常（如资源不可用等）
            %% 这些异常通常表示系统级问题
            ?SLOG(error, #{
                msg => "transaction_outer_exception",
                error => E2,
                reason => R2,
                stacktrace => S2
            }),
            {error, {transaction_outer_exception, {E2, R2}}}
    end.

%% @doc 带重试机制的函数执行（使用默认重试次数）
%% 这个函数为操作提供自动重试能力，提高系统的容错性
%%
%% 功能说明：
%% 1. 执行指定的操作函数
%% 2. 如果操作失败，根据错误类型决定是否重试
%% 3. 使用默认的最大重试次数（3次）
%% 4. 智能的错误分类和重试策略
%%
%% Java等价概念：
%% @Retryable(value = {TransientDataAccessException.class}, maxAttempts = 3)
%% public <T> T executeWithRetry(Supplier<T> operation) {
%%     return operation.get();
%% }
-spec with_retry(function(), list()) -> {ok, term()} | {error, term()}.
with_retry(Fun, Args) ->
    %% 使用默认的最大重试次数
    with_retry(Fun, Args, ?DEFAULT_MAX_RETRIES).

%% @doc 带重试机制的函数执行（指定最大重试次数）
%% 允许用户自定义最大重试次数，提供更灵活的重试控制
%%
%% 参数说明：
%% - Fun: 要执行的函数
%% - Args: 函数参数列表
%% - MaxRetries: 最大重试次数
%%
%% Java等价概念：
%% @Retryable(maxAttempts = maxRetries)
%% public <T> T executeWithRetry(Supplier<T> operation, int maxRetries) {
%%     return operation.get();
%% }
-spec with_retry(function(), list(), non_neg_integer()) -> {ok, term()} | {error, term()}.
with_retry(Fun, Args, MaxRetries) ->
    %% 从第1次尝试开始
    with_retry(Fun, Args, MaxRetries, 1).

%% @doc 内部重试实现 - 递归重试逻辑的核心实现
%% 这个函数实现了智能的重试机制，包括错误分类、延迟计算等
%%
%% 参数说明：
%% - Fun: 要执行的函数
%% - Args: 函数参数列表
%% - MaxRetries: 剩余重试次数
%% - Attempt: 当前尝试次数
%%
%% 重试策略：
%% 1. 错误分类：区分临时错误、永久错误和未知错误
%% 2. 智能重试：只对临时错误和未知错误进行重试
%% 3. 指数退避：使用指数退避算法计算重试延迟
%% 4. 详细日志：记录每次重试的详细信息
with_retry(_Fun, _Args, 0, _Attempt) ->
    %% 重试次数用完，返回失败
    {error, max_retries_reached};
with_retry(Fun, Args, MaxRetries, Attempt) ->
    try
        %% 执行操作函数
        %% 支持无参数函数和有参数函数两种调用方式
        Result = case Args of
            [] -> Fun();                    % 无参数函数直接调用
            _ -> apply(Fun, Args)          % 有参数函数使用apply调用
        end,

        %% 分析操作结果，决定是否需要重试
        case Result of
            {ok, _} = Success ->
                %% 操作成功，返回结果
                %% 注意：这里将{ok, _}包装为{ok, {ok, _}}，保持一致的返回格式
                {ok, Success};
            {async_return, ok} ->
                %% 异步操作成功
                {ok, async_success};
            {async_return, {ok, _}} = AsyncSuccess ->
                %% 异步操作成功，返回结果
                AsyncSuccess;
            {async_return, {error, Reason}} ->
                %% 异步操作失败
                {error, Reason};
            {error, Reason} = Error ->
                %% 操作失败，进行错误分类和重试决策
                %% 在Java中相当于：
                %% if (isRetryableException(exception)) { retry(); } else { throw exception; }
                case classify_error(Reason) of
                    temporary ->
                        %% 临时错误（如网络超时、连接断开等），可以重试
                        %% 在Java中相当于：@Retryable(value = {TransientDataAccessException.class})
                        ?SLOG(warning, #{
                            msg => "retryable_error",
                            reason => Reason,
                            attempt => Attempt,
                            retries_left => MaxRetries - 1
                        }),
                        %% 计算指数退避延迟时间
                        Delay1 = calculate_backoff_delay(Attempt),
                        timer:sleep(Delay1),
                        %% 递归重试，重试次数减1，尝试次数加1
                        with_retry(Fun, Args, MaxRetries - 1, Attempt + 1);
                    permanent ->
                        %% 永久错误（如参数错误、权限不足等），不应重试
                        %% 在Java中相当于：非@Retryable注解指定的异常类型
                        ?SLOG(error, #{
                            msg => "permanent_error_no_retry",
                            reason => Reason
                        }),
                        Error;
                    unknown ->
                        %% 未知错误，保守策略：进行重试
                        %% 在实际生产环境中，未知错误可能是临时的
                        ?SLOG(warning, #{
                            msg => "unknown_error_retrying",
                            reason => Reason,
                            attempt => Attempt,
                            retries_left => MaxRetries - 1
                        }),
                        %% 计算指数退避延迟时间
                        Delay2 = calculate_backoff_delay(Attempt),
                        timer:sleep(Delay2),
                        %% 递归重试
                        with_retry(Fun, Args, MaxRetries - 1, Attempt + 1)
                end
        end
    catch
        E3:R3:S3 ->
            %% 捕获执行过程中的异常
            %% 记录详细的异常信息，包括堆栈跟踪
            ?SLOG(error, #{
                msg => "retry_exception",
                error => E3,
                reason => R3,
                stacktrace => S3,
                attempt => Attempt,
                retries_left => MaxRetries - 1
            }),
            % 发生异常，重试
            Delay3 = calculate_backoff_delay(Attempt),
            timer:sleep(Delay3),
            with_retry(Fun, Args, MaxRetries - 1, Attempt + 1)
    end.

%% @doc 使用指数退避算法的重试
-spec retry_with_backoff(function(), non_neg_integer()) -> {ok, term()} | {error, term()}.
retry_with_backoff(Fun, MaxRetries) ->
    retry_with_backoff(Fun, MaxRetries, ?DEFAULT_INITIAL_DELAY).

%% @doc 使用指数退避算法的重试(指定初始延迟)
-spec retry_with_backoff(function(), non_neg_integer(), non_neg_integer()) -> {ok, term()} | {error, term()}.
retry_with_backoff(Fun, MaxRetries, InitialDelay) ->
    retry_with_backoff(Fun, MaxRetries, InitialDelay, 1).

%% @doc 内部指数退避重试实现
retry_with_backoff(_Fun, 0, _Delay, _Attempt) ->
    {error, max_retries_reached};
retry_with_backoff(Fun, Retries, Delay, Attempt) ->
    try
        case Fun() of
            {ok, _} = Success -> Success;
            {error, Reason} ->
                ?SLOG(warning, #{
                    msg => "operation_failed_retrying",
                    reason => Reason,
                    attempt => Attempt,
                    retries_left => Retries - 1,
                    next_delay => min(Delay * 2, ?DEFAULT_MAX_DELAY)
                }),
                timer:sleep(Delay),
                retry_with_backoff(Fun, Retries - 1, min(Delay * 2, ?DEFAULT_MAX_DELAY), Attempt + 1)
        end
    catch
        E4:R4:S4 ->
            ?SLOG(error, #{
                msg => "retry_exception",
                error => E4,
                reason => R4,
                stacktrace => S4,
                attempt => Attempt,
                retries_left => Retries - 1
            }),
            timer:sleep(Delay),
            retry_with_backoff(Fun, Retries - 1, min(Delay * 2, ?DEFAULT_MAX_DELAY), Attempt + 1)
    end.

%% @doc 分类MongoDB错误
-spec classify_error(term()) -> temporary | permanent | unknown.
classify_error(Error) ->
    case Error of
        % 连接相关错误 - 临时
        {connection_failure, _} -> temporary;
        {network_error, _} -> temporary;
        {socket_error, _} -> temporary;
        {tcp_error, _} -> temporary;
        {timeout, _} -> temporary;

        % 服务器状态错误 - 临时
        {not_master, _} -> temporary;
        {no_primary, _} -> temporary;
        {node_is_recovering, _} -> temporary;
        {write_concern_error, _} -> temporary;

        % 认证错误 - 永久
        {unauthorized, _} -> permanent;
        {auth_failed, _} -> permanent;

        % 数据错误 - 永久
        {invalid_bson, _} -> permanent;
        {duplicate_key, _} -> permanent;
        {document_too_large, _} -> permanent;
        {invalid_document, _} -> permanent;

        % 其他常见错误
        {command_failed, #{<<"code">> := Code}} when Code >= 10000 andalso Code < 11000 -> temporary; % 网络错误
        {command_failed, #{<<"code">> := Code}} when Code >= 11000 andalso Code < 12000 -> permanent; % 用户错误
        {command_failed, #{<<"code">> := Code}} when Code >= 13000 andalso Code < 14000 -> temporary; % 分片错误

        % 默认情况
        _ -> unknown
    end.

%%%===================================================================
%%% 内部函数
%%%===================================================================

%% @doc 计算退避延迟
calculate_backoff_delay(Attempt) ->
    % 指数退避算法: min(max_delay, initial_delay * 2^(attempt-1))
    min(?DEFAULT_MAX_DELAY, ?DEFAULT_INITIAL_DELAY * trunc(math:pow(2, Attempt - 1))).
