%% @doc EMQX MongoDB Plugin - Inflight Messages Management Module
%% 这个模块是MQTT Inflight消息管理的核心组件，负责QoS 1和QoS 2消息的状态跟踪
%%
%% 功能概述：
%% 1. Inflight消息状态管理 - 跟踪未确认消息的状态（waiting_ack, waiting_rel, waiting_comp）
%% 2. MQTT包ID管理 - 管理MQTT包ID的分配、使用和释放
%% 3. 重试机制 - 处理消息确认超时和自动重传
%% 4. 会话恢复支持 - 在会话恢复时恢复Inflight消息到EMQX会话中
%% 5. 持久化存储 - 将Inflight消息状态持久化到MongoDB
%%
%% MQTT Inflight消息状态机：
%% QoS 1: PUBLISH -> waiting_ack -> PUBACK (完成)
%% QoS 2: PUBLISH -> waiting_rel -> PUBREL -> waiting_comp -> PUBCOMP (完成)
%%
%% 设计原则：
%% - 与EMQX会话模块深度集成，而非仅通过钩子跟踪
%% - 严格遵循MQTT协议的完整状态机规范
%% - 提供可靠的重试和超时机制，确保消息最终送达
%% - 在会话恢复时完整恢复Inflight消息到EMQX会话中
%% - 支持高并发和大量Inflight消息的场景
%%
%% Java等价概念：
%% 类似于MQTT客户端库中的Inflight消息管理器
%% 或者消息队列中的未确认消息跟踪系统
%% 或者分布式系统中的事务状态管理器
%%
%% 架构模式：
%% - 状态机模式：管理MQTT消息的状态转换
%% - 持久化模式：确保状态在系统重启后不丢失
%% - 重试模式：处理网络异常和确认超时
%% - 恢复模式：支持会话恢复时的状态重建
%%
%% @end

-module(emqx_plugin_mongodb_inflight).

-include("emqx_plugin_mongodb.hrl").

%% 插件生命周期函数
%% 这些函数管理Inflight消息模块的启动、加载和卸载
%% 类似于Java中的@PostConstruct和@PreDestroy注解方法
-export([
    init/0,                 % 初始化模块，类似于构造函数
    load/0,                 % 加载模块（无参数版本）
    load/1,                 % 加载模块（带配置参数）
    unload/0                % 卸载模块，类似于资源清理方法
]).

%% EMQX钩子回调函数
%% 这些函数响应EMQX的生命周期事件，类似于Spring的事件监听器
%% 用于在关键时刻执行Inflight消息的持久化和恢复操作
-export([
    on_session_created/2,       % 会话创建时的回调，类似于@EventListener
    on_session_terminated/3,    % 会话终止时的回调，保存Inflight消息
    on_client_connected/2,      % 客户端连接时的回调，恢复Inflight消息
    on_client_disconnected/3    % 客户端断开时的回调，处理异常断开
]).

%% Inflight消息管理核心函数
%% 这些函数提供Inflight消息的完整生命周期管理
%% 类似于Java中的Service层业务逻辑方法
-export([
    store_inflight_message/3,           % 存储Inflight消息，类似于save方法
    update_inflight_state/4,            % 更新消息状态，类似于update方法
    remove_inflight_message/2,          % 删除已确认消息，类似于delete方法
    find_inflight_messages/1,           % 查找客户端的Inflight消息，类似于findBy方法
    retry_inflight_messages/1,          % 重试超时消息，类似于retry机制
    cleanup_expired_inflight_messages/0,% 清理过期消息，类似于定时清理任务
    get_inflight_stats/0,               % 获取统计信息，类似于监控指标
    log_persisted_inflight_messages_info/1, % 记录持久化Inflight消息信息
                                        % 功能：记录客户端的持久化Inflight消息详情
                                        % Java等价：public void logPersistedInflightMessagesInfo(String clientId)

    restore_client_inflight_messages/2, % 恢复客户端Inflight消息（已弃用，违反方案A）
                                       % 警告：这个函数在方案A中不应该被调用
                                       % Java等价：@Deprecated public void restoreClientInflightMessages(String clientId, String sessionPid)

    save_session_inflight_messages/2,   % 保存会话Inflight消息，类似于batch save

    extract_message_expiry_interval/1,  % 提取Message Expiry Interval属性
                                       % 功能：从MQTT 5.0属性中提取消息过期时间
                                       % Java等价：public Integer extractMessageExpiryInterval(Properties props)

    calculate_inflight_message_expiry_time/1, % 计算飞行中消息过期时间
                                             % 功能：根据MQTT协议计算消息的过期时间
                                             % Java等价：public long calculateInflightMessageExpiryTime(Integer expiryInterval)

    determine_message_direction_and_state/1, % 确定消息方向和初始状态
                                            % 功能：根据QoS级别确定消息的方向和状态
                                            % Java等价：public DirectionAndState determineMessageDirectionAndState(int qos)

    handle_qos2_ack_with_direction/4    % 处理QoS 2消息确认（考虑方向）
                                       % 功能：根据MQTT协议处理QoS 2状态机
                                       % Java等价：public void handleQos2AckWithDirection(String clientId, int packetId, AckType ackType, Direction direction)
]).

%% 内部辅助函数
%% 这些函数提供模块内部使用的工具和辅助功能
%% 类似于Java中的private或protected方法
-export([
    get_inflight_collection/0,              % 获取集合名称，类似于配置获取方法
    ensure_inflight_collection_and_indexes/1, % 确保集合和索引存在，类似于初始化方法
    start_retry_timer/0,                    % 启动重试定时器，类似于定时任务启动
    do_retry_inflight_messages/0            % 执行重试逻辑，类似于定时任务执行方法
]).

%% 注意：集合名称现在统一定义在 emqx_plugin_mongodb.hrl 中
%% 这样可以保持整个项目的命名一致性

%% MQTT Inflight消息状态常量定义
%% 这些状态对应MQTT协议中QoS 1和QoS 2消息的不同阶段
%% 类似于Java中的枚举类型或常量定义
%%
%% MQTT QoS 1流程：PUBLISH -> waiting_ack -> PUBACK
%% MQTT QoS 2流程：PUBLISH -> waiting_rel -> PUBREL -> waiting_comp -> PUBCOMP
-define(STATE_WAITING_ACK, <<"waiting_ack">>).    % 等待PUBACK确认（QoS 1）
-define(STATE_WAITING_REL, <<"waiting_rel">>).    % 等待PUBREL释放（QoS 2第一阶段）
-define(STATE_WAITING_COMP, <<"waiting_comp">>).  % 等待PUBCOMP完成（QoS 2第二阶段）

%% 重试机制配置常量
%% 这些常量控制Inflight消息的重试行为
%% 类似于Java中的配置常量或超时设置
-define(DEFAULT_RETRY_INTERVAL, 30000).    % 默认重试间隔：30秒
                                           % 类似于@Retryable(delay = 30000)
-define(DEFAULT_MAX_RETRIES, 3).           % 默认最大重试次数：3次
                                           % 类似于@Retryable(maxAttempts = 3)

%% 定时器引用常量
%% 用于标识重试定时器，便于管理和取消
%% 类似于Java中的ScheduledFuture引用
-define(RETRY_TIMER_REF, inflight_retry_timer).

%%--------------------------------------------------------------------
%% 插件生命周期函数实现
%% 这些函数管理模块的初始化、加载和卸载过程
%% 类似于Java Spring Boot的生命周期管理
%%--------------------------------------------------------------------

%% @doc 初始化Inflight消息管理模块
%% 这个函数在模块首次加载时被调用，执行基本的初始化操作
%%
%% 功能说明：
%% 1. 记录模块初始化日志
%% 2. 执行必要的初始化检查
%% 3. 准备模块的运行环境
%%
%% 返回值：
%% - ok: 初始化成功
%%
%% Java等价概念：
%% 类似于Spring的@PostConstruct注解方法
%% 或者Java构造函数中的初始化逻辑
%%
%% 示例：
%% @PostConstruct
%% public void init() {
%%     logger.info("Initializing inflight messages module");
%% }
init() ->
    %% 记录模块初始化开始的日志
    %% 这有助于系统启动过程的跟踪和调试
    %% 在Java中相当于：
    %% logger.info("Initializing inflight messages module");
    ?SLOG(info, #{msg => "initializing_inflight_messages_module"}),
    ok.

%% @doc 加载Inflight消息管理模块（无参数版本）
%% 这是load/1函数的便捷包装，使用默认参数
%%
%% 功能说明：
%% 调用load/1函数并传入空的选项列表
%%
%% Java等价概念：
%% 类似于方法重载或默认参数
%% public void load() { load(Collections.emptyList()); }
load() ->
    %% 调用带参数的load函数，传入空列表作为默认选项
    load([]).

%% @doc 加载Inflight消息管理模块（带配置参数）
%% 这个函数是模块加载的核心实现，负责启动所有必要的服务
%%
%% 功能说明：
%% 1. 读取和验证配置信息
%% 2. 根据配置决定是否启用Inflight持久化
%% 3. 注册EMQX钩子函数
%% 4. 启动重试定时器
%% 5. 异步恢复历史Inflight消息
%%
%% 参数说明：
%% - _Opts: 加载选项列表（当前未使用，为未来扩展预留）
%%
%% 返回值：
%% - ok: 加载完成（无论成功还是失败都返回ok）
%%
%% Java等价概念：
%% 类似于Spring Boot的自动配置类
%% 或者@EnableXXX注解的配置加载
%%
%% 示例：
%% @Configuration
%% @ConditionalOnProperty(name = "inflight.persistence.enabled", havingValue = "true")
%% public class InflightPersistenceAutoConfiguration {
%%     @PostConstruct
%%     public void load() { ... }
%% }
load(_Opts) ->
    try
        %% 读取MongoDB插件的完整配置
        %% 在Java中相当于：
        %% @Value("${mongodb.config}")
        %% private MongoConfig config;
        Config = emqx_plugin_mongodb:read_config(),

        %% 提取Inflight持久化的专门配置
        %% 如果配置不存在，使用空映射作为默认值
        %% 在Java中相当于：
        %% InflightConfig inflightConfig = config.getInflightPersistence()
        %%     .orElse(new InflightConfig());
        InflightConfig = maps:get(inflight_persistence, Config, #{}),

        %% 检查Inflight持久化是否启用
        %% 这是一个功能开关，允许运行时控制功能启用状态
        %% 在Java中相当于：
        %% @ConditionalOnProperty(name = "inflight.persistence.enabled", havingValue = "true")
        case maps:get(enabled, InflightConfig, false) of
            true ->
                %% Inflight持久化已启用，开始初始化所有组件

                %% 注册EMQX钩子函数
                %% 这些钩子会在EMQX的关键生命周期事件中被调用
                %% 在Java中相当于：
                %% @EventListener
                %% public void onSessionCreated(SessionCreatedEvent event) { ... }
                register_hooks(),

                %% 启动重试定时器
                %% 用于定期检查和重试超时的Inflight消息
                %% 在Java中相当于：
                %% @Scheduled(fixedDelay = 30000)
                %% public void retryInflightMessages() { ... }
                start_retry_timer(),

                %% 异步恢复历史Inflight消息
                %% 使用spawn创建独立进程，避免阻塞模块加载
                %% 在Java中相当于：
                %% @Async
                %% public void restoreInflightMessages() {
                %%     Thread.sleep(3000); // 等待系统完全启动
                %%     doRestoreInflightMessages();
                %% }
                spawn(fun() ->
                    timer:sleep(3000), %% 等待EMQX完全启动，确保所有依赖服务可用
                    restore_inflight_messages()
                end),

                %% 记录模块加载成功的日志
                ?SLOG(info, #{msg => "inflight_persistence_module_loaded_successfully"});
            false ->
                %% Inflight持久化未启用，跳过加载过程
                %% 记录信息日志，说明功能被禁用
                ?SLOG(info, #{msg => "inflight_persistence_disabled_skipping_load"})
        end
    catch
        %% 捕获加载过程中的所有异常
        %% 确保异常不会导致整个插件加载失败
        %% E: 异常类别（error、exit、throw）
        %% R: 异常原因
        %% S: 调用栈信息
        E:R:S ->
            %% 记录详细的错误信息，包含完整的上下文
            %% 这有助于问题诊断和调试
            ?SLOG(error, #{
                msg => "failed_to_load_inflight_persistence_module",
                error => E,                    %% 异常类别
                reason => R,                   %% 异常原因
                stacktrace => S                %% 调用栈，用于调试
            })
    end,
    %% 无论成功还是失败，都返回ok
    %% 这确保单个模块的加载失败不会影响整个插件的启动
    ok.

%% @doc 卸载Inflight消息管理模块
%% 这个函数负责清理模块加载时创建的所有资源和服务
%%
%% 功能说明：
%% 1. 注销所有已注册的EMQX钩子函数
%% 2. 停止重试定时器，避免资源泄漏
%% 3. 清理其他相关资源
%% 4. 记录卸载状态日志
%%
%% 返回值：
%% - ok: 卸载完成（无论成功还是失败都返回ok）
%%
%% Java等价概念：
%% 类似于Spring的@PreDestroy注解方法
%% 或者Java的资源清理逻辑
%% 或者AutoCloseable接口的close方法
%%
%% 示例：
%% @PreDestroy
%% public void unload() {
%%     unregisterEventListeners();
%%     stopScheduledTasks();
%%     cleanupResources();
%% }
unload() ->
    try
        %% 注销所有已注册的EMQX钩子函数
        %% 这确保模块卸载后不会再接收到EMQX事件
        %% 在Java中相当于：
        %% eventListenerRegistry.unregisterAll(this);
        unregister_hooks(),

        %% 停止重试定时器
        %% 防止定时器在模块卸载后继续运行，避免资源泄漏
        %% 在Java中相当于：
        %% if (scheduledFuture != null) {
        %%     scheduledFuture.cancel(true);
        %% }
        stop_retry_timer(),

        %% 记录模块卸载成功的日志
        %% 这有助于跟踪模块的生命周期状态
        ?SLOG(info, #{msg => "inflight_persistence_module_unloaded_successfully"})
    catch
        %% 捕获卸载过程中的所有异常
        %% 确保异常不会阻止卸载过程的完成
        %% E: 异常类别（error、exit、throw）
        %% R: 异常原因
        %% S: 调用栈信息
        E:R:S ->
            %% 记录详细的错误信息
            %% 即使卸载过程中出现错误，也要记录下来便于调试
            ?SLOG(error, #{
                msg => "failed_to_unload_inflight_persistence_module",
                error => E,                    %% 异常类别
                reason => R,                   %% 异常原因
                stacktrace => S                %% 调用栈，用于调试
            })
    end,
    %% 无论成功还是失败，都返回ok
    %% 这确保卸载过程总是能够完成，不会阻塞系统关闭
    ok.

%%--------------------------------------------------------------------
%% 钩子管理函数
%% 这些函数负责注册和注销EMQX的生命周期钩子
%% 类似于Java中的事件监听器注册和注销
%%--------------------------------------------------------------------

%% @doc 注册Inflight消息相关钩子
%% 这个函数向EMQX注册必要的钩子函数，以便在关键事件发生时执行Inflight消息处理
%%
%% 功能说明：
%% 1. 注册会话生命周期钩子，跟踪会话的创建和终止
%% 2. 注册客户端连接钩子，处理Inflight消息的保存和恢复
%% 3. 设置适当的优先级，确保钩子执行顺序
%%
%% 钩子优先级说明：
%% - 200: 中等优先级，在大多数其他钩子之后执行
%% - 这确保EMQX的核心处理完成后再执行Inflight消息处理
%%
%% Java等价概念：
%% 类似于Spring的@EventListener注册
%% 或者观察者模式的监听器注册
%%
%% 示例：
%% @EventListener
%% public void onSessionCreated(SessionCreatedEvent event) { ... }
register_hooks() ->
    %% 注册会话相关钩子 - 与会话生命周期深度集成
    %% 这些钩子确保Inflight消息与EMQX会话状态保持同步

    %% 会话创建钩子：在新会话创建时初始化Inflight消息跟踪
    %% 在Java中相当于：
    %% @EventListener
    %% public void onSessionCreated(SessionCreatedEvent event) { ... }
    emqx_hooks:add('session.created', {?MODULE, on_session_created, []}, 200),

    %% 会话终止钩子：在会话终止时保存Inflight消息状态
    %% 在Java中相当于：
    %% @EventListener
    %% public void onSessionTerminated(SessionTerminatedEvent event) { ... }
    emqx_hooks:add('session.terminated', {?MODULE, on_session_terminated, []}, 200),

    %% 注册客户端连接/断开钩子 - 处理Inflight消息的保存和恢复
    %% 这些钩子处理客户端网络连接层面的事件

    %% 客户端连接钩子：在客户端连接时恢复Inflight消息
    %% 在Java中相当于：
    %% @EventListener
    %% public void onClientConnected(ClientConnectedEvent event) { ... }
    emqx_hooks:add('client.connected', {?MODULE, on_client_connected, []}, 200),

    %% 客户端断开钩子：在客户端断开时处理异常断开情况
    %% 在Java中相当于：
    %% @EventListener
    %% public void onClientDisconnected(ClientDisconnectedEvent event) { ... }
    emqx_hooks:add('client.disconnected', {?MODULE, on_client_disconnected, []}, 200).

%% @doc 注销Inflight消息相关钩子
%% 这个函数从EMQX中移除所有已注册的钩子函数
%%
%% 功能说明：
%% 1. 移除所有已注册的钩子函数
%% 2. 确保模块卸载后不会再接收到事件
%% 3. 防止内存泄漏和资源占用
%%
%% Java等价概念：
%% 类似于事件监听器的注销
%% 或者@PreDestroy中的清理逻辑
%%
%% 示例：
%% @PreDestroy
%% public void unregisterEventListeners() {
%%     eventRegistry.unregisterAll(this);
%% }
unregister_hooks() ->
    %% 移除会话相关钩子
    %% 在Java中相当于：
    %% eventRegistry.unregister(SessionCreatedEvent.class, this);
    emqx_hooks:del('session.created', {?MODULE, on_session_created}),
    emqx_hooks:del('session.terminated', {?MODULE, on_session_terminated}),

    %% 移除客户端连接相关钩子
    %% 在Java中相当于：
    %% eventRegistry.unregister(ClientConnectedEvent.class, this);
    emqx_hooks:del('client.connected', {?MODULE, on_client_connected}),
    emqx_hooks:del('client.disconnected', {?MODULE, on_client_disconnected}).

%%--------------------------------------------------------------------
%% 钩子回调函数实现
%% 这些函数响应EMQX的生命周期事件，执行Inflight消息的相关处理
%% 类似于Java中的事件监听器方法
%%--------------------------------------------------------------------

%% @doc 会话创建钩子回调 - 初始化Inflight消息跟踪
%% 这个函数在EMQX会话创建时被调用，负责初始化Inflight消息的跟踪机制
%%
%% 功能说明：
%% 1. 提取客户端ID和会话类型信息
%% 2. 根据会话类型（持久/非持久）执行不同的处理逻辑
%% 3. 对于持久会话，初始化Inflight消息跟踪
%% 4. 对于Clean Session，清理历史Inflight消息
%%
%% 参数说明：
%% - ClientInfo: 客户端信息映射，包含clientid等字段
%% - SessInfo: 会话信息映射，包含clean_start等字段
%%
%% 返回值：
%% - ok: 总是返回ok，确保钩子链继续执行
%%
%% MQTT会话类型说明：
%% - Clean Session (clean_start = true): 临时会话，断开后清理所有状态
%% - Persistent Session (clean_start = false): 持久会话，保持状态直到明确清理
%%
%% Java等价概念：
%% 类似于Spring的@EventListener方法
%% 或者JMS的MessageListener.onMessage方法
%%
%% 示例：
%% @EventListener
%% public void onSessionCreated(SessionCreatedEvent event) {
%%     if (event.isPersistent()) {
%%         initializeInflightTracking(event.getClientId());
%%     } else {
%%         cleanupInflightMessages(event.getClientId());
%%     }
%% }
on_session_created(ClientInfo, SessInfo) ->
    try
        %% 从客户端信息中提取客户端ID
        %% 客户端ID是MQTT协议中的唯一标识符
        %% 在Java中相当于：
        %% String clientId = clientInfo.getClientId();
        ClientId = maps:get(clientid, ClientInfo, <<>>),

        %% 从会话信息中提取Clean Start标志
        %% Clean Start决定了会话的持久性策略
        %% 在Java中相当于：
        %% boolean cleanStart = sessionInfo.isCleanStart();
        CleanStart = maps:get(clean_start, SessInfo, true),

        %% 记录会话创建的调试日志
        %% 包含关键的会话参数，便于调试和监控
        ?SLOG(debug, #{
            msg => "session_created_inflight_tracking",
            client_id => ClientId,
            clean_start => CleanStart
        }),

        %% 根据会话类型执行不同的Inflight消息处理策略
        %% 这是MQTT协议规范的核心要求
        case CleanStart of
            false ->
                %% 持久会话：需要保持Inflight消息状态
                %% 异步初始化Inflight消息跟踪，避免阻塞会话创建
                %% 在Java中相当于：
                %% @Async
                %% public void initializePersistentSession(String clientId) {
                %%     inflightTracker.initialize(clientId);
                %% }
                spawn(fun() ->
                    initialize_inflight_tracking(ClientId)
                end);
            true ->
                %% Clean Session：清理所有历史状态
                %% 异步清理可能存在的旧Inflight消息
                %% 在Java中相当于：
                %% @Async
                %% public void cleanupSession(String clientId) {
                %%     inflightTracker.cleanup(clientId);
                %% }
                spawn(fun() ->
                    cleanup_client_inflight_messages(ClientId)
                end)
        end
    catch
        %% 捕获所有异常，确保钩子不会因为异常而中断
        %% 这是钩子函数的最佳实践：永远不要让异常传播出去
        %% E: 异常类别（error、exit、throw）
        %% R: 异常原因
        %% S: 调用栈信息
        E:R:S ->
            %% 记录详细的错误信息，但不重新抛出异常
            %% 在Java中相当于：
            %% @EventListener
            %% public void onSessionCreated(SessionCreatedEvent event) {
            %%     try {
            %%         // 处理逻辑
            %%     } catch (Exception e) {
            %%         logger.error("Error in session created handler", e);
            %%         // 不重新抛出异常
            %%     }
            %% }
            ?SLOG(error, #{
                msg => "error_in_inflight_session_created_hook",
                error => E,                    %% 异常类别
                reason => R,                   %% 异常原因
                stacktrace => S                %% 调用栈，用于调试
            })
    end,
    %% 总是返回ok，确保EMQX的钩子链能够继续执行
    %% 这是钩子函数的标准做法
    ok.

%% @doc 会话终止钩子回调 - 保存Inflight消息
on_session_terminated(ClientInfo, Reason, SessInfo) ->
    try
        ClientId = maps:get(clientid, ClientInfo, <<>>),
        CleanStart = maps:get(clean_start, SessInfo, true),

        ?SLOG(debug, #{
            msg => "session_terminated_inflight_handling",
            client_id => ClientId,
            reason => Reason,
            clean_start => CleanStart
        }),

        % 如果是持久会话，保存Inflight消息
        case CleanStart of
            false ->
                % 持久会话，保存Inflight消息到MongoDB
                spawn(fun() ->
                    save_session_inflight_to_mongodb(ClientId, SessInfo)
                end);
            true ->
                % Clean session，清理Inflight消息
                spawn(fun() ->
                    cleanup_client_inflight_messages(ClientId)
                end)
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_in_inflight_session_terminated_hook",
                error => E,
                reason => R,
                stacktrace => S
            })
    end,
    ok.

%% @doc 客户端连接钩子回调 - 处理Inflight消息持久化
%% 修复：采用方案A - 纯持久化方案
%%
%% 功能说明：
%% 1. 不直接恢复Inflight消息，避免与EMQX内置QoS机制冲突
%% 2. 记录持久化Inflight消息的信息供管理员参考
%% 3. 让EMQX在客户端重连时自然处理未确认消息的重传
%%
%% 设计原理：
%% - 插件只负责持久化，不干扰EMQX的QoS处理逻辑
%% - EMQX的内置QoS机制会在客户端重连时自动处理未确认消息
%% - 避免重复处理和状态不一致问题
%%
%% 修复说明：
%% - 原方案：插件直接恢复Inflight消息到EMQX会话 → 可能与EMQX内置QoS机制严重冲突
%% - 新方案：插件只记录信息 → 让EMQX内置功能处理
%%
%% Java等价概念：
%% @EventListener
%% public void handleClientConnectedForInflightPersistence(ClientConnectedEvent event) {
%%     if (!event.isCleanStart()) {
%%         // 不直接恢复消息，只记录信息
%%         auditLogger.info("Client reconnected with persistent session: client={}", event.getClientId());
%%         logPersistedInflightMessages(event.getClientId());
%%     }
%%     // 让MQTT broker的QoS机制自然处理未确认消息
%% }
on_client_connected(ClientInfo, ConnInfo) ->
    try
        ClientId = maps:get(clientid, ClientInfo, <<>>),
        CleanStart = maps:get(clean_start, ConnInfo, true),

        ?SLOG(debug, #{
            msg => "client_connected_inflight_persistence_handling",
            client_id => ClientId,
            clean_start => CleanStart,
            approach => "pure_persistence_no_direct_restore"
        }),

        %% 根据会话类型处理持久化Inflight消息
        case CleanStart of
            false ->
                %% 持久会话，记录持久化Inflight消息信息（不直接恢复）
                spawn(fun() ->
                    log_persisted_inflight_messages_info(ClientId)
                end);
            true ->
                %% Clean session，清理旧的Inflight消息记录
                spawn(fun() ->
                    cleanup_client_inflight_messages(ClientId)
                end)
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_in_inflight_client_connected_hook",
                error => E,
                reason => R,
                stacktrace => S
            })
    end,
    ok.

%% @doc 客户端断开连接钩子回调 - 保存Inflight消息
on_client_disconnected(ClientInfo, Reason, _Env) ->
    try
        ClientId = maps:get(clientid, ClientInfo, <<>>),

        ?SLOG(debug, #{
            msg => "client_disconnected_inflight_handling",
            client_id => ClientId,
            reason => Reason
        }),

        % 获取客户端的会话信息来判断是否为持久会话
        case emqx_cm:lookup_channels(ClientId) of
            [] ->
                % 客户端已完全断开，检查是否需要保存Inflight消息
                spawn(fun() ->
                    handle_client_disconnect_inflight(ClientId, Reason)
                end);
            _Channels ->
                % 客户端还有其他连接，不处理Inflight消息
                ok
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_in_inflight_client_disconnected_hook",
                error => E,
                reason => R,
                stacktrace => S
            })
    end,
    ok.

%%--------------------------------------------------------------------
%% Inflight消息管理辅助函数
%%--------------------------------------------------------------------

%% @doc 初始化Inflight消息跟踪
initialize_inflight_tracking(ClientId) ->
    try
        ?SLOG(debug, #{
            msg => "initializing_inflight_tracking",
            client_id => ClientId
        }),

        % 确保集合和索引存在
        Collection = get_inflight_collection(),
        ensure_inflight_collection_and_indexes(Collection),

        % 清理可能存在的旧Inflight消息
        cleanup_client_inflight_messages(ClientId)
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_initializing_inflight_tracking",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 保存会话Inflight消息到MongoDB
save_session_inflight_to_mongodb(ClientId, SessInfo) ->
    try
        ?SLOG(debug, #{
            msg => "saving_session_inflight_to_mongodb",
            client_id => ClientId
        }),

        % 获取会话的Inflight消息
        case extract_inflight_from_session(SessInfo) of
            {ok, InflightMsgs} when length(InflightMsgs) > 0 ->
                ?SLOG(info, #{
                    msg => "saving_inflight_messages_to_mongodb",
                    client_id => ClientId,
                    count => length(InflightMsgs)
                }),

                % 保存每个Inflight消息
                lists:foreach(fun({PacketId, Message, State}) ->
                    store_inflight_message_with_state(ClientId, PacketId, Message, State)
                end, InflightMsgs);
            {ok, []} ->
                ?SLOG(debug, #{
                    msg => "no_inflight_messages_to_save",
                    client_id => ClientId
                });
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_extract_inflight_from_session",
                    client_id => ClientId,
                    reason => Reason
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_saving_session_inflight_to_mongodb",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 从MongoDB恢复客户端Inflight消息
%% 警告：这个函数在方案A中不应该被调用！
%%
%% 修复说明：
%% - 这个函数违反了方案A的设计原则（插件不应该直接恢复Inflight消息到EMQX会话）
%% - 保留此函数仅用于向后兼容，但会记录警告日志
%% - 在新的方案A中，应该让EMQX的QoS机制处理未确认消息
restore_client_inflight_from_mongodb(ClientId) ->
    %% 记录警告：这个函数不应该在方案A中被调用
    ?SLOG(warning, #{
        msg => "restore_client_inflight_from_mongodb_called_violates_plan_a",
        client_id => ClientId,
        note => "this_function_should_not_be_called_in_pure_persistence_approach"
    }),

    try
        ?SLOG(debug, #{
            msg => "restoring_client_inflight_from_mongodb_but_violates_plan_a",
            client_id => ClientId
        }),

        Collection = get_inflight_collection(),
        Filter = #{<<"client_id">> => ClientId},

        %% 使用同步API查找Inflight消息（违反方案A原则）
        case emqx_plugin_mongodb_api:find_documents(Collection, Filter) of
            {ok, InflightDocs} when is_list(InflightDocs), length(InflightDocs) > 0 ->
                ?SLOG(info, #{
                    msg => "found_inflight_messages_to_restore_but_violates_plan_a",
                    client_id => ClientId,
                    count => length(InflightDocs)
                }),

                %% 恢复每个Inflight消息到EMQX会话（违反方案A原则）
                lists:foreach(fun(Doc) ->
                    restore_inflight_message_to_session(ClientId, Doc)
                end, InflightDocs);
            {ok, []} ->
                ?SLOG(debug, #{
                    msg => "no_inflight_messages_to_restore",
                    client_id => ClientId
                });
            {ok, InflightDocs} when is_list(InflightDocs) ->
                ?SLOG(debug, #{
                    msg => "no_inflight_messages_to_restore",
                    client_id => ClientId,
                    count => length(InflightDocs)
                });
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_query_inflight_messages",
                    client_id => ClientId,
                    reason => Reason
                });
            {async_return, ok} ->
                % 异步查询已提交，但无法获取结果
                ?SLOG(info, #{
                    msg => "inflight_query_async_submitted",
                    client_id => ClientId,
                    note => "results_will_be_processed_asynchronously"
                });
            Other ->
                ?SLOG(warning, #{
                    msg => "unexpected_inflight_query_response",
                    client_id => ClientId,
                    response => Other
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_client_inflight_from_mongodb",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 处理客户端断开连接的Inflight消息
handle_client_disconnect_inflight(ClientId, Reason) ->
    try
        % 检查是否为异常断开（需要保存Inflight消息）
        case is_abnormal_disconnect(Reason) of
            true ->
                ?SLOG(debug, #{
                    msg => "abnormal_disconnect_saving_inflight",
                    client_id => ClientId,
                    reason => Reason
                }),

                % 获取客户端的会话信息
                case get_client_session_info(ClientId) of
                    {ok, SessInfo} ->
                        save_session_inflight_to_mongodb(ClientId, SessInfo);
                    {error, SessionReason} ->
                        ?SLOG(warning, #{
                            msg => "failed_to_get_session_info_for_inflight_save",
                            client_id => ClientId,
                            reason => SessionReason
                        })
                end;
            false ->
                ?SLOG(debug, #{
                    msg => "normal_disconnect_no_inflight_save_needed",
                    client_id => ClientId,
                    reason => Reason
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_handling_client_disconnect_inflight",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%%--------------------------------------------------------------------
%% Inflight消息管理函数
%%--------------------------------------------------------------------

%% @doc 存储Inflight消息
store_inflight_message(ClientId, PacketId, Message) ->
    try
        Collection = get_inflight_collection(),
        ensure_inflight_collection_and_indexes(Collection),

        % 获取MQTT 5.0消息属性
        Props = emqx_message:get_header(properties, Message, #{}),

        % 解析MQTT 5.0消息属性
        MessageExpiryInterval = extract_message_expiry_interval(Props),
        TopicAlias = extract_topic_alias(Props),
        ResponseTopic = extract_response_topic(Props),
        CorrelationData = extract_correlation_data(Props),
        UserProperties = extract_user_properties(Props),
        ContentType = extract_content_type(Props),
        PayloadFormatIndicator = extract_payload_format_indicator(Props),
        SubscriptionIdentifier = extract_subscription_identifier(Props),

        % 计算消息过期时间（优先使用消息自身的过期设置）
        ExpiryTime = calculate_inflight_message_expiry_time(MessageExpiryInterval),

        % 确定消息方向和初始状态
        {Direction, InitialState} = determine_message_direction_and_state(emqx_message:qos(Message)),

        % 构建符合MQTT协议的Inflight消息文档
        InflightDoc = #{
            <<"_id">> => generate_inflight_id(ClientId, PacketId),
            <<"client_id">> => ClientId,
            <<"packet_id">> => PacketId,
            <<"message_id">> => emqx_guid:to_hexstr(emqx_message:id(Message)),
            <<"topic">> => emqx_message:topic(Message),
            <<"payload">> => emqx_message:payload(Message),
            <<"qos">> => emqx_message:qos(Message),
            <<"retain">> => emqx_message:get_flag(retain, Message, false),
            <<"dup">> => emqx_message:get_flag(dup, Message, false),

            % MQTT 5.0消息属性
            <<"message_expiry_interval">> => MessageExpiryInterval,
            <<"topic_alias">> => TopicAlias,
            <<"response_topic">> => ResponseTopic,
            <<"correlation_data">> => CorrelationData,
            <<"user_properties">> => UserProperties,
            <<"content_type">> => ContentType,
            <<"payload_format_indicator">> => PayloadFormatIndicator,
            <<"subscription_identifier">> => SubscriptionIdentifier,

            % 飞行中消息状态管理
            <<"direction">> => Direction,
            <<"state">> => InitialState,
            <<"retry_count">> => 0,
            <<"created_at">> => erlang:system_time(millisecond),
            <<"delivered_at">> => undefined,
            <<"acked_at">> => undefined,
            <<"next_retry_at">> => erlang:system_time(millisecond) + ?DEFAULT_RETRY_INTERVAL,
            <<"expires_at">> => ExpiryTime,
            <<"node">> => atom_to_binary(node(), utf8),

            % 兼容性：保留原始属性
            <<"properties">> => encode_message_properties(Props)
        },

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {upsert, Collection,
                                #{<<"client_id">> => ClientId, <<"packet_id">> => PacketId},
                                InflightDoc}) of
            {ok, _} ->
                ?SLOG(debug, #{
                    msg => "inflight_message_stored",
                    client_id => ClientId,
                    packet_id => PacketId
                });
            {async_return, ok} ->
                ?SLOG(debug, #{
                    msg => "inflight_message_stored_async",
                    client_id => ClientId,
                    packet_id => PacketId
                });
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_store_inflight_message",
                    client_id => ClientId,
                    packet_id => PacketId,
                    reason => Reason
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_storing_inflight_message",
                error => E,
                reason => R,
                stacktrace => S,
                client_id => ClientId,
                packet_id => PacketId
            })
    end.

%% @doc 更新Inflight消息状态
update_inflight_state(ClientId, PacketId, NewState, ExtraFields) ->
    try
        Collection = get_inflight_collection(),

        % 构建更新文档
        UpdateDoc = #{
            <<"$set">> => maps:merge(#{
                <<"state">> => NewState,
                <<"updated_at">> => erlang:system_time(millisecond)
            }, ExtraFields)
        },

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {update, Collection,
                                #{<<"client_id">> => ClientId, <<"packet_id">> => PacketId},
                                UpdateDoc}) of
            {ok, _} ->
                ?SLOG(debug, #{
                    msg => "inflight_message_state_updated",
                    client_id => ClientId,
                    packet_id => PacketId,
                    new_state => NewState
                });
            {async_return, ok} ->
                ?SLOG(debug, #{
                    msg => "inflight_message_state_updated_async",
                    client_id => ClientId,
                    packet_id => PacketId,
                    new_state => NewState
                });
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_update_inflight_message_state",
                    client_id => ClientId,
                    packet_id => PacketId,
                    new_state => NewState,
                    reason => Reason
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_updating_inflight_message_state",
                error => E,
                reason => R,
                stacktrace => S,
                client_id => ClientId,
                packet_id => PacketId
            })
    end.

%% @doc 移除Inflight消息
remove_inflight_message(ClientId, PacketId) ->
    try
        Collection = get_inflight_collection(),

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {delete, Collection,
                                #{<<"client_id">> => ClientId, <<"packet_id">> => PacketId}}) of
            {ok, _} ->
                ?SLOG(debug, #{
                    msg => "inflight_message_removed",
                    client_id => ClientId,
                    packet_id => PacketId
                });
            {async_return, ok} ->
                ?SLOG(debug, #{
                    msg => "inflight_message_removed_async",
                    client_id => ClientId,
                    packet_id => PacketId
                });
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_remove_inflight_message",
                    client_id => ClientId,
                    packet_id => PacketId,
                    reason => Reason
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_removing_inflight_message",
                error => E,
                reason => R,
                stacktrace => S,
                client_id => ClientId,
                packet_id => PacketId
            })
    end.

%% @doc 查找客户端的Inflight消息
find_inflight_messages(ClientId) ->
    try
        Collection = get_inflight_collection(),
        Filter = #{<<"client_id">> => ClientId},

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {find, Collection, Filter}) of
            {ok, Results} ->
                {ok, Results};
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_find_inflight_messages",
                    client_id => ClientId,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_finding_inflight_messages",
                error => E,
                reason => R,
                stacktrace => S,
                client_id => ClientId
            }),
            {error, {E, R}}
    end.

%% @doc 重试Inflight消息
retry_inflight_messages(ClientId) ->
    try
        Collection = get_inflight_collection(),
        Now = erlang:system_time(millisecond),

        % 查找需要重试的消息
        Filter = #{
            <<"client_id">> => ClientId,
            <<"next_retry_at">> => #{<<"$lte">> => Now},
            <<"retry_count">> => #{<<"$lt">> => ?DEFAULT_MAX_RETRIES}
        },

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {find, Collection, Filter}) of
            {ok, Messages} ->
                lists:foreach(fun(Msg) ->
                    retry_single_message(Msg)
                end, Messages),
                {ok, length(Messages)};
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_retry_inflight_messages",
                    client_id => ClientId,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_retrying_inflight_messages",
                error => E,
                reason => R,
                stacktrace => S,
                client_id => ClientId
            }),
            {error, {E, R}}
    end.

%% @doc 清理过期的Inflight消息
cleanup_expired_inflight_messages() ->
    try
        Collection = get_inflight_collection(),
        Now = erlang:system_time(millisecond),

        % 删除过期的消息
        Filter = #{<<"expires_at">> => #{<<"$lte">> => Now}},

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {delete, Collection, Filter}) of
            {ok, Result} ->
                DeletedCount = maps:get(<<"deletedCount">>, Result, 0),
                ?SLOG(info, #{
                    msg => "expired_inflight_messages_cleaned",
                    deleted_count => DeletedCount
                }),
                {ok, DeletedCount};
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_cleanup_expired_inflight_messages",
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_cleaning_expired_inflight_messages",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 获取Inflight统计信息
get_inflight_stats() ->
    try
        Collection = get_inflight_collection(),

        % 统计各种状态的消息数量
        Pipeline = [
            #{<<"$group">> => #{
                <<"_id">> => <<"$state">>,
                <<"count">> => #{<<"$sum">> => 1}
            }}
        ],

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {aggregate, Collection, Pipeline}) of
            {ok, Results} ->
                Stats = lists:foldl(fun(#{<<"_id">> := State, <<"count">> := Count}, Acc) ->
                    Acc#{State => Count}
                end, #{}, Results),
                {ok, Stats};
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_get_inflight_stats",
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_getting_inflight_stats",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%%--------------------------------------------------------------------
%% 辅助函数
%%--------------------------------------------------------------------

%% @doc 处理QoS 2消息确认
%% 修复：实现完整的MQTT协议QoS 2状态机
handle_qos2_ack(ClientId, PacketId, AckType) ->
    handle_qos2_ack_with_direction(ClientId, PacketId, AckType, <<"outgoing">>).

%% @doc 处理QoS 2消息确认（考虑消息方向）
%% 根据MQTT协议，发送方和接收方有不同的状态机
handle_qos2_ack_with_direction(ClientId, PacketId, AckType, Direction) ->
    case {Direction, AckType} of
        {<<"outgoing">>, pubrec} ->
            % 发送方收到PUBREC，状态转换为waiting_rel，准备发送PUBREL
            update_inflight_state(ClientId, PacketId, ?STATE_WAITING_REL, #{
                <<"pubrec_received_at">> => erlang:system_time(millisecond),
                <<"next_action">> => <<"send_pubrel">>
            });
        {<<"outgoing">>, pubcomp} ->
            % 发送方收到PUBCOMP，QoS 2流程完成，移除记录
            update_inflight_state(ClientId, PacketId, <<"completed">>, #{
                <<"pubcomp_received_at">> => erlang:system_time(millisecond),
                <<"completed_at">> => erlang:system_time(millisecond)
            }),
            remove_inflight_message(ClientId, PacketId);
        {<<"incoming">>, pubrel} ->
            % 接收方收到PUBREL，状态转换为waiting_comp，准备发送PUBCOMP
            update_inflight_state(ClientId, PacketId, ?STATE_WAITING_COMP, #{
                <<"pubrel_received_at">> => erlang:system_time(millisecond),
                <<"next_action">> => <<"send_pubcomp">>
            });
        {<<"incoming">>, pubcomp} ->
            % 接收方发送PUBCOMP后，QoS 2流程完成，移除记录
            update_inflight_state(ClientId, PacketId, <<"completed">>, #{
                <<"pubcomp_sent_at">> => erlang:system_time(millisecond),
                <<"completed_at">> => erlang:system_time(millisecond)
            }),
            remove_inflight_message(ClientId, PacketId);
        _ ->
            ?SLOG(warning, #{
                msg => "invalid_qos2_ack_combination",
                client_id => ClientId,
                packet_id => PacketId,
                ack_type => AckType,
                direction => Direction,
                note => "this_combination_violates_mqtt_protocol"
            })
    end.

%% @doc 生成Inflight消息ID
generate_inflight_id(ClientId, PacketId) ->
    iolist_to_binary([ClientId, <<"_">>, integer_to_binary(PacketId)]).

%% @doc 编码消息属性
encode_message_properties(Properties) when is_map(Properties) ->
    maps:fold(fun(K, V, Acc) ->
        KeyBin = case K of
            KBin when is_binary(KBin) -> KBin;
            KAtom when is_atom(KAtom) -> atom_to_binary(KAtom, utf8);
            _ -> iolist_to_binary(io_lib:format("~p", [K]))
        end,
        ValueBin = case V of
            VBin when is_binary(VBin) -> VBin;
            VAtom when is_atom(VAtom) -> atom_to_binary(VAtom, utf8);
            VInt when is_integer(VInt) -> integer_to_binary(VInt);
            _ -> iolist_to_binary(io_lib:format("~p", [V]))
        end,
        Acc#{KeyBin => ValueBin}
    end, #{}, Properties);
encode_message_properties(_) ->
    #{}.

%% @doc 获取Inflight集合名称
get_inflight_collection() ->
    Config = emqx_plugin_mongodb:read_config(),
    InflightConfig = maps:get(inflight_persistence, Config, #{}),
    maps:get(collection, InflightConfig, ?DEFAULT_INFLIGHT_COLLECTION).

%% @doc 记录持久化Inflight消息信息
%% 修复：采用方案A - 纯持久化方案
%%
%% 功能说明：
%% 1. 记录客户端的持久化Inflight消息详细信息
%% 2. 不直接恢复消息，避免与EMQX内置QoS机制冲突
%% 3. 供管理员参考和故障排查使用
%%
%% 参数说明：
%% - ClientId: 客户端ID
%%
%% Java等价概念：
%% public void logPersistedInflightMessagesInfo(String clientId) {
%%     List<InflightMessage> messages = inflightRepository.findByClientId(clientId);
%%     if (!messages.isEmpty()) {
%%         logger.warn("Client {} has {} persisted inflight messages after reconnect", clientId, messages.size());
%%         for (InflightMessage msg : messages) {
%%             logger.warn("  - Inflight: packet_id={}, topic={}, qos={}, state={}",
%%                        msg.getPacketId(), msg.getTopic(), msg.getQos(), msg.getState());
%%         }
%%     }
%% }
log_persisted_inflight_messages_info(ClientId) ->
    try
        Collection = get_inflight_collection(),
        Filter = #{<<"client_id">> => ClientId},

        ?SLOG(debug, #{
            msg => "checking_persisted_inflight_messages",
            client_id => ClientId
        }),

        case emqx_plugin_mongodb_api:find_documents(Collection, Filter) of
            {ok, InflightDocs} when is_list(InflightDocs), length(InflightDocs) > 0 ->
                MessageCount = length(InflightDocs),
                ?SLOG(warning, #{
                    msg => "client_has_persisted_inflight_messages_after_reconnect",
                    client_id => ClientId,
                    message_count => MessageCount,
                    note => "messages_will_be_handled_by_emqx_qos_mechanism"
                }),

                %% 记录每个Inflight消息的详细信息（限制数量避免日志过多）
                MaxLogMessages = 10,
                MessagesToLog = case MessageCount > MaxLogMessages of
                    true -> lists:sublist(InflightDocs, MaxLogMessages);
                    false -> InflightDocs
                end,

                lists:foreach(fun(InflightDoc) ->
                    try
                        PacketId = maps:get(<<"packet_id">>, InflightDoc, 0),
                        Topic = maps:get(<<"topic">>, InflightDoc, <<"unknown">>),
                        QoS = maps:get(<<"qos">>, InflightDoc, 0),
                        State = maps:get(<<"state">>, InflightDoc, <<"unknown">>),
                        CreatedAt = maps:get(<<"created_at">>, InflightDoc, 0),

                        ?SLOG(warning, #{
                            msg => "persisted_inflight_message_details_after_reconnect",
                            client_id => ClientId,
                            packet_id => PacketId,
                            topic => Topic,
                            qos => QoS,
                            state => State,
                            created_at => CreatedAt,
                            note => "will_be_handled_by_emqx_qos_mechanism_when_needed"
                        })
                    catch
                        E:R:S ->
                            ?SLOG(error, #{
                                msg => "error_logging_inflight_message_details",
                                client_id => ClientId,
                                error => E,
                                reason => R,
                                stacktrace => S,
                                inflight_doc => InflightDoc
                            })
                    end
                end, MessagesToLog),

                %% 如果消息数量超过限制，记录省略信息
                case MessageCount > MaxLogMessages of
                    true ->
                        ?SLOG(warning, #{
                            msg => "additional_persisted_inflight_messages_not_logged",
                            client_id => ClientId,
                            logged_count => MaxLogMessages,
                            total_count => MessageCount,
                            omitted_count => MessageCount - MaxLogMessages
                        });
                    false ->
                        ok
                end;
            {ok, []} ->
                ?SLOG(debug, #{
                    msg => "no_persisted_inflight_messages_found",
                    client_id => ClientId
                });
            {ok, InflightDocs} when is_list(InflightDocs) ->
                ?SLOG(debug, #{
                    msg => "no_persisted_inflight_messages_found",
                    client_id => ClientId,
                    count => length(InflightDocs)
                });
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_check_persisted_inflight_messages",
                    client_id => ClientId,
                    reason => Reason
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_checking_persisted_inflight_messages",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 确保Inflight集合和索引存在
ensure_inflight_collection_and_indexes(Collection) ->
    try
        % 创建索引
        Indexes = [
            % 客户端ID索引
            #{<<"key">> => #{<<"client_id">> => 1}, <<"name">> => <<"ifl_client_id_1_index">>},
            % 状态索引
            #{<<"key">> => #{<<"state">> => 1}, <<"name">> => <<"ifl_state_1_index">>},
            % 重试时间索引
            #{<<"key">> => #{<<"next_retry_at">> => 1}, <<"name">> => <<"ifl_next_retry_at_1_index">>},
            % 过期时间索引
            #{<<"key">> => #{<<"expires_at">> => 1}, <<"name">> => <<"ifl_expires_at_1_index">>},
            % 复合索引：客户端ID + 包ID
            #{<<"key">> => #{<<"client_id">> => 1, <<"packet_id">> => 1}, <<"name">> => <<"ifl_client_id_packet_id_1_index">>}
        ],

        lists:foreach(fun(IndexSpec) ->
            spawn(fun() ->
                emqx_plugin_mongodb_connector:create_index_async(Collection, IndexSpec)
            end)
        end, Indexes)
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_ensuring_inflight_collection_indexes",
                error => E,
                reason => R,
                stacktrace => S,
                collection => Collection
            })
    end.

%% @doc 启动重试定时器
start_retry_timer() ->
    case erlang:get(?RETRY_TIMER_REF) of
        undefined ->
            TimerRef = erlang:send_after(?DEFAULT_RETRY_INTERVAL, self(), retry_inflight_messages),
            erlang:put(?RETRY_TIMER_REF, TimerRef),
            ?SLOG(info, #{msg => "inflight_retry_timer_started"});
        _ExistingRef ->
            ?SLOG(debug, #{msg => "inflight_retry_timer_already_running"})
    end.

%% @doc 停止重试定时器
stop_retry_timer() ->
    case erlang:get(?RETRY_TIMER_REF) of
        undefined ->
            ok;
        TimerRef ->
            erlang:cancel_timer(TimerRef),
            erlang:erase(?RETRY_TIMER_REF),
            ?SLOG(info, #{msg => "inflight_retry_timer_stopped"})
    end.

%% @doc 执行重试检查
do_retry_inflight_messages() ->
    try
        Collection = get_inflight_collection(),
        Now = erlang:system_time(millisecond),

        % 查找需要重试的消息
        Filter = #{
            <<"next_retry_at">> => #{<<"$lte">> => Now},
            <<"retry_count">> => #{<<"$lt">> => ?DEFAULT_MAX_RETRIES}
        },

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {find, Collection, Filter, #{limit => 100}}) of
            {ok, Messages} ->
                lists:foreach(fun(Msg) ->
                    spawn(fun() -> retry_single_message(Msg) end)
                end, Messages),

                % 重新启动定时器
                start_retry_timer(),

                ?SLOG(debug, #{
                    msg => "inflight_retry_check_completed",
                    messages_found => length(Messages)
                });
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_check_retry_inflight_messages",
                    reason => Reason
                }),
                % 重新启动定时器
                start_retry_timer()
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_checking_retry_inflight_messages",
                error => E,
                reason => R,
                stacktrace => S
            }),
            % 重新启动定时器
            start_retry_timer()
    end.

%% @doc 恢复Inflight消息
restore_inflight_messages() ->
    try
        Collection = get_inflight_collection(),
        Now = erlang:system_time(millisecond),

        % 查找未过期的Inflight消息
        Filter = #{
            <<"expires_at">> => #{<<"$gt">> => Now}
        },

        % 使用同步查询获取消息
        case emqx_plugin_mongodb_api:find_documents(Collection, Filter) of
            {ok, Messages} when is_list(Messages) ->
                lists:foreach(fun(Msg) ->
                    spawn(fun() -> restore_single_inflight_message(Msg) end)
                end, Messages),

                ?SLOG(info, #{
                    msg => "inflight_messages_restored",
                    message_count => length(Messages)
                });
            {ok, _Other} ->
                ?SLOG(warning, #{
                    msg => "unexpected_inflight_restore_result",
                    result => _Other
                });
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_restore_inflight_messages",
                    reason => Reason
                });
            {async_return, ok} ->
                % 异步查询成功，但无法获取结果
                ?SLOG(info, #{
                    msg => "inflight_restore_async_query_submitted",
                    note => "results_will_be_processed_asynchronously"
                });
            Other ->
                ?SLOG(warning, #{
                    msg => "unexpected_inflight_restore_response",
                    response => Other
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_inflight_messages",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 重试单个消息
retry_single_message(Msg) ->
    try
        ClientId = maps:get(<<"client_id">>, Msg),
        PacketId = maps:get(<<"packet_id">>, Msg),
        RetryCount = maps:get(<<"retry_count">>, Msg, 0),

        % 更新重试计数和下次重试时间
        NewRetryCount = RetryCount + 1,
        NextRetryAt = erlang:system_time(millisecond) + ?DEFAULT_RETRY_INTERVAL,

        update_inflight_state(ClientId, PacketId, maps:get(<<"state">>, Msg), #{
            <<"retry_count">> => NewRetryCount,
            <<"next_retry_at">> => NextRetryAt,
            <<"last_retry_at">> => erlang:system_time(millisecond)
        }),

        ?SLOG(debug, #{
            msg => "inflight_message_retry_scheduled",
            client_id => ClientId,
            packet_id => PacketId,
            retry_count => NewRetryCount
        })
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_retrying_single_message",
                error => E,
                reason => R,
                stacktrace => S,
                message => Msg
            })
    end.

%% @doc 恢复单个Inflight消息
restore_single_inflight_message(Msg) ->
    try
        ClientId = maps:get(<<"client_id">>, Msg),
        PacketId = maps:get(<<"packet_id">>, Msg),
        State = maps:get(<<"state">>, Msg),

        ?SLOG(debug, #{
            msg => "inflight_message_restored",
            client_id => ClientId,
            packet_id => PacketId,
            state => State
        })

        % 这里可以添加更复杂的恢复逻辑，比如重新发送消息等
        % 目前只是记录日志
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_single_inflight_message",
                error => E,
                reason => R,
                stacktrace => S,
                message => Msg
            })
    end.

%%--------------------------------------------------------------------
%% 会话恢复相关函数
%%--------------------------------------------------------------------

%% @doc 恢复客户端的Inflight消息到EMQX会话中
%% 警告：这个函数在方案A中不应该被调用！
%%
%% 修复说明：
%% - 这个函数违反了方案A的设计原则（插件不应该直接恢复Inflight消息到EMQX会话）
%% - 保留此函数仅用于向后兼容，但会记录警告日志
%% - 在新的方案A中，应该让EMQX的QoS机制处理未确认消息
restore_client_inflight_messages(ClientId, SessionPid) ->
    %% 记录警告：这个函数不应该在方案A中被调用
    ?SLOG(warning, #{
        msg => "restore_client_inflight_messages_called_violates_plan_a",
        client_id => ClientId,
        session_pid => SessionPid,
        note => "this_function_should_not_be_called_in_pure_persistence_approach"
    }),

    try
        ?SLOG(info, #{
            msg => "restoring_client_inflight_messages_but_violates_plan_a",
            client_id => ClientId,
            session_pid => SessionPid
        }),

        %% 查询客户端的Inflight消息（违反方案A原则）
        Collection = get_inflight_collection(),
        Filter = #{<<"client_id">> => ClientId},

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {find, Collection, Filter}) of
            {ok, InflightMsgs} ->
                ?SLOG(info, #{
                    msg => "found_inflight_messages_to_restore_but_violates_plan_a",
                    client_id => ClientId,
                    count => length(InflightMsgs)
                }),

                %% 恢复每个Inflight消息到会话中（违反方案A原则）
                lists:foreach(fun(InflightMsg) ->
                    restore_single_inflight_to_session(ClientId, SessionPid, InflightMsg)
                end, InflightMsgs);
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_query_inflight_messages_for_restore",
                    client_id => ClientId,
                    reason => Reason
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_client_inflight_messages",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 恢复单个Inflight消息到会话中
restore_single_inflight_to_session(ClientId, SessionPid, InflightMsg) ->
    try
        PacketId = maps:get(<<"packet_id">>, InflightMsg),
        MessageId = maps:get(<<"message_id">>, InflightMsg),
        Topic = maps:get(<<"topic">>, InflightMsg),
        Payload = maps:get(<<"payload">>, InflightMsg),
        QoS = maps:get(<<"qos">>, InflightMsg),
        State = maps:get(<<"state">>, InflightMsg),

        % 重建EMQX消息
        Message = emqx_message:make(ClientId, QoS, Topic, Payload),
        Message1 = emqx_message:set_header(packet_id, PacketId, Message),
        Message2 = emqx_message:set_header(id, MessageId, Message1),

        % 根据状态恢复到会话的Inflight窗口中
        case State of
            <<"waiting_ack">> ->
                % 恢复到会话的Inflight窗口
                restore_to_session_inflight(SessionPid, PacketId, Message2);
            <<"waiting_rel">> ->
                % QoS 2消息等待PUBREL
                restore_to_session_inflight(SessionPid, PacketId, Message2);
            <<"waiting_comp">> ->
                % QoS 2消息等待PUBCOMP
                restore_to_session_inflight(SessionPid, PacketId, Message2);
            _ ->
                ?SLOG(warning, #{
                    msg => "unknown_inflight_state_skipping_restore",
                    client_id => ClientId,
                    packet_id => PacketId,
                    state => State
                })
        end,

        ?SLOG(debug, #{
            msg => "inflight_message_restored_to_session",
            client_id => ClientId,
            packet_id => PacketId,
            state => State
        })
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_single_inflight_to_session",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S,
                inflight_msg => InflightMsg
            })
    end.

%% @doc 恢复消息到会话的Inflight窗口
restore_to_session_inflight(SessionPid, PacketId, Message) ->
    try
        % 使用EMQX会话API恢复Inflight消息
        case emqx_session:restore_inflight(SessionPid, PacketId, Message) of
            ok ->
                ?SLOG(debug, #{
                    msg => "message_restored_to_session_inflight",
                    session_pid => SessionPid,
                    packet_id => PacketId
                });
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_restore_message_to_session_inflight",
                    session_pid => SessionPid,
                    packet_id => PacketId,
                    reason => Reason
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_calling_session_restore_inflight",
                session_pid => SessionPid,
                packet_id => PacketId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 保存会话的Inflight消息到MongoDB
%% 警告：这个函数在方案A中不应该被调用！
%%
%% 修复说明：
%% - 这个函数违反了方案A的设计原则（插件不应该直接访问EMQX会话的Inflight消息）
%% - 保留此函数仅用于向后兼容，但会记录警告日志
%% - 在新的方案A中，应该通过钩子监听消息事件进行持久化
save_session_inflight_messages(ClientId, SessionPid) ->
    %% 记录警告：这个函数不应该在方案A中被调用
    ?SLOG(warning, #{
        msg => "save_session_inflight_messages_called_violates_plan_a",
        client_id => ClientId,
        session_pid => SessionPid,
        note => "this_function_should_not_be_called_in_pure_persistence_approach"
    }),

    try
        %% 获取会话的Inflight消息（违反方案A原则）
        case emqx_session:get_inflight_messages(SessionPid) of
            {ok, InflightMsgs} ->
                ?SLOG(info, #{
                    msg => "saving_session_inflight_messages_but_violates_plan_a",
                    client_id => ClientId,
                    count => length(InflightMsgs)
                }),

                %% 保存每个Inflight消息（违反方案A原则）
                lists:foreach(fun({PacketId, Message, State}) ->
                    store_inflight_message(ClientId, PacketId, Message, State)
                end, InflightMsgs);
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_get_session_inflight_messages",
                    client_id => ClientId,
                    session_pid => SessionPid,
                    reason => Reason
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_saving_session_inflight_messages",
                client_id => ClientId,
                session_pid => SessionPid,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 存储Inflight消息（带状态）
store_inflight_message(ClientId, PacketId, Message, State) ->
    try
        Collection = get_inflight_collection(),
        Now = erlang:system_time(millisecond),

        InflightDoc = #{
            <<"client_id">> => ClientId,
            <<"packet_id">> => PacketId,
            <<"message_id">> => emqx_message:id(Message),
            <<"topic">> => emqx_message:topic(Message),
            <<"payload">> => emqx_message:payload(Message),
            <<"qos">> => emqx_message:qos(Message),
            <<"state">> => State,
            <<"created_at">> => Now,
            <<"updated_at">> => Now,
            <<"expires_at">> => Now + get_inflight_expiry()
        },

        % 使用upsert操作
        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {upsert, Collection,
                                #{<<"client_id">> => ClientId, <<"packet_id">> => PacketId},
                                InflightDoc}) of
            {ok, _} ->
                ?SLOG(debug, #{
                    msg => "inflight_message_stored",
                    client_id => ClientId,
                    packet_id => PacketId,
                    state => State
                });
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_store_inflight_message",
                    client_id => ClientId,
                    packet_id => PacketId,
                    reason => Reason
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_storing_inflight_message",
                client_id => ClientId,
                packet_id => PacketId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 获取Inflight过期时间
get_inflight_expiry() ->
    Config = emqx_plugin_mongodb:read_config(),
    InflightConfig = maps:get(inflight_persistence, Config, #{}),
    maps:get(inflight_expiry, InflightConfig, 14400000). % 默认4小时

%%--------------------------------------------------------------------
%% 缺失的辅助函数
%%--------------------------------------------------------------------

%% @doc 清理客户端的Inflight消息
cleanup_client_inflight_messages(ClientId) ->
    try
        Collection = get_inflight_collection(),
        Filter = #{<<"client_id">> => ClientId},

        % 使用同步API进行删除操作
        case emqx_plugin_mongodb_api:delete_many(Collection, Filter) of
            {ok, DeletedCount} when is_integer(DeletedCount) ->
                ?SLOG(debug, #{
                    msg => "client_inflight_messages_cleaned",
                    client_id => ClientId,
                    deleted_count => DeletedCount
                });
            {ok, _Other} ->
                ?SLOG(debug, #{
                    msg => "client_inflight_messages_cleanup_completed",
                    client_id => ClientId
                });
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_cleanup_client_inflight_messages",
                    client_id => ClientId,
                    reason => Reason
                });
            {async_return, ok} ->
                % 异步删除已提交，但无法获取结果
                ?SLOG(info, #{
                    msg => "client_inflight_cleanup_async_submitted",
                    client_id => ClientId
                });
            Other ->
                ?SLOG(warning, #{
                    msg => "unexpected_cleanup_response",
                    client_id => ClientId,
                    response => Other
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_cleaning_client_inflight_messages",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 从会话信息中提取Inflight消息
extract_inflight_from_session(SessInfo) ->
    try
        % 尝试从会话信息中获取Inflight消息
        case maps:get(inflight, SessInfo, undefined) of
            undefined ->
                {ok, []};
            InflightData when is_map(InflightData) ->
                % 解析Inflight数据
                InflightList = maps:fold(fun(PacketId, {Message, State}, Acc) ->
                    [{PacketId, Message, State} | Acc]
                end, [], InflightData),
                {ok, InflightList};
            _ ->
                {ok, []}
        end
    catch
        E:R ->
            ?SLOG(error, #{
                msg => "error_extracting_inflight_from_session",
                error => E,
                reason => R
            }),
            {error, {E, R}}
    end.

%% @doc 存储Inflight消息（带状态）
store_inflight_message_with_state(ClientId, PacketId, Message, State) ->
    try
        Collection = get_inflight_collection(),
        Now = erlang:system_time(millisecond),

        InflightDoc = #{
            <<"client_id">> => ClientId,
            <<"packet_id">> => PacketId,
            <<"message_id">> => emqx_message:id(Message),
            <<"topic">> => emqx_message:topic(Message),
            <<"payload">> => emqx_message:payload(Message),
            <<"qos">> => emqx_message:qos(Message),
            <<"state">> => atom_to_binary(State, utf8),
            <<"created_at">> => Now,
            <<"updated_at">> => Now,
            <<"expires_at">> => Now + get_inflight_expiry()
        },

        % 使用upsert操作
        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {upsert, Collection,
                                #{<<"client_id">> => ClientId, <<"packet_id">> => PacketId},
                                InflightDoc}) of
            {ok, _} ->
                ?SLOG(debug, #{
                    msg => "inflight_message_stored_with_state",
                    client_id => ClientId,
                    packet_id => PacketId,
                    state => State
                });
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_store_inflight_message_with_state",
                    client_id => ClientId,
                    packet_id => PacketId,
                    reason => Reason
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_storing_inflight_message_with_state",
                client_id => ClientId,
                packet_id => PacketId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 恢复Inflight消息到会话
restore_inflight_message_to_session(ClientId, InflightDoc) ->
    try
        PacketId = maps:get(<<"packet_id">>, InflightDoc),
        Topic = maps:get(<<"topic">>, InflightDoc),
        Payload = maps:get(<<"payload">>, InflightDoc),
        QoS = maps:get(<<"qos">>, InflightDoc),
        State = maps:get(<<"state">>, InflightDoc),

        % 重建EMQX消息
        Message = emqx_message:make(ClientId, QoS, Topic, Payload),
        Message1 = emqx_message:set_header(packet_id, PacketId, Message),

        % 获取客户端的会话PID
        case emqx_cm:lookup_channels(ClientId) of
            [SessionPid | _] ->
                % 恢复到会话的Inflight窗口
                restore_to_session_inflight(SessionPid, PacketId, Message1);
            [] ->
                ?SLOG(warning, #{
                    msg => "no_session_found_for_inflight_restore",
                    client_id => ClientId,
                    packet_id => PacketId
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_inflight_message_to_session",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S,
                inflight_doc => InflightDoc
            })
    end.

%% @doc 判断是否为异常断开连接
is_abnormal_disconnect(Reason) ->
    case Reason of
        normal -> false;
        shutdown -> false;
        {shutdown, _} -> false;
        _ -> true
    end.

%% @doc 获取客户端会话信息
get_client_session_info(ClientId) ->
    try
        case emqx_cm:lookup_channels(ClientId) of
            [SessionPid | _] ->
                % 尝试获取会话信息
                case emqx_session:info(SessionPid) of
                    SessionInfo when is_map(SessionInfo) ->
                        {ok, SessionInfo};
                    _ ->
                        {error, session_info_not_available}
                end;
            [] ->
                {error, session_not_found}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_getting_client_session_info",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%%--------------------------------------------------------------------
%% MQTT 5.0消息属性解析函数
%%--------------------------------------------------------------------

%% @doc 提取Message Expiry Interval属性
%% MQTT 5.0协议：指定消息在发布后的有效期（秒）
extract_message_expiry_interval(Properties) when is_map(Properties) ->
    case maps:get('Message-Expiry-Interval', Properties,
                  maps:get(<<"Message-Expiry-Interval">>, Properties,
                          maps:get(message_expiry_interval, Properties, undefined))) of
        Interval when is_integer(Interval), Interval > 0 -> Interval;
        _ -> undefined
    end;
extract_message_expiry_interval(_) -> undefined.

%% @doc 提取Topic Alias属性
%% MQTT 5.0协议：主题别名，用于减少网络传输
extract_topic_alias(Properties) when is_map(Properties) ->
    case maps:get('Topic-Alias', Properties,
                  maps:get(<<"Topic-Alias">>, Properties,
                          maps:get(topic_alias, Properties, 0))) of
        Alias when is_integer(Alias), Alias > 0 -> Alias;
        _ -> 0
    end;
extract_topic_alias(_) -> 0.

%% @doc 提取Response Topic属性
%% MQTT 5.0协议：指定响应消息的主题
extract_response_topic(Properties) when is_map(Properties) ->
    case maps:get('Response-Topic', Properties,
                  maps:get(<<"Response-Topic">>, Properties,
                          maps:get(response_topic, Properties, <<>>))) of
        Topic when is_binary(Topic) -> Topic;
        Topic when is_list(Topic) -> list_to_binary(Topic);
        _ -> <<>>
    end;
extract_response_topic(_) -> <<>>.

%% @doc 提取Correlation Data属性
%% MQTT 5.0协议：用于请求/响应模式的关联数据
extract_correlation_data(Properties) when is_map(Properties) ->
    case maps:get('Correlation-Data', Properties,
                  maps:get(<<"Correlation-Data">>, Properties,
                          maps:get(correlation_data, Properties, <<>>))) of
        Data when is_binary(Data) -> Data;
        Data when is_list(Data) -> list_to_binary(Data);
        _ -> <<>>
    end;
extract_correlation_data(_) -> <<>>.

%% @doc 提取User Properties属性
%% MQTT 5.0协议：用户自定义属性键值对
extract_user_properties(Properties) when is_map(Properties) ->
    case maps:get('User-Property', Properties,
                  maps:get(<<"User-Property">>, Properties,
                          maps:get(user_properties, Properties, #{}))) of
        UserProps when is_map(UserProps) -> UserProps;
        _ -> #{}
    end;
extract_user_properties(_) -> #{}.

%% @doc 提取Content Type属性
%% MQTT 5.0协议：描述载荷内容的MIME类型
extract_content_type(Properties) when is_map(Properties) ->
    case maps:get('Content-Type', Properties,
                  maps:get(<<"Content-Type">>, Properties,
                          maps:get(content_type, Properties, <<>>))) of
        ContentType when is_binary(ContentType) -> ContentType;
        ContentType when is_list(ContentType) -> list_to_binary(ContentType);
        _ -> <<>>
    end;
extract_content_type(_) -> <<>>.

%% @doc 提取Payload Format Indicator属性
%% MQTT 5.0协议：指示载荷格式（0=字节，1=UTF-8字符串）
extract_payload_format_indicator(Properties) when is_map(Properties) ->
    case maps:get('Payload-Format-Indicator', Properties,
                  maps:get(<<"Payload-Format-Indicator">>, Properties,
                          maps:get(payload_format_indicator, Properties, 0))) of
        Indicator when Indicator =:= 0; Indicator =:= 1 -> Indicator;
        _ -> 0
    end;
extract_payload_format_indicator(_) -> 0.

%% @doc 提取Subscription Identifier属性
%% MQTT 5.0协议：订阅标识符，用于标识特定订阅
extract_subscription_identifier(Properties) when is_map(Properties) ->
    case maps:get('Subscription-Identifier', Properties,
                  maps:get(<<"Subscription-Identifier">>, Properties,
                          maps:get(subscription_identifier, Properties, 0))) of
        Identifier when is_integer(Identifier), Identifier > 0 -> Identifier;
        _ -> 0
    end;
extract_subscription_identifier(_) -> 0.

%% @doc 计算飞行中消息过期时间
%% 优先使用消息自身的Message Expiry Interval，否则使用插件配置
calculate_inflight_message_expiry_time(undefined) ->
    % 没有指定Message Expiry Interval，使用插件配置
    erlang:system_time(millisecond) + get_inflight_expiry();
calculate_inflight_message_expiry_time(MessageExpiryInterval) when is_integer(MessageExpiryInterval) ->
    % 使用消息指定的过期时间（转换为毫秒）
    erlang:system_time(millisecond) + (MessageExpiryInterval * 1000);
calculate_inflight_message_expiry_time(_) ->
    % 无效值，使用插件配置
    erlang:system_time(millisecond) + get_inflight_expiry().

%% @doc 确定消息方向和初始状态
%% 根据QoS级别确定消息的方向和初始状态
determine_message_direction_and_state(QoS) ->
    case QoS of
        0 ->
            % QoS 0消息不需要飞行中跟踪
            {<<"none">>, <<"completed">>};
        1 ->
            % QoS 1消息等待PUBACK确认
            {<<"outgoing">>, ?STATE_WAITING_ACK};
        2 ->
            % QoS 2消息等待PUBREC确认
            {<<"outgoing">>, ?STATE_WAITING_REL};
        _ ->
            % 无效QoS，默认处理
            {<<"unknown">>, <<"error">>}
    end.
