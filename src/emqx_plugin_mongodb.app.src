% EMQX MongoDB 插件应用程序配置文件
% 这是一个Erlang term（数据结构），描述了名为 `emqx_plugin_mongodb` 的OTP应用程序。
% 在构建时，rebar3 (Erlang构建工具) 会使用这个 `.app.src` 文件生成一个 `.app` 文件。
{
  application, % 关键字，表示这是一个应用程序定义
  emqx_plugin_mongodb, % 应用程序的名称 (原子 atom)
  [
    % 应用程序的描述，字符串类型。
    {description, "EMQX MongoDB Plugin."},
    % 应用程序的版本号，字符串类型。
    {vsn, "v5.4.0"},
    % `modules` 列表定义了此应用程序包含哪些Erlang模块。
    % 构建工具通常会自动填充这个列表。这里为空，表示构建时会自动发现。
    % 类似于Java中，一个库会包含多个编译后的 .class 文件。
    {modules, []},
    % `registered` 列表声明了此应用程序启动后会注册哪些具名进程。
    % `emqx_plugin_mongodb_sup` 是我们之前看到的监督者模块名，它在 `start_link/0` 中被注册为本地名称。
    % 这有助于其他部分代码查找和与这些核心进程交互。
    {registered, [emqx_plugin_mongodb_sup]},
    % `applications` 列表声明了此应用程序依赖的其他OTP应用程序。
    % `kernel` 和 `stdlib` 是所有Erlang系统都依赖的核心应用程序。
    % `mongodb` 是Erlang MongoDB驱动程序，是本插件的核心依赖。
    % 这类似于Java中Maven/Gradle的 `<dependencies>` 部分。
    {applications, [kernel, stdlib, sasl, mongodb]},
    % `mod` 指定了应用程序的回调模块和启动参数。
    % 当此应用程序启动时，OTP框架会调用 `emqx_plugin_mongodb_app:start/2` 函数。
    % `{emqx_plugin_mongodb_app, []}` 表示回调模块是 `emqx_plugin_mongodb_app`，传递给其 `start/2` 函数的第二个参数是空列表 `[]`。
    % `emqx_plugin_mongodb_app` 模块需要实现 `application` 行为。
    {mod, {emqx_plugin_mongodb_app, []}},
    % `env` 列表用于定义应用程序的环境变量（配置参数）。
    % 这些参数可以在启动时被覆盖。这里为空，表示没有默认的应用级环境变量。
    % 插件的配置通常通过 `emqx_plugin_mongodb.hocon` 文件管理。
    {env, []},
    % 许可证信息。
    {licenses, ["Apache-2.0"]},
    % 维护者信息。
    {maintainers, ["WXW"]}
  ]
}.
