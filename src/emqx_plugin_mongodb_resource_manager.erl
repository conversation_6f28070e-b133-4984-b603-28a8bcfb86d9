%%%-------------------------------------------------------------------
%%% @doc MongoDB插件的资源管理优化模块 - 企业级资源管理和自适应系统
%%% 这个模块是高级资源管理组件，提供智能的资源监控、优雅降级和动态调整能力
%%%
%%% 功能概述：
%%% 1. 资源使用监控 - 实时监控CPU、内存、连接数、队列长度等关键资源
%%% 2. 优雅降级策略 - 根据系统负载自动执行分级降级策略
%%% 3. 动态资源分配 - 根据实际负载动态调整连接池大小和资源配额
%%% 4. 智能阈值管理 - 支持多级阈值配置和自适应调整
%%% 5. 系统保护机制 - 防止系统过载和资源耗尽
%%% 6. 性能优化 - 根据系统状态自动优化性能参数
%%% 7. 监控告警 - 提供详细的资源使用统计和告警机制
%%%
%%% 降级策略层级：
%%% - Normal: 正常运行状态，所有功能可用
%%% - Light: 轻度降级，降低批处理大小和队列TTL
%%% - Moderate: 中度降级，拒绝低优先级消息，进一步降低资源使用
%%% - Severe: 重度降级，大幅减少资源使用，暂停非关键功能
%%% - Critical: 临界降级，只保留核心功能，最大程度保护系统
%%%
%%% 架构设计：
%%% - 监控驱动：基于实时监控数据驱动决策
%%% - 分级响应：多级降级策略，渐进式资源保护
%%% - 自动恢复：系统负载降低时自动恢复正常状态
%%% - 可配置性：支持自定义阈值和降级动作
%%% - 非侵入式：不影响业务逻辑的正常执行
%%%
%%% Java等价概念：
%%% 类似于Spring Boot的自动配置和监控系统：
%%% @Component
%%% @Service
%%% @ConditionalOnProperty(name = "resource.management.enabled", havingValue = "true")
%%% public class ResourceManager {
%%%     @Autowired private MeterRegistry meterRegistry;
%%%     @Autowired private CircuitBreakerRegistry circuitBreakerRegistry;
%%%     @Autowired private ConnectionPoolManager poolManager;
%%%
%%%     @EventListener
%%%     public void onResourceThresholdExceeded(ResourceThresholdEvent event) {
%%%         DegradationLevel level = calculateDegradationLevel(event);
%%%         applyDegradationStrategy(level);
%%%     }
%%%
%%%     @Scheduled(fixedDelay = 5000)
%%%     public void monitorResources() {
%%%         ResourceUsage usage = collectResourceUsage();
%%%         if (usage.exceedsThreshold()) {
%%%             triggerDegradation(usage);
%%%         }
%%%     }
%%%
%%%     private void applyDegradationStrategy(DegradationLevel level) {
%%%         switch (level) {
%%%             case LIGHT:
%%%                 reduceBatchSize(0.8);
%%%                 reduceQueueTTL(0.8);
%%%                 break;
%%%             case MODERATE:
%%%                 reduceBatchSize(0.5);
%%%                 rejectLowPriorityMessages(true);
%%%                 break;
%%%             case SEVERE:
%%%                 pauseNonCriticalFeatures();
%%%                 break;
%%%         }
%%%     }
%%% }
%%%
%%% 设计模式：
%%% - 策略模式：不同级别的降级策略
%%% - 观察者模式：资源状态变化的监听和响应
%%% - 状态模式：系统运行状态的管理
%%% - 适配器模式：不同资源监控接口的适配
%%% @end
%%%-------------------------------------------------------------------
-module(emqx_plugin_mongodb_resource_manager).

%% 实现OTP gen_server行为
%% 类似于Java中实现Runnable或Service接口
%% gen_server提供了标准的服务器进程模板，支持状态管理和消息处理
-behaviour(gen_server).

%% ============================================================================
%% 公共API函数 - 资源管理和控制接口
%% 这些函数提供资源管理的核心功能
%% 类似于Java中的Service层公共接口
%% ============================================================================
-export([
    start_link/1,                   % 启动资源管理器
                                   % 功能：启动gen_server进程，初始化资源管理系统
                                   % Java等价：@PostConstruct public void startResourceManager()

    get_resource_usage/0,           % 获取当前资源使用情况
                                   % 功能：获取CPU、内存、连接数等资源的实时使用情况
                                   % Java等价：public ResourceUsage getCurrentResourceUsage()

    get_resource_limits/0,          % 获取资源限制配置
                                   % 功能：获取当前配置的资源限制和阈值
                                   % Java等价：public ResourceLimits getResourceLimits()

    set_resource_limits/1,          % 设置资源限制配置
                                   % 功能：更新资源限制和降级阈值配置
                                   % Java等价：public void setResourceLimits(ResourceLimits limits)

    enable_graceful_degradation/1,  % 启用优雅降级
                                   % 功能：启用自动降级功能，配置降级策略
                                   % Java等价：public void enableGracefulDegradation(DegradationConfig config)

    disable_graceful_degradation/0, % 禁用优雅降级
                                   % 功能：禁用自动降级功能，恢复正常运行模式
                                   % Java等价：public void disableGracefulDegradation()

    adjust_connection_pool/1,       % 调整连接池大小
                                   % 功能：根据负载动态调整MongoDB连接池大小
                                   % Java等价：public void adjustConnectionPool(int targetSize)

    get_system_status/0,            % 获取系统状态
                                   % 功能：获取系统运行状态和降级级别
                                   % Java等价：public SystemStatus getSystemStatus()

    update_resource_limits/1,       % 更新资源限制
                                   % 功能：动态更新资源限制，不需要重启服务
                                   % Java等价：public void updateResourceLimits(ResourceLimits limits)

    apply_degradation/1,            % 应用降级策略
                                   % 功能：手动应用指定级别的降级策略
                                   % Java等价：public void applyDegradation(DegradationLevel level)

    integrate/0                     % 集成到系统协调器
                                   % 功能：将资源管理器注册到系统协调器
                                   % Java等价：@PostConstruct public void integrate()
]).

%% ============================================================================
%% gen_server回调函数 - OTP标准服务器行为实现
%% 这些函数实现gen_server行为的标准回调
%% 类似于Java中的生命周期回调方法
%% ============================================================================
-export([
    init/1,                         % 初始化回调
                                   % 功能：初始化服务器状态，启动监控工作进程
                                   % Java等价：@PostConstruct public void init()

    handle_call/3,                  % 同步调用处理
                                   % 功能：处理同步请求，如状态查询、配置更新
                                   % Java等价：public Response handleRequest(Request request)

    handle_cast/2,                  % 异步消息处理
                                   % 功能：处理异步消息，如资源状态更新
                                   % Java等价：@EventListener public void handleEvent(Event event)

    handle_info/2,                  % 系统消息处理
                                   % 功能：处理系统消息，如定时器、进程监控
                                   % Java等价：@Scheduled public void handleSystemEvent()

    terminate/2,                    % 终止回调
                                   % 功能：清理资源，保存状态
                                   % Java等价：@PreDestroy public void cleanup()

    code_change/3                   % 热代码升级回调
                                   % 功能：支持运行时代码升级
                                   % Java等价：类似于热部署机制
]).

%% ============================================================================
%% 内部工作函数 - 后台监控和处理逻辑
%% 这些函数实现资源管理的后台工作逻辑
%% 类似于Java中的后台服务线程
%% ============================================================================
-export([
    monitor_worker/0,               % 资源监控工作进程
                                   % 功能：定期监控系统资源使用情况
                                   % Java等价：@Scheduled public void monitorResources()

    degradation_worker/0            % 降级处理工作进程
                                   % 功能：执行降级策略和恢复操作
                                   % Java等价：@Async public void processDegradation()
]).

-include("emqx_plugin_mongodb.hrl").

%% ============================================================================
%% 数据结构定义 - 资源管理的核心数据结构
%% 这些记录定义了资源管理系统的状态和配置
%% 类似于Java中的实体类或数据传输对象（DTO）
%% ============================================================================

%% @doc 系统状态记录
%% 这个记录保存系统的实时运行状态和资源使用情况
%%
%% 功能说明：
%% 1. 记录各种资源的实时使用情况
%% 2. 跟踪当前的降级级别和历史
%% 3. 提供系统健康状态的完整视图
%%
%% Java等价概念：
%% public class SystemStatus {
%%     private double memoryUsage;        // 内存使用率 (0.0-1.0)
%%     private double cpuUsage;           // CPU使用率 (0.0-1.0)
%%     private int connectionCount;       // 当前连接数
%%     private int queueLength;           // 当前队列长度
%%     private DegradationLevel degradationLevel; // 当前降级级别
%%     private long lastCheckTime;        // 上次检查时间
%%     private List<DegradationEvent> degradationHistory; // 降级历史
%% }
-record(system_status, {
    memory_usage = 0.0,         % 内存使用率（0.0-1.0）
                               % 功能：表示当前内存使用的百分比
                               % Java等价：private double memoryUsage;

    cpu_usage = 0.0,           % CPU使用率（0.0-1.0）
                               % 功能：表示当前CPU使用的百分比
                               % Java等价：private double cpuUsage;

    connection_count = 0,      % 当前活跃连接数
                               % 功能：记录MongoDB连接池中的活跃连接数量
                               % Java等价：private int activeConnectionCount;

    queue_length = 0,          % 当前消息队列长度
                               % 功能：记录待处理消息队列的长度
                               % Java等价：private int queueLength;

    degradation_level = normal, % 当前降级级别
                               % 功能：表示系统当前的降级状态
                               % 可能值：normal, light, moderate, severe, critical
                               % Java等价：private DegradationLevel currentLevel;

    last_check_time = 0,       % 上次资源检查时间戳
                               % 功能：记录最后一次资源监控检查的时间
                               % Java等价：private long lastCheckTimestamp;

    degradation_history = []   % 降级历史记录列表
                               % 功能：保存最近的降级事件历史，用于分析和调试
                               % Java等价：private List<DegradationEvent> history;
}).

%% @doc 资源限制配置记录
%% 这个记录定义了系统的资源限制、降级阈值和降级动作
%%
%% 功能说明：
%% 1. 定义各种资源的使用上限
%% 2. 配置多级降级阈值
%% 3. 定义每个降级级别对应的具体动作
%% 4. 控制降级功能的启用状态
%%
%% Java等价概念：
%% @ConfigurationProperties(prefix = "resource.limits")
%% public class ResourceLimits {
%%     private double memoryLimit;
%%     private double cpuLimit;
%%     private int connectionLimit;
%%     private int queueLimit;
%%     private boolean degradationEnabled;
%%     private Map<DegradationLevel, ThresholdConfig> thresholds;
%%     private Map<DegradationLevel, List<DegradationAction>> actions;
%% }
-record(resource_limits, {
    memory_limit = ?DEFAULT_MEMORY_LIMIT,  % 内存使用上限
                                          % 功能：定义内存使用的最大允许值
                                          % Java等价：@Value("${resource.memory.limit}")

    cpu_limit = ?DEFAULT_CPU_LIMIT,        % CPU使用上限
                                          % 功能：定义CPU使用的最大允许值
                                          % Java等价：@Value("${resource.cpu.limit}")

    connection_limit = ?DEFAULT_CONNECTION_LIMIT, % 连接数上限
                                                 % 功能：定义MongoDB连接池的最大连接数
                                                 % Java等价：@Value("${resource.connection.limit}")

    queue_limit = ?DEFAULT_QUEUE_LIMIT,    % 队列长度上限
                                          % 功能：定义消息队列的最大长度
                                          % Java等价：@Value("${resource.queue.limit}")

    degradation_enabled = false,           % 降级功能启用标志
                                          % 功能：控制是否启用自动降级功能
                                          % Java等价：@Value("${resource.degradation.enabled:false}")

    degradation_thresholds = #{            % 多级降级阈值配置
                                          % 功能：定义不同降级级别的触发阈值
                                          % Java等价：Map<DegradationLevel, ThresholdConfig>
        light => #{                        % 轻度降级阈值（70%）
            memory => 0.7,                % 内存使用率达到70%时触发轻度降级
            cpu => 0.7,                   % CPU使用率达到70%时触发轻度降级
            connections => 0.7,           % 连接数达到70%时触发轻度降级
            queue => 0.7                  % 队列长度达到70%时触发轻度降级
        },
        moderate => #{                     % 中度降级阈值（80%）
            memory => 0.8,                % 内存使用率达到80%时触发中度降级
            cpu => 0.8,                   % CPU使用率达到80%时触发中度降级
            connections => 0.8,           % 连接数达到80%时触发中度降级
            queue => 0.8                  % 队列长度达到80%时触发中度降级
        },
        severe => #{                       % 重度降级阈值（90%）
            memory => 0.9,                % 内存使用率达到90%时触发重度降级
            cpu => 0.9,                   % CPU使用率达到90%时触发重度降级
            connections => 0.9,           % 连接数达到90%时触发重度降级
            queue => 0.9                  % 队列长度达到90%时触发重度降级
        },
        critical => #{                     % 临界降级阈值（95%）
            memory => 0.95,               % 内存使用率达到95%时触发临界降级
            cpu => 0.95,                  % CPU使用率达到95%时触发临界降级
            connections => 0.95,          % 连接数达到95%时触发临界降级
            queue => 0.95                 % 队列长度达到95%时触发临界降级
        }
    },
    degradation_actions = #{              % 降级动作配置
                                          % 功能：定义每个降级级别对应的具体动作
                                          % Java等价：Map<DegradationLevel, List<DegradationAction>>
        light => [                        % 轻度降级动作
            {batch_size, 0.8},            % 将批处理大小降低至80%
            {queue_ttl, 0.8}              % 将队列TTL降低至80%
        ],
        moderate => [                     % 中度降级动作
            {batch_size, 0.5},            % 将批处理大小降低至50%
            {queue_ttl, 0.5},             % 将队列TTL降低至50%
            {reject_low_priority, true}   % 开始拒绝低优先级消息
        ],
        severe => [                       % 重度降级动作
            {batch_size, 0.3},            % 将批处理大小降低至30%
            {queue_ttl, 0.3},             % 将队列TTL降低至30%
            {reject_low_priority, true},  % 拒绝低优先级消息
            {reject_medium_priority, true} % 拒绝中优先级消息
        ],
        critical => [                     % 临界降级动作
            {batch_size, 0.1},            % 将批处理大小降低至10%
            {queue_ttl, 0.1},             % 将队列TTL降低至10%
            {reject_low_priority, true},  % 拒绝低优先级消息
            {reject_medium_priority, true}, % 拒绝中优先级消息
            {persist_only, true}          % 只进行持久化，暂停其他处理
        ]
    }
}).

%%%===================================================================
%%% API
%%%===================================================================

%% @doc 启动资源管理服务
start_link(Options) ->
    gen_server:start_link({local, ?MODULE}, ?MODULE, [Options], []).

%% @doc 获取资源使用情况
get_resource_usage() ->
    gen_server:call(?MODULE, get_resource_usage).

%% @doc 获取资源限制
get_resource_limits() ->
    gen_server:call(?MODULE, get_resource_limits).

%% @doc 设置资源限制
set_resource_limits(Limits) ->
    gen_server:call(?MODULE, {set_resource_limits, Limits}).

%% @doc 启用优雅降级
enable_graceful_degradation(Options) ->
    gen_server:call(?MODULE, {enable_graceful_degradation, Options}).

%% @doc 禁用优雅降级
disable_graceful_degradation() ->
    gen_server:call(?MODULE, disable_graceful_degradation).

%% @doc 调整连接池大小
adjust_connection_pool(Size) ->
    gen_server:call(?MODULE, {adjust_connection_pool, Size}).

%% @doc 获取系统状态
get_system_status() ->
    gen_server:call(?MODULE, get_system_status).

%% @doc 更新资源限制
update_resource_limits(Limits) ->
    gen_server:call(?MODULE, {update_resource_limits, Limits}).

%% @doc 应用降级
apply_degradation(Options) ->
    gen_server:call(?MODULE, {apply_degradation, Options}).

%% @doc 集成到协调器
integrate() ->
    ?SLOG(info, #{msg => "integrating_resource_manager_module"}),
    % 将此模块注册到协调器
    case erlang:function_exported(emqx_plugin_mongodb_coordinator, register_module, 2) of
        true ->
            emqx_plugin_mongodb_coordinator:register_module(?MODULE, #{
                priority => high,
                description => <<"Resource management module">>,
                features => [resource_monitoring, graceful_degradation, dynamic_allocation]
            });
        false ->
            ok
    end.

%%%===================================================================
%%% gen_server callbacks
%%%===================================================================

%% @doc 初始化回调
init([Options]) ->
    % 创建ETS表
    ets:new(?RESOURCE_STATS, [named_table, public, {write_concurrency, true}]),
    ets:new(?RESOURCE_LIMITS, [named_table, public, {read_concurrency, true}]),

    % 初始化资源限制
    init_resource_limits(Options),

    % 初始化系统状态
    SystemStatus = #system_status{
        last_check_time = erlang:system_time(millisecond)
    },

    % 存储系统状态
    persistent_term:put(mongodb_system_status, SystemStatus),

    % 启动监控工作进程
    spawn_link(?MODULE, monitor_worker, []),

    % 启动降级工作进程
    spawn_link(?MODULE, degradation_worker, []),

    {ok, #{
        options => Options,
        system_status => SystemStatus
    }}.

%% @doc 处理同步调用
handle_call(get_resource_usage, _From, State) ->
    % 获取当前资源使用情况
    Usage = get_current_resource_usage(),
    {reply, {ok, Usage}, State};

handle_call(get_resource_limits, _From, State) ->
    % 获取资源限制
    [{_, Limits}] = ets:lookup(?RESOURCE_LIMITS, limits),
    {reply, {ok, Limits}, State};

handle_call({set_resource_limits, NewLimits}, _From, State) ->
    % 获取当前资源限制
    [{_, Limits}] = ets:lookup(?RESOURCE_LIMITS, limits),

    % 更新资源限制
    UpdatedLimits = Limits#resource_limits{
        memory_limit = maps:get(memory_limit, NewLimits, Limits#resource_limits.memory_limit),
        cpu_limit = maps:get(cpu_limit, NewLimits, Limits#resource_limits.cpu_limit),
        connection_limit = maps:get(connection_limit, NewLimits, Limits#resource_limits.connection_limit),
        queue_limit = maps:get(queue_limit, NewLimits, Limits#resource_limits.queue_limit)
    },

    % 保存更新后的资源限制
    ets:insert(?RESOURCE_LIMITS, {limits, UpdatedLimits}),

    {reply, ok, State};

handle_call({enable_graceful_degradation, Options}, _From, State) ->
    % 获取当前资源限制
    [{_, Limits}] = ets:lookup(?RESOURCE_LIMITS, limits),

    % 更新降级配置
    UpdatedLimits = Limits#resource_limits{
        degradation_enabled = true,
        degradation_thresholds = maps:get(thresholds, Options, Limits#resource_limits.degradation_thresholds),
        degradation_actions = maps:get(actions, Options, Limits#resource_limits.degradation_actions)
    },

    % 保存更新后的资源限制
    ets:insert(?RESOURCE_LIMITS, {limits, UpdatedLimits}),

    {reply, ok, State};

handle_call(disable_graceful_degradation, _From, State) ->
    % 获取当前资源限制
    [{_, Limits}] = ets:lookup(?RESOURCE_LIMITS, limits),

    % 禁用降级
    UpdatedLimits = Limits#resource_limits{
        degradation_enabled = false
    },

    % 保存更新后的资源限制
    ets:insert(?RESOURCE_LIMITS, {limits, UpdatedLimits}),

    % 重置系统状态
    SystemStatus = persistent_term:get(mongodb_system_status),
    NewSystemStatus = SystemStatus#system_status{
        degradation_level = normal
    },
    persistent_term:put(mongodb_system_status, NewSystemStatus),

    {reply, ok, State#{system_status => NewSystemStatus}};

handle_call({adjust_connection_pool, Size}, _From, State) ->
    % 调整连接池大小
    Result = try
        % 调用连接管理模块调整连接池大小
        adjust_pool_size(Size),
        ok
    catch
        _:Reason ->
            {error, Reason}
    end,

    {reply, Result, State};

handle_call(get_system_status, _From, State) ->
    % 获取系统状态
    SystemStatus = persistent_term:get(mongodb_system_status),
    {reply, {ok, SystemStatus}, State};

handle_call({update_resource_limits, NewLimits}, _From, State) ->
    % 获取当前资源限制
    [{_, Limits}] = ets:lookup(?RESOURCE_LIMITS, limits),

    % 更新资源限制
    UpdatedLimits = Limits#resource_limits{
        memory_limit = maps:get(memory_limit, NewLimits, Limits#resource_limits.memory_limit),
        cpu_limit = maps:get(cpu_limit, NewLimits, Limits#resource_limits.cpu_limit),
        connection_limit = maps:get(connection_limit, NewLimits, Limits#resource_limits.connection_limit),
        queue_limit = maps:get(queue_limit, NewLimits, Limits#resource_limits.queue_limit)
    },

    % 保存更新后的资源限制
    ets:insert(?RESOURCE_LIMITS, {limits, UpdatedLimits}),

    {reply, ok, State};

handle_call({apply_degradation, Options}, _From, State) ->
    % 应用降级
    Result = try
        % 调用降级逻辑
        apply_degradation_logic(Options),
        ok
    catch
        _:Reason ->
            {error, Reason}
    end,

    {reply, Result, State};

handle_call(_Request, _From, State) ->
    {reply, {error, unknown_call}, State}.

%% @doc 处理异步调用
handle_cast(_Msg, State) ->
    {noreply, State}.

%% @doc 处理消息
handle_info(_Info, State) ->
    {noreply, State}.

%% @doc 终止回调
terminate(_Reason, _State) ->
    % 删除ETS表
    catch ets:delete(?RESOURCE_STATS),
    catch ets:delete(?RESOURCE_LIMITS),
    ok.

%% @doc 代码更新回调
code_change(_OldVsn, State, _Extra) ->
    {ok, State}.

%%%===================================================================
%%% 内部函数
%%%===================================================================

%% @doc 初始化资源限制
init_resource_limits(Options) ->
    % 从选项中获取资源限制
    Limits = #resource_limits{
        memory_limit = maps:get(memory_limit, Options, ?DEFAULT_MEMORY_LIMIT),
        cpu_limit = maps:get(cpu_limit, Options, ?DEFAULT_CPU_LIMIT),
        connection_limit = maps:get(connection_limit, Options, ?DEFAULT_CONNECTION_LIMIT),
        queue_limit = maps:get(queue_limit, Options, ?DEFAULT_QUEUE_LIMIT),
        degradation_enabled = maps:get(degradation_enabled, Options, false)
    },

    % 保存资源限制
    ets:insert(?RESOURCE_LIMITS, {limits, Limits}).

%% @doc 获取当前资源使用情况
get_current_resource_usage() ->
    % 使用recon监控模块获取资源使用情况
    MemUsage = try
        emqx_plugin_mongodb_recon_monitor:get_memory_usage()
    catch
        _:_ ->
            ?SLOG(debug, #{msg => "recon_memory_monitoring_failed", action => "using_default_memory_usage"}),
            0.5  % 默认值
    end,

    % 获取CPU使用情况
    CpuUsage = try
        emqx_plugin_mongodb_recon_monitor:get_cpu_usage()
    catch
        _:_ ->
            ?SLOG(debug, #{msg => "recon_cpu_monitoring_failed", action => "using_default_cpu_usage"}),
            0.5  % 默认值
    end,

    % 获取连接数
    ConnectionCount = get_connection_count(),

    % 获取队列长度
    QueueLength = get_queue_length(),

    % 返回资源使用情况
    #{
        memory_usage => MemUsage,
        cpu_usage => CpuUsage,
        connection_count => ConnectionCount,
        queue_length => QueueLength,
        timestamp => erlang:system_time(millisecond)
    }.

%% @doc 获取连接数
get_connection_count() ->
    % 这里应该从连接管理模块获取实际连接数
    % 简化版实现，实际应该查询连接池状态
    try
        case whereis(emqx_plugin_mongodb_connection) of
            undefined -> 0;
            _ ->
                % 简化版，实际应该查询连接池
                8
        end
    catch
        _:_ -> 0
    end.

%% @doc 获取队列长度
get_queue_length() ->
    % 这里应该从队列管理模块获取实际队列长度
    % 简化版实现，实际应该查询各个队列的长度
    try
        % 检查批处理队列
        case whereis(emqx_plugin_mongodb_pipeline) of
            undefined -> 0;
            _ ->
                % 简化版，实际应该查询队列长度
                100
        end
    catch
        _:_ -> 0
    end.

%% @doc 监控工作进程
monitor_worker() ->
    % 定期监控资源使用情况
    monitor_loop().

%% @doc 监控循环
monitor_loop() ->
    % 获取当前资源使用情况，添加错误处理
    try
        Usage = get_current_resource_usage(),

        % 更新系统状态
        update_system_status(Usage),

        % 存储资源使用情况
        store_resource_usage(Usage)
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "resource_usage_monitoring_failed",
                error => E,
                reason => R,
                stacktrace => S
            }),
            % 使用默认值
            DefaultUsage = #{
                memory_usage => 0.5,
                cpu_usage => 0.5,
                connection_count => 0,
                queue_length => 0,
                timestamp => erlang:system_time(millisecond)
            },
            % 更新系统状态
            update_system_status(DefaultUsage),
            % 存储默认资源使用情况
            store_resource_usage(DefaultUsage)
    end,

    % 等待下一个监控周期
    timer:sleep(?MONITOR_INTERVAL),

    % 继续循环
    monitor_loop().

%% @doc 存储资源使用情况
store_resource_usage(Usage) ->
    % 获取当前时间
    Now = erlang:system_time(millisecond),

    % 存储资源使用情况
    ets:insert(?RESOURCE_STATS, {Now, Usage}).

%% @doc 更新系统状态
update_system_status(Usage) ->
    % 获取当前系统状态
    SystemStatus = persistent_term:get(mongodb_system_status),

    % 更新系统状态
    NewSystemStatus = SystemStatus#system_status{
        memory_usage = maps:get(memory_usage, Usage),
        cpu_usage = maps:get(cpu_usage, Usage),
        connection_count = maps:get(connection_count, Usage),
        queue_length = maps:get(queue_length, Usage),
        last_check_time = maps:get(timestamp, Usage)
    },

    % 存储更新后的系统状态
    persistent_term:put(mongodb_system_status, NewSystemStatus).

%% @doc 降级工作进程
degradation_worker() ->
    % 定期检查是否需要降级
    degradation_loop().

%% @doc 降级循环
degradation_loop() ->
    % 获取当前资源限制
    [{_, Limits}] = ets:lookup(?RESOURCE_LIMITS, limits),

    % 检查是否启用降级
    case Limits#resource_limits.degradation_enabled of
        true ->
            % 获取当前系统状态
            SystemStatus = persistent_term:get(mongodb_system_status),

            % 检查是否需要降级
            check_and_apply_degradation(SystemStatus, Limits);
        false ->
            % 未启用降级
            ok
    end,

    % 等待下一个降级检查周期
    timer:sleep(?DEGRADATION_CHECK_INTERVAL),

    % 继续循环
    degradation_loop().

%% @doc 检查并应用降级策略
check_and_apply_degradation(SystemStatus, Limits) ->
    % 计算当前降级级别
    NewLevel = calculate_degradation_level(SystemStatus, Limits),

    % 获取当前降级级别
    CurrentLevel = SystemStatus#system_status.degradation_level,

    % 如果级别变化，应用新的降级策略
    if
        NewLevel =/= CurrentLevel ->
            % 应用降级策略
            apply_degradation_actions(NewLevel, Limits),

            % 更新系统状态
            Now = erlang:system_time(millisecond),
            NewHistory = [{Now, NewLevel} | SystemStatus#system_status.degradation_history],
            % 限制历史记录大小
            TrimmedHistory = lists:sublist(NewHistory, 100),

            NewSystemStatus = SystemStatus#system_status{
                degradation_level = NewLevel,
                degradation_history = TrimmedHistory
            },

            % 存储更新后的系统状态
            persistent_term:put(mongodb_system_status, NewSystemStatus),

            % 记录降级事件
            ?SLOG(warning, #{
                msg => "mongodb_degradation_level_changed",
                from => CurrentLevel,
                to => NewLevel,
                memory_usage => SystemStatus#system_status.memory_usage,
                cpu_usage => SystemStatus#system_status.cpu_usage
            });
        true ->
            % 级别未变化，不做任何操作
            ok
    end.

%% @doc 计算降级级别
calculate_degradation_level(SystemStatus, Limits) ->
    % 获取各个资源使用率
    MemoryUsage = SystemStatus#system_status.memory_usage,
    CpuUsage = SystemStatus#system_status.cpu_usage,

    % 安全计算连接和队列比率，防止除零错误
    ConnectionRatio = case Limits#resource_limits.connection_limit of
        0 -> 0.0;  % 防止除零
        ConnLimit when is_number(ConnLimit) andalso ConnLimit > 0 ->
            SystemStatus#system_status.connection_count / ConnLimit;
        _ -> 0.0  % 其他异常情况
    end,

    QueueRatio = case Limits#resource_limits.queue_limit of
        0 -> 0.0;  % 防止除零
        QLimit when is_number(QLimit) andalso QLimit > 0 ->
            SystemStatus#system_status.queue_length / QLimit;
        _ -> 0.0  % 其他异常情况
    end,

    % 获取降级阈值
    Thresholds = Limits#resource_limits.degradation_thresholds,

    % 检查是否达到临界级别 (添加默认值处理)
    CriticalThresholds = maps:get(critical, Thresholds, #{}),
    CriticalMemory = maps:get(memory, CriticalThresholds, 0.95),
    CriticalCpu = maps:get(cpu, CriticalThresholds, 0.95),
    CriticalConnections = maps:get(connections, CriticalThresholds, 0.95),
    CriticalQueue = maps:get(queue, CriticalThresholds, 0.95),

    if
        MemoryUsage >= CriticalMemory orelse
        CpuUsage >= CriticalCpu orelse
        ConnectionRatio >= CriticalConnections orelse
        QueueRatio >= CriticalQueue ->
            critical;
        true ->
            % 检查是否达到严重级别 (添加默认值处理)
            SevereThresholds = maps:get(severe, Thresholds, #{}),
            SevereMemory = maps:get(memory, SevereThresholds, 0.9),
            SevereCpu = maps:get(cpu, SevereThresholds, 0.9),
            SevereConnections = maps:get(connections, SevereThresholds, 0.9),
            SevereQueue = maps:get(queue, SevereThresholds, 0.9),

            if
                MemoryUsage >= SevereMemory orelse
                CpuUsage >= SevereCpu orelse
                ConnectionRatio >= SevereConnections orelse
                QueueRatio >= SevereQueue ->
                    severe;
                true ->
                    % 检查是否达到中度级别 (添加默认值处理)
                    ModerateThresholds = maps:get(moderate, Thresholds, #{}),
                    ModerateMemory = maps:get(memory, ModerateThresholds, 0.8),
                    ModerateCpu = maps:get(cpu, ModerateThresholds, 0.8),
                    ModerateConnections = maps:get(connections, ModerateThresholds, 0.8),
                    ModerateQueue = maps:get(queue, ModerateThresholds, 0.8),

                    if
                        MemoryUsage >= ModerateMemory orelse
                        CpuUsage >= ModerateCpu orelse
                        ConnectionRatio >= ModerateConnections orelse
                        QueueRatio >= ModerateQueue ->
                            moderate;
                        true ->
                            % 检查是否达到轻度级别 (添加默认值处理)
                            LightThresholds = maps:get(light, Thresholds, #{}),
                            LightMemory = maps:get(memory, LightThresholds, 0.7),
                            LightCpu = maps:get(cpu, LightThresholds, 0.7),
                            LightConnections = maps:get(connections, LightThresholds, 0.7),
                            LightQueue = maps:get(queue, LightThresholds, 0.7),

                            if
                                MemoryUsage >= LightMemory orelse
                                CpuUsage >= LightCpu orelse
                                ConnectionRatio >= LightConnections orelse
                                QueueRatio >= LightQueue ->
                                    light;
                                true ->
                                    % 未达到任何降级阈值
                                    normal
                            end
                    end
            end
    end.

%% @doc 应用降级策略
apply_degradation_actions(Level, Limits) ->
    % 如果是正常级别，恢复默认设置
    case Level of
        normal ->
            % 恢复默认设置
            restore_default_settings();
        _ ->
            % 获取降级动作 (添加错误处理)
            try
                Actions = maps:get(Level, Limits#resource_limits.degradation_actions, []),

                % 应用降级动作
                apply_actions(Actions)
            catch
                Exception:Reason:Stack ->
                    ?SLOG(error, #{
                        msg => "failed_to_apply_degradation_actions",
                        level => Level,
                        exception => Exception,
                        reason => Reason,
                        stacktrace => Stack
                    }),
                    % 应用默认动作
                    apply_default_actions(Level)
            end
    end.

%% @doc 应用默认降级动作
apply_default_actions(Level) ->
    % 根据级别应用默认动作
    DefaultActions = case Level of
        light -> [{batch_size, 0.8}, {queue_ttl, 0.8}];
        moderate -> [{batch_size, 0.5}, {queue_ttl, 0.5}, {reject_low_priority, true}];
        severe -> [{batch_size, 0.3}, {queue_ttl, 0.3}, {reject_low_priority, true}, {reject_medium_priority, true}];
        critical -> [{batch_size, 0.1}, {queue_ttl, 0.1}, {reject_low_priority, true}, {reject_medium_priority, true}, {persist_only, true}];
        _ -> []
    end,

    % 应用默认动作
    apply_actions(DefaultActions).

%% @doc 恢复默认设置
restore_default_settings() ->
    % 恢复批处理大小
    adjust_batch_size(1.0),

    % 恢复队列TTL
    adjust_queue_ttl(1.0),

    % 恢复消息处理策略
    set_message_processing_policy(#{
        reject_low_priority => false,
        reject_medium_priority => false,
        persist_only => false
    }).

%% @doc 应用降级动作
apply_actions(Actions) ->
    % 应用每个动作
    lists:foreach(
        fun({Action, Value}) ->
            apply_single_action(Action, Value)
        end,
        Actions
    ).

%% @doc 应用单个降级动作
apply_single_action(batch_size, Ratio) ->
    % 调整批处理大小
    adjust_batch_size(Ratio);
apply_single_action(queue_ttl, Ratio) ->
    % 调整队列TTL
    adjust_queue_ttl(Ratio);
apply_single_action(reject_low_priority, true) ->
    % 拒绝低优先级消息
    set_message_processing_policy(#{reject_low_priority => true});
apply_single_action(reject_medium_priority, true) ->
    % 拒绝中优先级消息
    set_message_policy(#{reject_medium_priority => true});
apply_single_action(persist_only, true) ->
    % 只持久化不处理
    set_message_policy(#{persist_only => true});
apply_single_action(_, _) ->
    % 忽略未知动作
    ok.

%% @doc 调整批处理大小
adjust_batch_size(Ratio) ->
    % 这里应该调用批处理模块的接口
    % 简化版实现，实际应该调用实际的接口
    try
        case whereis(emqx_plugin_mongodb_adaptive_batch) of
            undefined -> ok;
            _ ->
                % 调整批处理大小
                DefaultSize = 1000,
                NewSize = trunc(DefaultSize * Ratio),
                % 调用批处理模块的接口
                % 实际应该调用 emqx_plugin_mongodb_adaptive_batch:set_batch_size(NewSize)
                ?SLOG(info, #{
                    msg => "adjust_batch_size",
                    ratio => Ratio,
                    new_size => NewSize
                }),
                ok
        end
    catch
        _:Reason ->
            ?SLOG(error, #{
                msg => "failed_to_adjust_batch_size",
                ratio => Ratio,
                reason => Reason
            }),
            {error, Reason}
    end.

%% @doc 调整队列TTL
adjust_queue_ttl(Ratio) ->
    % 这里应该调用队列管理模块的接口
    % 简化版实现，实际应该调用实际的接口
    try
        DefaultTTL = 60000, % 60秒
        NewTTL = trunc(DefaultTTL * Ratio),
        % 调用队列管理模块的接口
        % 实际应该调用 emqx_plugin_mongodb_queue:set_ttl(NewTTL)
        ?SLOG(info, #{
            msg => "adjust_queue_ttl",
            ratio => Ratio,
            new_ttl => NewTTL
        }),
        ok
    catch
        _:Reason ->
            ?SLOG(error, #{
                msg => "failed_to_adjust_queue_ttl",
                ratio => Ratio,
                reason => Reason
            }),
            {error, Reason}
    end.

%% @doc 设置消息处理策略
set_message_processing_policy(Policy) ->
    % 这里应该调用消息处理模块的接口
    % 简化版实现，实际应该调用实际的接口
    try
        % 调用消息处理模块的接口
        % 实际应该调用 emqx_plugin_mongodb:set_message_policy(Policy)
        ?SLOG(info, #{
            msg => "set_message_processing_policy",
            policy => Policy
        }),
        % 存储策略
        persistent_term:put(mongodb_message_policy, Policy),
        ok
    catch
        _:Reason ->
            ?SLOG(error, #{
                msg => "failed_to_set_message_policy",
                policy => Policy,
                reason => Reason
            }),
            {error, Reason}
    end.

%% @doc 调整连接池大小
adjust_pool_size(Size) ->
    % 这里应该调用连接管理模块的接口
    % 简化版实现，实际应该调用实际的接口
    try
        % 调用连接管理模块的接口
        % 实际应该调用 emqx_plugin_mongodb_connection:adjust_pool_size(Size)
        ?SLOG(info, #{
            msg => "adjust_connection_pool_size",
            new_size => Size
        }),
        ok
    catch
        _:Reason ->
            ?SLOG(error, #{
                msg => "failed_to_adjust_pool_size",
                size => Size,
                reason => Reason
            }),
            {error, Reason}
    end.

%% @doc 应用降级逻辑
apply_degradation_logic(Options) ->
    % 获取降级级别
    Level = maps:get(level, Options, moderate),

    % 获取降级动作
    Actions = get_degradation_actions(Level),

    % 应用降级动作
    lists:foreach(fun(Action) ->
        apply_degradation_action(Action)
    end, Actions),

    % 记录降级事件
    ?SLOG(warning, #{
        msg => "applied_graceful_degradation",
        level => Level,
        actions => Actions
    }),
    ok.

%% @doc 设置消息处理策略
set_message_policy(Policy) ->
    % 获取当前策略
    CurrentPolicy = case ets:lookup(?RESOURCE_STATS, message_policy) of
        [{_, ExistingPolicy}] -> ExistingPolicy;
        [] -> #{}
    end,

    % 合并新策略
    UpdatedPolicy = maps:merge(CurrentPolicy, Policy),

    % 保存更新后的策略
    ets:insert(?RESOURCE_STATS, {message_policy, UpdatedPolicy}),

    % 记录策略变更
    ?SLOG(info, #{
        msg => "message_policy_updated",
        policy => UpdatedPolicy
    }),
    ok.

%% @doc 获取降级动作
get_degradation_actions(Level) ->
    % 从ETS表中获取降级动作配置
    case ets:lookup(?RESOURCE_LIMITS, actions) of
        [{_, Actions}] ->
            % 获取指定级别的动作
            maps:get(Level, Actions, []);
        [] ->
            % 使用默认动作
            case Level of
                light -> [{batch_size, 0.8}, {queue_ttl, 0.8}];
                moderate -> [{batch_size, 0.5}, {queue_ttl, 0.5}, {reject_low_priority, true}];
                severe -> [{batch_size, 0.3}, {queue_ttl, 0.3}, {reject_low_priority, true}, {reject_medium_priority, true}];
                critical -> [{batch_size, 0.1}, {queue_ttl, 0.1}, {reject_low_priority, true}, {reject_medium_priority, true}, {persist_only, true}];
                _ -> []
            end
    end.

%% @doc 应用单个降级动作
apply_degradation_action({Action, Value}) ->
    apply_single_action(Action, Value);
apply_degradation_action(Action) when is_atom(Action) ->
    apply_single_action(Action, true);
apply_degradation_action(_) ->
    ok.
