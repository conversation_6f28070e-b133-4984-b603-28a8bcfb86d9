%% @doc MongoDB插件模块协调器集成
%% 这个模块负责将所有增强模块集成到协调器中，实现模块间的统一协调
%%
%% 功能概述：
%% 1. 统一管理所有MongoDB插件的增强模块
%% 2. 提供安全的模块集成机制，避免单个模块失败影响整体
%% 3. 实现模块间的协调和通信
%% 4. 支持动态模块集成和错误恢复
%%
%% 架构设计：
%% - 采用协调器模式，统一管理多个功能模块
%% - 使用安全集成策略，单个模块失败不影响其他模块
%% - 提供详细的日志记录，便于调试和监控
%%
%% Java等价概念：
%% 类似于Spring Framework的ApplicationContext
%% 或者OSGi的Bundle管理器
%% 或者微服务架构中的服务注册中心
-module(emqx_plugin_mongodb_coordinator_integration).

-include("emqx_plugin_mongodb.hrl").

%% API导出
%% 提供模块集成的公共接口
-export([
    integrate_all_modules/0    %% 集成所有模块到协调器
]).

%% @doc 集成所有模块到协调器
%% 这个函数负责将所有MongoDB插件的增强模块集成到协调器中
%%
%% 功能说明：
%% 1. 检查协调器进程是否正在运行
%% 2. 按顺序集成所有功能模块
%% 3. 提供完整的错误处理和日志记录
%% 4. 确保单个模块失败不影响整体集成
%%
%% 返回值：
%% - ok: 集成成功或协调器未运行
%% - {error, {integration_failed, Reason}}: 集成过程中发生严重错误
%%
%% Java等价概念：
%% 类似于Spring ApplicationContext的refresh()方法
%% 或者OSGi Bundle的start()方法
%% 或者微服务的服务注册过程
%%
%% 集成的模块列表：
%% 1. circuit_breaker: 熔断器模块，提供故障保护
%% 2. error_handler: 错误处理模块，统一错误管理
%% 3. resource_manager: 资源管理模块，管理连接池等资源
%% 4. backpressure: 背压机制模块，防止系统过载
%% 5. adaptive_batch: 自适应批处理模块，优化批量操作
%% 6. pipeline: 并行处理管道模块，提高并发性能
%% 7. zero_copy: 零拷贝模块，优化内存使用
%% 8. connection_manager: 连接管理模块，管理MongoDB连接
%% 9. session_manager: 会话管理模块，处理MQTT会话持久化
integrate_all_modules() ->
    %% 记录集成开始的日志
    %% 在Java中相当于：logger.info("Starting module integration");
    ?SLOG(info, #{msg => "integrating_all_modules_to_coordinator"}),

    %% 使用try-catch包装整个集成过程，确保异常被正确处理
    %% 在Java中相当于：
    %% try {
    %%     integrateAllModules();
    %% } catch (Exception e) {
    %%     logger.error("Integration failed", e);
    %% }
    try
        %% 检查协调器进程是否正在运行
        %% erlang:whereis/1 类似于Java中的进程管理器查找
        %% 在Java中相当于：
        %% if (processManager.isRunning("coordinator")) {
        %%     integrateModules();
        %% }
        case erlang:whereis(emqx_plugin_mongodb_coordinator) of
            undefined ->
                %% 协调器未运行，记录警告并跳过集成
                %% 这是一种优雅降级策略
                ?SLOG(warning, #{msg => "coordinator_not_running_skipping_integration"}),
                ok;
            _ ->
                %% 协调器正在运行，开始集成各个增强模块
                %% 按照依赖关系和重要性顺序进行集成

                %% 1. 集成熔断器模块 - 提供故障保护机制
                integrate_circuit_breaker(),

                %% 2. 集成错误处理模块 - 统一错误管理和恢复
                integrate_error_handler(),

                %% 3. 集成资源管理模块 - 管理连接池、内存等资源
                integrate_resource_manager(),

                %% 4. 集成背压机制模块 - 防止系统过载
                integrate_backpressure(),

                %% 5. 集成自适应批处理模块 - 优化批量操作性能
                integrate_adaptive_batch(),

                %% 6. 集成并行处理管道模块 - 提高并发处理能力
                integrate_pipeline(),

                %% 7. 集成零拷贝模块 - 优化内存使用和性能
                integrate_zero_copy(),

                %% 8. 集成连接管理模块 - 管理MongoDB连接生命周期
                integrate_connection_manager(),

                %% 9. 集成会话管理模块 - 处理MQTT会话持久化
                integrate_session_manager(),

                %% 记录所有模块集成成功的日志
                ?SLOG(info, #{msg => "all_modules_integrated_successfully"}),
                ok
        end
    catch
        %% 捕获集成过程中的所有异常
        %% E: 异常类别（error、exit、throw）
        %% R: 异常原因
        %% S: 调用栈信息
        E:R:S ->
            %% 记录详细的错误信息，包含完整的上下文
            ?SLOG(error, #{
                msg => "failed_to_integrate_modules",
                error => E,                    %% 异常类别
                reason => R,                   %% 异常原因
                stacktrace => S                %% 调用栈，用于调试
            }),
            %% 返回结构化的错误信息
            {error, {integration_failed, R}}
    end.

%% @private 集成熔断器模块（私有函数）
%% 这个函数负责将熔断器模块集成到协调器中
%%
%% 功能说明：
%% 熔断器模块提供故障保护机制，防止级联故障
%% 当MongoDB连接出现问题时，自动切断请求，保护系统稳定性
%%
%% Java等价概念：
%% 类似于Netflix Hystrix的熔断器
%% 或者Spring Cloud Circuit Breaker
integrate_circuit_breaker() ->
    %% 使用安全集成函数，确保单个模块失败不影响整体
    safe_integrate(emqx_plugin_mongodb_circuit_breaker).

%% @private 集成错误处理模块（私有函数）
%% 这个函数负责将错误处理模块集成到协调器中
%%
%% 功能说明：
%% 错误处理模块提供统一的错误管理和恢复机制
%% 包括错误分类、重试策略、错误恢复等功能
%%
%% Java等价概念：
%% 类似于Spring的@ExceptionHandler
%% 或者全局异常处理器
integrate_error_handler() ->
    safe_integrate(emqx_plugin_mongodb_error_handler).

%% @private 集成资源管理模块（私有函数）
%% 这个函数负责将资源管理模块集成到协调器中
%%
%% 功能说明：
%% 资源管理模块负责管理系统资源，如连接池、内存、线程等
%% 提供资源的分配、回收、监控等功能
%%
%% Java等价概念：
%% 类似于Spring的ResourceManager
%% 或者Apache Commons Pool的连接池管理
integrate_resource_manager() ->
    safe_integrate(emqx_plugin_mongodb_resource_manager).

%% @private 集成背压机制模块（私有函数）
%% 这个函数负责将背压机制模块集成到协调器中
%%
%% 功能说明：
%% 背压机制模块防止系统过载，当处理能力不足时主动限流
%% 保护下游系统不被过多请求压垮
%%
%% Java等价概念：
%% 类似于RxJava的Backpressure
%% 或者Akka Streams的背压机制
integrate_backpressure() ->
    safe_integrate(emqx_plugin_mongodb_backpressure).

%% @private 集成自适应批处理模块（私有函数）
%% 这个函数负责将自适应批处理模块集成到协调器中
%%
%% 功能说明：
%% 自适应批处理模块根据系统负载动态调整批处理大小
%% 在高负载时增加批处理大小，在低负载时减少批处理大小
%%
%% Java等价概念：
%% 类似于Spring Batch的批处理
%% 或者自定义的动态批处理器
integrate_adaptive_batch() ->
    safe_integrate(emqx_plugin_mongodb_adaptive_batch).

%% @private 集成并行处理管道模块（私有函数）
%% 这个函数负责将并行处理管道模块集成到协调器中
%%
%% 功能说明：
%% 并行处理管道模块提供多阶段并行处理能力
%% 将复杂的处理流程分解为多个阶段，并行执行以提高性能
%%
%% Java等价概念：
%% 类似于Java 8的Stream并行处理
%% 或者CompletableFuture的管道处理
integrate_pipeline() ->
    safe_integrate(emqx_plugin_mongodb_pipeline).

%% @private 集成零拷贝模块（私有函数）
%% 这个函数负责将零拷贝模块集成到协调器中
%%
%% 功能说明：
%% 零拷贝模块优化内存使用，减少不必要的数据拷贝
%% 提高大数据量处理的性能，降低内存占用
%%
%% Java等价概念：
%% 类似于Java NIO的零拷贝
%% 或者Netty的零拷贝机制
integrate_zero_copy() ->
    safe_integrate(emqx_plugin_mongodb_zero_copy).

%% @private 集成连接管理模块（私有函数）
%% 这个函数负责将连接管理模块集成到协调器中
%%
%% 功能说明：
%% 连接管理模块负责MongoDB连接的生命周期管理
%% 包括连接创建、维护、监控、回收等功能
%%
%% Java等价概念：
%% 类似于HikariCP连接池
%% 或者Spring Data MongoDB的连接管理
integrate_connection_manager() ->
    safe_integrate(emqx_plugin_mongodb_connection).

%% @private 集成会话管理模块（私有函数）
%% 这个函数负责将会话管理模块集成到协调器中
%%
%% 功能说明：
%% 会话管理模块处理MQTT会话的持久化
%% 包括会话状态保存、恢复、清理等功能
%%
%% Java等价概念：
%% 类似于Spring Session的会话管理
%% 或者Redis Session的持久化机制
integrate_session_manager() ->
    safe_integrate(emqx_plugin_mongodb_session).

%% @private 安全集成模块（私有函数）
%% 这个函数提供安全的模块集成机制，确保单个模块失败不影响整体集成
%%
%% 功能说明：
%% 1. 检查目标模块是否导出integrate/0函数
%% 2. 如果存在，调用模块的integrate函数进行集成
%% 3. 如果不存在，记录调试信息并继续
%% 4. 捕获所有异常，记录警告但不中断整体流程
%%
%% 参数说明：
%% - Module: 要集成的模块名（原子类型）
%%
%% 返回值：
%% - ok: 总是返回ok，无论集成成功还是失败
%%
%% Java等价概念：
%% 类似于Spring的@ConditionalOnBean注解
%% 或者OSGi的可选依赖机制
%% 或者插件系统的安全加载机制
%%
%% 设计原则：
%% - 容错优先：单个模块失败不影响其他模块
%% - 优雅降级：缺少集成函数时继续执行
%% - 详细日志：记录所有集成状态便于调试
safe_integrate(Module) ->
    try
        %% 检查模块是否导出integrate/0函数
        %% erlang:function_exported/3 类似于Java的反射机制
        %% 在Java中相当于：
        %% if (module.getClass().getMethod("integrate") != null) {
        %%     module.integrate();
        %% }
        case erlang:function_exported(Module, integrate, 0) of
            true ->
                %% 模块导出了integrate函数，调用它进行集成
                %% 在Java中相当于：module.integrate();
                Module:integrate(),

                %% 记录模块集成成功的调试日志
                %% 使用debug级别，避免在生产环境产生过多日志
                ?SLOG(debug, #{msg => "module_integrated", module => Module}),
                ok;
            false ->
                %% 模块没有导出integrate函数，这是正常情况
                %% 不是所有模块都需要集成到协调器中
                %% 记录调试信息，表明模块被跳过
                ?SLOG(debug, #{msg => "module_has_no_integrate_function", module => Module}),
                ok
        end
    catch
        %% 捕获集成过程中的所有异常
        %% 这是容错设计的核心，确保单个模块失败不影响整体
        %% E: 异常类别（error、exit、throw）
        %% R: 异常原因
        %% S: 调用栈信息
        E:R:S ->
            %% 记录模块集成失败的警告日志
            %% 包含完整的错误上下文信息，便于调试
            %% 在Java中相当于：
            %% logger.warn("Failed to integrate module: " + module, exception);
            ?SLOG(warning, #{
                msg => "failed_to_integrate_module",
                module => Module,              %% 失败的模块名
                error => E,                    %% 异常类别
                reason => R,                   %% 异常原因
                stacktrace => S                %% 调用栈，用于调试
            }),
            %% 返回ok，忽略单个模块集成失败
            %% 这是一种容错策略：继续集成其他模块
            %% 在Java中相当于：
            %% try {
            %%     module.integrate();
            %% } catch (Exception e) {
            %%     logger.warn("Module integration failed, continuing...", e);
            %% }
            ok
    end.
