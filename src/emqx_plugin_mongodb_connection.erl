%%%-------------------------------------------------------------------
%%% @doc
%%% MongoDB插件的连接管理模块
%%%
%%% 这个模块是MongoDB连接管理的核心，负责连接池的创建、管理和监控
%%% 提供高可用的MongoDB连接服务，支持连接池、健康检查、故障转移等功能
%%%
%%% 功能特性：
%%% 1. 连接池管理：支持多个连接池，每个池对应不同的分片或副本集
%%% 2. 健康检查：实时监控连接状态，自动剔除不健康的连接
%%% 3. 故障转移：支持主从切换和副本集故障转移
%%% 4. 连接预热：启动时预先建立连接，减少首次请求延迟
%%% 5. 负载均衡：根据连接健康度和响应时间进行负载分配
%%% 6. 监控统计：提供连接使用情况和性能指标
%%%
%%% Java等价概念：
%%% - 类似于HikariCP连接池管理器
%%% - 类似于Apache Commons DBCP的连接池
%%% - 类似于Spring Boot的DataSource配置
%%% - 类似于MongoDB Java Driver的连接管理
%%% - 类似于Tomcat的连接池监控
%%%
%%% 架构设计：
%%% - 使用gen_server实现状态管理和并发控制
%%% - 使用ETS表存储连接健康信息，支持高并发读取
%%% - 使用监控器（monitor）跟踪连接进程生命周期
%%% - 使用定时器实现定期健康检查
%%%
%%% @end
%%%-------------------------------------------------------------------
-module(emqx_plugin_mongodb_connection).

%% 实现gen_server行为，提供状态管理和并发控制
%% 类似于Java中实现Runnable接口或继承Thread类
-behaviour(gen_server).

%% API - 公共接口函数
%% 这些函数提供连接管理的主要功能，类似于Java中的public方法
-export([
    start_link/1,               % 启动连接管理服务，类似于DataSource的初始化
    get_connection/1,           % 获取连接，类似于DataSource.getConnection()
    release_connection/2,       % 释放连接，类似于Connection.close()
    update_connection_health/2, % 更新连接健康状态，类似于连接池的健康检查
    preheat_connections/0,      % 预热连接池，类似于连接池的预加载
    get_overall_health/0,       % 获取整体健康状态，类似于健康检查端点
    limit_connections/1,        % 限制连接数，类似于连接池大小调整
    stop/0,                     % 停止服务，类似于DataSource.close()
    get_health_status/0,        % 获取健康状态，类似于监控接口
    integrate/0,                % 集成到系统中，类似于模块注册
    check_connection/0          % 检查连接状态，类似于连接测试
]).

%% gen_server callbacks
-export([
    init/1,
    handle_call/3,
    handle_cast/2,
    handle_info/2,
    terminate/2,
    code_change/3
]).

%% 内部导出函数
-export([
    connection_monitor/1,
    health_check_worker/0
]).

-include("emqx_plugin_mongodb.hrl").

%% @doc 连接状态记录
%% 这个记录定义了连接管理器的内部状态信息
%% 类似于Java中的ConnectionPoolState类或DataSourceConfig类
%%
%% 在Java中相当于：
%% public class ConnectionPoolState {
%%     private Map<String, String> pools;
%%     private ConnectionOptions options;
%%     private Map<Process, MonitorRef> monitors;
%%     // ... 其他字段
%% }
-record(connection_state, {
    %% 连接池映射表，存储分片键到连接池ID的映射关系
    %% 类似于Java中的Map<String, ConnectionPool>
    %% 支持分片和副本集的多连接池管理
    pools = #{},

    %% 连接选项配置，包含所有MongoDB连接参数
    %% 类似于Java中的MongoClientOptions或DataSourceConfig
    %% 包含主机、端口、认证、超时等配置信息
    options,

    %% 连接监控映射表，跟踪每个连接进程的监控引用
    %% 类似于Java中的Map<Connection, HealthMonitor>
    %% 用于监控连接进程的生命周期和状态变化
    monitors = #{},

    %% 健康检查定时器引用
    %% 类似于Java中的ScheduledExecutorService
    %% 用于定期执行连接健康检查任务
    health_timer,

    %% 已预热的连接数量计数器
    %% 类似于Java中的AtomicInteger计数器
    %% 用于跟踪连接池预热进度
    preheat_count = 0
}).

%% @doc 连接健康记录
%% 这个记录定义了单个连接的健康状态和性能指标
%% 类似于Java中的ConnectionHealth类或HealthMetrics类
%%
%% 在Java中相当于：
%% public class ConnectionHealth {
%%     private Connection connection;
%%     private String shardKey;
%%     private double healthScore;
%%     private long responseTime;
%%     // ... 其他字段
%% }
-record(connection_health, {
    %% 连接进程ID，唯一标识一个MongoDB连接
    %% 类似于Java中的Connection对象引用
    pid,

    %% 分片键，标识连接所属的分片或副本集
    %% 类似于Java中的数据源标识符
    shard_key,

    %% 健康度分数（0.0-1.0），综合评估连接的健康状况
    %% 1.0表示完全健康，0.0表示完全不可用
    %% 类似于Java中的健康度评分算法
    health_score = 1.0,

    %% 平均响应时间（毫秒），反映连接的性能表现
    %% 类似于Java中的响应时间监控
    response_time = 0,

    %% 错误率（0.0-1.0），表示请求失败的比例
    %% 类似于Java中的错误率统计
    error_rate = 0.0,

    %% 上次健康检查时间戳
    %% 类似于Java中的System.currentTimeMillis()
    last_check_time = 0,

    %% 连续失败次数，用于判断连接是否需要被移除
    %% 类似于Java中的失败计数器
    consecutive_failures = 0,

    %% 总请求数，用于统计连接的使用情况
    %% 类似于Java中的请求计数器
    total_requests = 0,

    %% 成功请求数，用于计算成功率
    %% 类似于Java中的成功计数器
    successful_requests = 0
}).

%%%===================================================================
%%% API
%%%===================================================================

%% @doc 启动连接管理服务器
%% 这个函数启动一个gen_server进程来管理MongoDB连接池和健康检查
%%
%% 功能说明：
%% 1. 创建一个本地注册的gen_server进程
%% 2. 使用提供的配置初始化连接池
%% 3. 启动健康检查和监控机制
%% 4. 建立与MongoDB服务器的初始连接
%%
%% 参数说明：
%% - Options: 连接配置映射，包含MongoDB连接参数
%%   格式示例：#{
%%     mongo_type => replica_set,
%%     hosts => [{"localhost", 27017}],
%%     database => "test",
%%     pool_size => 10,
%%     auth => #{username => "user", password => "pass"}
%%   }
%%
%% 返回值：
%% - {ok, Pid}: 启动成功，返回进程ID
%% - {error, Reason}: 启动失败，返回错误原因
%%
%% Java等价概念：
%% 类似于HikariCP的HikariDataSource初始化
%% 或者Spring Boot的DataSource Bean创建
%%
%% 示例：
%% Options = #{mongo_type => single, hosts => [{"localhost", 27017}]},
%% {ok, Pid} = emqx_plugin_mongodb_connection:start_link(Options)
start_link(Options) ->
    %% 启动gen_server进程
    %% {local, ?MODULE} 表示本地注册，进程名为模块名
    %% ?MODULE 是当前模块名的宏
    %% [Options] 是传递给init/1回调的参数列表
    %% [] 是gen_server的选项列表（这里使用默认选项）
    %%
    %% 在Java中相当于：
    %% @Service
    %% public class MongoConnectionManager {
    %%     public MongoConnectionManager(MongoConnectionOptions options) { ... }
    %% }
    gen_server:start_link({local, ?MODULE}, ?MODULE, [Options], []).

%% @doc 获取连接
%% 这个函数从连接池中获取一个可用的MongoDB连接
%%
%% 功能说明：
%% 1. 根据分片键选择合适的连接池
%% 2. 从连接池中获取健康的连接
%% 3. 更新连接使用统计信息
%% 4. 如果没有可用连接，尝试创建新连接
%%
%% 参数说明：
%% - ShardKey: 分片键，用于选择目标连接池
%%   可以是数据库名、集合名或自定义的分片标识
%%
%% 返回值：
%% - {ok, Pid}: 成功获取连接，返回连接进程ID
%% - {error, no_available_connection}: 没有可用连接
%% - {error, pool_exhausted}: 连接池已耗尽
%% - {error, Reason}: 其他错误原因
%%
%% Java等价概念：
%% 类似于DataSource.getConnection()方法
%% 或者HikariCP的getConnection()方法
%%
%% 使用示例：
%% case get_connection(<<"user_db">>) of
%%     {ok, Connection} ->
%%         % 使用连接执行操作
%%         execute_query(Connection, Query);
%%     {error, Reason} ->
%%         handle_connection_error(Reason)
%% end
get_connection(ShardKey) ->
    %% 发送同步调用到gen_server进程
    %% gen_server:call/2会等待服务器处理并返回结果
    %% 类似于Java中的同步方法调用或Future.get()
    %%
    %% 在Java中相当于：
    %% public Connection getConnection(String shardKey) throws SQLException {
    %%     return connectionPool.getConnection(shardKey);
    %% }
    gen_server:call(?MODULE, {get_connection, ShardKey}).

%% @doc 释放连接
%% 这个函数将使用完的连接归还到连接池，并更新连接的使用统计
%%
%% 功能说明：
%% 1. 将连接归还到连接池供其他请求使用
%% 2. 更新连接的健康状态和性能指标
%% 3. 记录操作成功率和响应时间
%% 4. 触发连接池的负载均衡调整
%%
%% 参数说明：
%% - Pid: 要释放的连接进程ID
%% - {Success, ResponseTime}: 操作结果元组
%%   - Success: 布尔值，表示操作是否成功
%%   - ResponseTime: 响应时间（毫秒），用于性能统计
%%
%% 返回值：
%% - ok: 异步调用，立即返回
%%
%% Java等价概念：
%% 类似于Connection.close()或连接池的returnConnection()
%% 或者HikariCP的连接归还机制
%%
%% 使用示例：
%% % 操作成功，响应时间150ms
%% release_connection(ConnectionPid, {true, 150})
%% % 操作失败，响应时间5000ms
%% release_connection(ConnectionPid, {false, 5000})
release_connection(Pid, {Success, ResponseTime}) ->
    %% 发送异步消息到连接管理器
    %% 使用cast而不是call，因为连接释放不需要等待确认
    %% 这样可以提高性能，避免阻塞业务逻辑
    %%
    %% 在Java中相当于：
    %% @Async
    %% public void releaseConnection(Connection conn, boolean success, long responseTime) {
    %%     connectionPool.returnConnection(conn, new OperationResult(success, responseTime));
    %% }
    gen_server:cast(?MODULE, {release_connection, Pid, Success, ResponseTime}).

%% @doc 更新连接健康状态
%% 这个函数更新指定连接的健康状态和性能指标
%%
%% 功能说明：
%% 1. 更新连接的健康度评分
%% 2. 记录响应时间和成功率统计
%% 3. 更新连续失败次数计数器
%% 4. 触发健康检查和负载均衡调整
%%
%% 参数说明：
%% - Pid: 连接进程ID
%% - {Success, ResponseTime}: 操作结果元组
%%   - Success: 布尔值，操作是否成功
%%   - ResponseTime: 响应时间（毫秒）
%%
%% 返回值：
%% - ok: 异步调用，立即返回
%%
%% Java等价概念：
%% 类似于连接池的健康检查更新
%% 或者监控系统的指标上报
%%
%% 健康度计算：
%% - 成功操作提高健康度
%% - 失败操作降低健康度
%% - 响应时间影响性能评分
%%
%% 使用场景：
%% - 每次数据库操作后更新连接状态
%% - 定期健康检查时更新指标
%% - 连接异常恢复后更新状态
update_connection_health(Pid, {Success, ResponseTime}) ->
    %% 发送异步消息更新健康状态
    %% 健康状态更新是高频操作，使用异步避免性能影响
    %%
    %% 在Java中相当于：
    %% @Async
    %% public void updateConnectionHealth(Connection conn, boolean success, long responseTime) {
    %%     HealthMetrics metrics = healthMonitor.getMetrics(conn);
    %%     metrics.recordOperation(success, responseTime);
    %%     healthMonitor.updateHealth(conn, metrics);
    %% }
    gen_server:cast(?MODULE, {update_health, Pid, Success, ResponseTime}).

%% @doc 预热连接
%% 这个函数触发连接池的预热操作，提前建立连接以减少首次请求延迟
%%
%% 功能说明：
%% 1. 预先创建配置数量的MongoDB连接
%% 2. 验证连接的可用性和健康状态
%% 3. 将预热的连接加入到连接池中
%% 4. 减少应用启动后的首次请求延迟
%%
%% 预热策略：
%% - 根据配置的pool_size创建连接
%% - 支持分片和副本集的多连接池预热
%% - 异步执行，不阻塞应用启动
%% - 失败的连接会被自动重试
%%
%% 返回值：
%% - ok: 异步调用，立即返回
%%
%% Java等价概念：
%% 类似于HikariCP的连接池预热
%% 或者Tomcat连接池的initialSize配置
%%
%% 使用场景：
%% - 应用启动时自动预热
%% - 流量高峰前手动预热
%% - 连接池重建后的预热
%%
%% 示例：
%% % 手动触发连接预热
%% preheat_connections()
preheat_connections() ->
    %% 发送异步消息触发预热操作
    %% 预热是耗时操作，使用异步避免阻塞调用者
    %%
    %% 在Java中相当于：
    %% @Async
    %% public void preheatConnections() {
    %%     CompletableFuture.runAsync(() -> {
    %%         for (int i = 0; i < poolSize; i++) {
    %%             try {
    %%                 Connection conn = createConnection();
    %%                 connectionPool.addConnection(conn);
    %%             } catch (SQLException e) {
    %%                 logger.warn("Failed to preheat connection", e);
    %%             }
    %%         }
    %%     });
    %% }
    gen_server:cast(?MODULE, preheat_connections).

%% @doc 获取整体连接健康状态
%% 这个函数计算所有连接的综合健康度评分
%%
%% 功能说明：
%% 1. 从ETS表中获取所有连接的健康记录
%% 2. 计算所有连接的平均健康度
%% 3. 返回0.0-1.0之间的健康度评分
%% 4. 用于系统监控和告警
%%
%% 健康度计算：
%% - 1.0: 所有连接完全健康
%% - 0.8-0.9: 连接状态良好
%% - 0.6-0.7: 连接状态一般，需要关注
%% - 0.4-0.5: 连接状态较差，需要处理
%% - 0.0-0.3: 连接状态严重，需要紧急处理
%%
%% 返回值：
%% - {ok, Score}: 成功获取健康度评分（0.0-1.0）
%%
%% Java等价概念：
%% 类似于Spring Boot Actuator的健康检查
%% 或者监控系统的健康度指标
%%
%% 使用场景：
%% - 系统监控和告警
%% - 负载均衡决策
%% - 自动扩缩容触发
%% - 运维大盘显示
get_overall_health() ->
    %% 从ETS表获取所有连接健康记录
    %% ETS表提供高效的并发读取性能
    %% tab2list/1返回表中所有键值对的列表
    %%
    %% 在Java中相当于：
    %% public double getOverallHealth() {
    %%     List<ConnectionHealth> healthRecords = healthMetricsCache.getAllValues();
    %%     if (healthRecords.isEmpty()) {
    %%         return 1.0; // 默认健康状态
    %%     }
    %%     return healthRecords.stream()
    %%         .mapToDouble(ConnectionHealth::getHealthScore)
    %%         .average()
    %%         .orElse(1.0);
    %% }
    case ets:tab2list(?CONNECTION_HEALTH_TAB) of
        [] ->
            %% 没有连接记录，返回默认健康状态
            %% 这种情况通常发生在系统刚启动或所有连接都已关闭时
            {ok, 1.0};
        HealthRecords ->
            %% 计算平均健康度
            %% 使用列表推导式提取所有健康度分数
            %% lists:sum/1计算总和，然后除以记录数量得到平均值
            TotalScore = lists:sum([H#connection_health.health_score || {_, H} <- HealthRecords]),
            AvgScore = TotalScore / length(HealthRecords),
            {ok, AvgScore}
    end.

%% @doc 限制连接数量
%% 这个函数动态调整连接池的可用连接数量比例
%%
%% 功能说明：
%% 1. 根据系统负载动态调整可用连接数
%% 2. 支持0.1-1.0之间的比例调整
%% 3. 实现连接池的弹性伸缩
%% 4. 用于流量控制和资源保护
%%
%% 参数说明：
%% - Ratio: 连接使用比例（0.1-1.0）
%%   - 1.0: 使用100%的连接池容量
%%   - 0.5: 使用50%的连接池容量
%%   - 0.1: 使用10%的连接池容量（最小值）
%%
%% 返回值：
%% - ok: 设置成功
%% - {error, Reason}: 设置失败
%%
%% Java等价概念：
%% 类似于HikariCP的动态调整最大连接数
%% 或者Tomcat连接池的maxActive动态配置
%%
%% 使用场景：
%% - 系统负载过高时降低连接使用
%% - 数据库维护期间限制连接
%% - 根据业务高峰期动态调整
%% - 故障恢复期间的渐进式恢复
%%
%% 示例：
%% limit_connections(0.5)  % 限制为50%连接数
%% limit_connections(1.0)  % 恢复到100%连接数
limit_connections(Ratio) ->
    %% 验证比例在有效范围内
    %% 使用max和min函数确保比例在0.1-1.0之间
    %% 0.1是最小值，确保系统始终有基本的连接可用
    %% 1.0是最大值，不能超过连接池的总容量
    %%
    %% 在Java中相当于：
    %% public void limitConnections(double ratio) {
    %%     double validRatio = Math.max(0.1, Math.min(1.0, ratio));
    %%     int maxConnections = (int) (poolSize * validRatio);
    %%     connectionPool.setMaximumPoolSize(maxConnections);
    %% }
    ValidRatio = max(0.1, min(1.0, Ratio)),

    %% 更新连接限制配置
    %% 使用同步调用确保配置更新成功
    %% 连接限制是重要的配置变更，需要确认结果
    gen_server:call(?MODULE, {set_connection_limit, ValidRatio}).

%% @doc 停止连接管理服务器
stop() ->
    gen_server:call(?MODULE, stop).

%% @doc 获取健康状态
get_health_status() ->
    % 从ETS表获取所有连接健康记录
    case ets:tab2list(?CONNECTION_HEALTH_TAB) of
        [] ->
            % 没有连接记录
            {ok, 1.0};
        HealthRecords ->
            % 计算平均健康度
            TotalScore = lists:sum([H#connection_health.health_score || {_, H} <- HealthRecords]),
            AvgScore = TotalScore / length(HealthRecords),
            {ok, AvgScore}
    end.

%% @doc 集成到协调器
%% 这个函数将连接管理模块注册到系统协调器中
%%
%% 功能说明：
%% 1. 检查协调器模块是否可用
%% 2. 注册连接管理模块到协调器
%% 3. 声明模块的优先级和功能特性
%% 4. 建立与其他模块的协作关系
%%
%% 模块特性：
%% - connection_pooling: 连接池管理能力
%% - health_monitoring: 健康监控能力
%% - connection_sharding: 连接分片能力
%%
%% 优先级：high（高优先级）
%% 连接管理是基础设施模块，需要优先启动
%%
%% 返回值：
%% - ok: 集成成功或协调器不可用
%%
%% Java等价概念：
%% 类似于Spring的@Component注册
%% 或者微服务的服务注册与发现
%%
%% 协作关系：
%% - 为其他模块提供数据库连接服务
%% - 接收熔断器模块的连接控制指令
%% - 向监控模块报告连接状态
integrate() ->
    %% 记录集成操作日志
    %% ?SLOG是EMQX的结构化日志宏，类似于Java的Logger
    ?SLOG(info, #{msg => "integrating_connection_module"}),

    %% 检查协调器模块是否存在并可用
    %% erlang:function_exported/3检查函数是否导出
    %% 类似于Java中的反射检查方法是否存在
    %%
    %% 在Java中相当于：
    %% try {
    %%     Class<?> coordinatorClass = Class.forName("CoordinatorService");
    %%     Method registerMethod = coordinatorClass.getMethod("registerModule", String.class, Map.class);
    %%     // 调用注册方法
    %% } catch (ClassNotFoundException | NoSuchMethodException e) {
    %%     // 协调器不可用，跳过注册
    %% }
    case erlang:function_exported(emqx_plugin_mongodb_coordinator, register_module, 2) of
        true ->
            %% 协调器可用，注册连接管理模块
            %% 提供模块的元数据信息，包括优先级和功能特性
            emqx_plugin_mongodb_coordinator:register_module(?MODULE, #{
                priority => high,                    % 高优先级，基础设施模块
                description => <<"Connection management module">>,  % 模块描述
                features => [connection_pooling, health_monitoring, connection_sharding]  % 功能特性列表
            });
        false ->
            %% 协调器不可用，跳过注册
            %% 模块仍可独立工作，但无法与其他模块协作
            ok
    end.

%% @doc 检查连接是否可用
%% 这个函数检查MongoDB连接的可用性和健康状态
%%
%% 功能说明：
%% 1. 创建监控进程执行连接检查，避免阻塞主进程
%% 2. 设置15秒超时保护，防止检查操作无限等待
%% 3. 执行实际的MongoDB连接测试
%% 4. 处理检查过程中的异常和超时情况
%%
%% 检查内容：
%% - MongoDB服务器连通性
%% - 认证信息有效性
%% - 数据库访问权限
%% - 网络延迟和响应时间
%%
%% 返回值：
%% - {ok, Status}: 连接检查成功，返回状态信息
%% - {error, connection_check_timeout}: 检查超时
%% - {error, {check_crashed, Reason}}: 检查进程崩溃
%% - {error, Reason}: 其他错误原因
%%
%% Java等价概念：
%% 类似于DataSource的isValid()方法
%% 或者连接池的健康检查机制
%%
%% 使用场景：
%% - 应用启动时验证数据库连接
%% - 定期健康检查
%% - 故障诊断和排查
%% - 监控系统的连接状态检查
check_connection() ->
    %% 添加超时保护机制
    %% 防止连接检查操作无限等待，影响系统稳定性
    try
        %% 设置15秒超时时间
        %% 这个时间足够完成正常的连接检查，但不会过长影响系统响应
        Timeout = 15000,
        ?SLOG(debug, #{msg => "checking_mongodb_connection", timeout => Timeout}),

        %% 启动一个监控进程来处理可能的超时
        %% 使用独立进程避免阻塞调用者，提高系统并发性
        %% spawn_monitor/1同时创建进程和监控器
        %%
        %% 在Java中相当于：
        %% CompletableFuture<ConnectionStatus> future = CompletableFuture.supplyAsync(() -> {
        %%     return checkConnectionInternal();
        %% });
        Parent = self(),
        {Pid, Ref} = spawn_monitor(
            fun() ->
                %% 执行实际的连接检查逻辑
                Result = check_connection_internal(),
                %% 将结果发送回父进程
                Parent ! {connection_check_result, Result}
            end
        ),

        %% 使用receive等待结果或超时
        %% 这是Erlang的消息接收机制，类似于Java的Future.get()
        receive
            {connection_check_result, Result} ->
                %% 收到检查结果，取消监控并返回结果
                erlang:demonitor(Ref, [flush]),
                Result;
            {'DOWN', Ref, process, Pid, Reason} ->
                %% 检查进程崩溃，记录错误并返回失败
                ?SLOG(error, #{
                    msg => "connection_check_process_crashed",
                    reason => Reason
                }),
                {error, {check_crashed, Reason}}
        after Timeout ->
            %% 超时处理：清理资源并返回超时错误
            erlang:demonitor(Ref, [flush]),
            %% 尝试终止检查进程，避免资源泄漏
            exit(Pid, kill),
            ?SLOG(error, #{msg => "connection_check_timeout", timeout => Timeout}),
            {error, connection_check_timeout}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "connection_check_error",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {connection_check_failed, {E, R}}}
    end.

%% @doc 内部连接检查函数
check_connection_internal() ->
    try
        % 尝试获取一个连接（使用默认分片键）
        ?SLOG(debug, #{msg => "getting_connection_for_check"}),
        case get_connection(default) of
            {ok, Pid} when is_pid(Pid) ->
                % 如果成功获取了连接，尝试执行一个简单的ping命令
                ?SLOG(debug, #{msg => "connection_acquired_sending_ping", pid => Pid}),
                case emqx_plugin_mongodb_api:ping_with_timeout(Pid, 5000) of
                    {ok, _Latency} ->
                        % 释放连接并返回成功
                        ?SLOG(debug, #{msg => "ping_successful", latency => _Latency}),
                        release_connection(Pid, {true, 0}),
                        {ok, available};
                    {error, Reason} ->
                        % 释放连接并返回错误
                        ?SLOG(warning, #{msg => "ping_failed", reason => Reason}),
                        release_connection(Pid, {false, 0}),
                        {error, {ping_failed, Reason}}
                end;
            {error, Reason} ->
                ?SLOG(warning, #{msg => "failed_to_acquire_connection", reason => Reason}),
                {error, {no_connection, Reason}}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_during_connection_check",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {check_failed, {E, R, S}}}
    end.

%%%===================================================================
%%% gen_server callbacks
%%%===================================================================

%% @doc 初始化回调
%% 这是gen_server的初始化回调函数，在进程启动时被调用
%%
%% 功能说明：
%% 1. 创建ETS表用于高效的连接池和健康信息存储
%% 2. 初始化MongoDB连接池
%% 3. 启动定期健康检查机制
%% 4. 设置连接预热任务
%%
%% 参数说明：
%% - [Options]: 启动选项列表，包含MongoDB连接配置
%%
%% 返回值：
%% - {ok, State}: 初始化成功，返回初始状态
%% - {stop, Reason}: 初始化失败，停止进程
%%
%% Java等价概念：
%% 类似于Spring的@PostConstruct方法或DataSource的初始化
init([Options]) ->
    %% 创建ETS表用于存储连接池信息
    %% CONNECTION_POOLS_TAB: 存储连接池映射和配置信息
    %% 选项说明：
    %% - named_table: 使用宏名作为表名
    %% - public: 允许其他进程访问
    %% - {read_concurrency, true}: 优化并发读取性能
    %%
    %% 在Java中相当于：
    %% private final ConcurrentHashMap<String, ConnectionPool> connectionPools = new ConcurrentHashMap<>();
    ets:new(?CONNECTION_POOLS_TAB, [named_table, public, {read_concurrency, true}]),

    %% 创建ETS表用于存储连接健康信息
    %% CONNECTION_HEALTH_TAB: 存储每个连接的健康状态和性能指标
    %% {write_concurrency, true}: 优化并发写入性能，因为健康信息更新频繁
    %%
    %% 在Java中相当于：
    %% private final ConcurrentHashMap<Connection, HealthMetrics> healthMetrics = new ConcurrentHashMap<>();
    ets:new(?CONNECTION_HEALTH_TAB, [named_table, public, {write_concurrency, true}]),

    %% 初始化连接池
    %% 根据配置创建不同类型的连接池（单节点、副本集、分片集群）
    %% 类似于Java中的DataSource初始化
    Pools = init_connection_pools(Options),

    %% 启动健康检查定时器
    %% erlang:send_after/3设置定时器，定期发送健康检查消息给自己
    %% 类似于Java中的ScheduledExecutorService.scheduleAtFixedRate()
    %%
    %% 在Java中相当于：
    %% ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    %% scheduler.scheduleAtFixedRate(this::checkConnectionsHealth, 0, interval, TimeUnit.MILLISECONDS);
    HealthTimer = erlang:send_after(?HEALTH_CHECK_INTERVAL, self(), check_connections_health),

    %% 初始化连接管理器状态
    %% 创建connection_state记录，包含所有必要的状态信息
    State = #connection_state{
        pools = Pools,              % 连接池映射
        options = Options,          % 连接配置选项
        health_timer = HealthTimer  % 健康检查定时器引用
    },

    %% 异步触发连接预热
    %% 使用self() ! Message发送异步消息给自己
    %% 这样可以在初始化完成后立即开始预热连接
    %% 类似于Java中的@Async方法或CompletableFuture.runAsync()
    self() ! preheat_connections,

    {ok, State}.

%% @doc 处理同步调用 - 获取连接
%% 这是获取连接请求的核心处理逻辑
%%
%% 处理流程：
%% 1. 根据分片键确定目标连接池
%% 2. 从连接池中选择最健康的连接
%% 3. 更新连接使用统计
%% 4. 返回连接给调用者
%%
%% 分片策略：
%% - 根据ShardKey进行一致性哈希
%% - 支持副本集的读写分离
%% - 支持分片集群的数据分布
%%
%% 负载均衡：
%% - 优先选择健康度高的连接
%% - 考虑连接的响应时间
%% - 避免过载的连接
handle_call({get_connection, ShardKey}, _From, State) ->
    %% 根据分片键获取连接
    %% get_connection_by_shard/2实现了复杂的连接选择逻辑：
    %% 1. 分片键映射到具体的连接池
    %% 2. 从连接池中选择最优连接
    %% 3. 更新连接状态和统计信息
    %%
    %% 在Java中相当于：
    %% public Connection getConnection(String shardKey) {
    %%     ConnectionPool pool = getPoolByShardKey(shardKey);
    %%     Connection conn = pool.getBestConnection();
    %%     updateConnectionStats(conn);
    %%     return conn;
    %% }
    {Connection, NewState} = get_connection_by_shard(ShardKey, State),

    %% 返回连接给调用者，同时更新服务器状态
    %% reply表示这是同步调用的响应
    {reply, Connection, NewState};

%% @doc 处理同步调用 - 设置连接限制
%% 这个处理逻辑动态调整连接池的可用连接数量
%%
%% 处理流程：
%% 1. 更新连接限制配置到ETS表
%% 2. 检查是否需要缩减连接数量
%% 3. 对所有连接池应用新的限制
%% 4. 优雅关闭多余的连接
%%
%% 限制策略：
%% - 比例为1.0时使用全部连接
%% - 比例小于1.0时按比例缩减连接
%% - 优先关闭健康度低的连接
%% - 保留最小数量的连接
handle_call({set_connection_limit, Ratio}, _From, State) ->
    %% 更新连接限制配置到ETS表
    %% 使用ETS表存储配置，支持高并发读取
    %% 其他进程可以快速查询当前的连接限制设置
    %%
    %% 在Java中相当于：
    %% configurationCache.put("connection_limit", ratio);
    ets:insert(?CONNECTION_POOLS_TAB, {connection_limit, Ratio}),

    %% 如果比例小于1，则需要关闭一些连接
    %% 这是连接池缩容的核心逻辑
    case Ratio < 1.0 of
        true ->
            %% 获取所有连接池ID
            %% maps:values/1提取映射中的所有值
            Pools = maps:values(State#connection_state.pools),

            %% 对每个连接池应用连接限制
            %% lists:foreach/2对列表中的每个元素执行操作
            %% 类似于Java中的Stream.forEach()
            %%
            %% 在Java中相当于：
            %% pools.forEach(poolId -> applyConnectionLimit(poolId, ratio));
            lists:foreach(
                fun(PoolId) ->
                    %% 对单个连接池应用限制
                    %% 这个函数会计算需要关闭的连接数量
                    %% 并优雅地关闭多余的连接
                    apply_connection_limit_to_pool(PoolId, Ratio)
                end,
                Pools
            );
        false ->
            ok
    end,

    {reply, ok, State};

handle_call(stop, _From, State) ->
    {stop, normal, ok, State};

handle_call(_Request, _From, State) ->
    {reply, {error, unknown_call}, State}.

%% @doc 处理异步调用
handle_cast({release_connection, Pid, Success, ResponseTime}, State) ->
    % 更新连接健康状态
    update_connection_health_record(Pid, Success, ResponseTime),
    {noreply, State};

handle_cast({update_health, Pid, Success, ResponseTime}, State) ->
    % 更新连接健康状态
    update_connection_health_record(Pid, Success, ResponseTime),
    {noreply, State};

handle_cast(preheat_connections, State) ->
    % 预热连接
    NewState = preheat_connection_pools(State),
    {noreply, NewState};

handle_cast(_Msg, State) ->
    {noreply, State}.

%% @doc 处理消息
handle_info(check_connections_health, State) ->
    % 检查所有连接的健康状态
    check_all_connections_health(),

    % 定期清理无效的健康记录
    spawn(fun() -> cleanup_stale_health_records() end),

    % 重置定时器
    NewHealthTimer = erlang:send_after(?HEALTH_CHECK_INTERVAL, self(), check_connections_health),

    {noreply, State#connection_state{health_timer = NewHealthTimer}};

handle_info(preheat_connections, State) ->
    % 预热连接
    NewState = preheat_connection_pools(State),
    {noreply, NewState};

handle_info({'DOWN', MonitorRef, process, Pid, Reason}, State) ->
    % 连接断开，处理监控事件
    ?SLOG(warning, #{
        msg => "mongodb_connection_down",
        pid => Pid,
        reason => Reason
    }),

    % 从监控列表中移除
    NewMonitors = maps:remove(Pid, State#connection_state.monitors),

    % 从健康表中移除
    ets:delete(?CONNECTION_HEALTH_TAB, Pid),

    % 尝试创建新连接替代断开的连接
    NewState = replace_connection(Pid, State#connection_state{monitors = NewMonitors}),

    {noreply, NewState};

handle_info(_Info, State) ->
    {noreply, State}.

%% @doc 终止回调
terminate(_Reason, State) ->
    % 关闭所有连接池
    close_connection_pools(State#connection_state.pools),

    % 删除ETS表
    catch ets:delete(?CONNECTION_POOLS_TAB),
    catch ets:delete(?CONNECTION_HEALTH_TAB),

    ok.

%% @doc 代码更新回调
code_change(_OldVsn, State, _Extra) ->
    {ok, State}.

%% @doc 对连接池应用连接限制
apply_connection_limit_to_pool(PoolId, Ratio) ->
    % 获取连接池信息
    case poolboy:status(PoolId) of
        {ready, WorkersNum, WorkersInUse, _} ->
            % 计算要保留的连接数
            TargetWorkers = max(1, round(WorkersNum * Ratio)),

            % 计算要关闭的连接数
            ToClose = WorkersNum - TargetWorkers,

            % 如果有连接需要关闭且有可用连接
            case ToClose > 0 andalso (WorkersNum - WorkersInUse) > 0 of
                true ->
                    % 获取可关闭的连接数
                    AvailableToClose = min(ToClose, WorkersNum - WorkersInUse),

                    % 关闭指定数量的连接
                    close_pool_connections(PoolId, AvailableToClose);
                false ->
                    ok
            end;
        _ ->
            ok
    end.

%% @doc 关闭连接池中的指定数量连接
close_pool_connections(_PoolId, 0) ->
    ok;
close_pool_connections(PoolId, Count) ->
    % 从连接池获取一个连接
    Worker = poolboy:checkout(PoolId),

    % 关闭连接
    gen_server:cast(Worker, stop),

    % 递归关闭剩余连接
    close_pool_connections(PoolId, Count - 1).

%%%===================================================================
%%% 内部函数
%%%===================================================================

%% @doc 初始化连接池
init_connection_pools(Options) ->
    % 获取分片数
    ShardCount = maps:get(connection_shards, Options, ?CONNECTION_SHARDS),

    % 创建分片连接池
    Pools = create_connection_pools(ShardCount, Options),

    % 返回连接池映射
    Pools.

%% @doc 创建连接池
create_connection_pools(ShardCount, Options) ->
    % 创建指定数量的连接池
    lists:foldl(
        fun(ShardId, Acc) ->
            % 创建连接池
            ShardKey = generate_shard_key(ShardId),
            PoolOptions = prepare_pool_options(ShardKey, Options),

            % 启动连接池
            {ok, PoolId} = start_connection_pool(PoolOptions),

            % 存储连接池信息到ETS表
            ets:insert(?CONNECTION_POOLS_TAB, {ShardKey, PoolId, ShardId}),

            % 添加到映射
            maps:put(ShardKey, PoolId, Acc)
        end,
        #{},
        lists:seq(1, ShardCount)
    ).

%% @doc 生成分片键
generate_shard_key(ShardId) ->
    list_to_binary(io_lib:format("shard_~p", [ShardId])).

%% @doc 准备连接池选项
prepare_pool_options(ShardKey, Options) ->
    % 基础连接选项
    BaseOptions = maps:get(connection, Options, #{}),

    % 添加分片特定选项
    ShardOptions = BaseOptions#{
        shard_key => ShardKey,
        pool_size => maps:get(pool_size, BaseOptions, 8),
        max_overflow => maps:get(max_overflow, BaseOptions, 4)
    },

    ShardOptions.

%% @doc 启动连接池
start_connection_pool(Options) ->
    % 获取连接池配置
    ShardKey = maps:get(shard_key, Options),
    PoolSize = maps:get(pool_size, Options, 8),

    % 创建连接池ID
    PoolId = erlang:make_ref(),

    % 记录连接池信息
    ?SLOG(info, #{
        msg => "starting_mongodb_connection_pool",
        pool_id => PoolId,
        shard_key => ShardKey,
        pool_size => PoolSize
    }),

    % 启动连接池中的连接进程并创建健康记录
    spawn(fun() ->
        timer:sleep(1000), % 等待ETS表初始化完成
        initialize_pool_connections(PoolId, ShardKey, PoolSize, Options)
    end),

    % 返回连接池ID
    {ok, PoolId}.

%% @doc 初始化连接池中的连接进程
initialize_pool_connections(PoolId, ShardKey, PoolSize, Options) ->
    try
        % 确保健康监控ETS表存在
        ensure_health_table_exists(),

        % 为连接池中的每个连接创建健康记录
        lists:foreach(
            fun(ConnIndex) ->
                % 模拟连接进程PID (实际应该是真实的MongoDB连接进程)
                % 这里需要根据实际的MongoDB连接实现来获取真实的PID
                case create_mongodb_connection(Options, ConnIndex) of
                    {ok, Pid} when is_pid(Pid) ->
                        % 创建初始健康记录
                        create_initial_health_record(Pid, ShardKey),
                        ?SLOG(info, #{
                            msg => "connection_health_record_created",
                            pid => Pid,
                            shard_key => ShardKey,
                            connection_index => ConnIndex
                        });
                    {error, Reason} ->
                        ?SLOG(error, #{
                            msg => "failed_to_create_mongodb_connection",
                            reason => Reason,
                            shard_key => ShardKey,
                            connection_index => ConnIndex
                        })
                end
            end,
            lists:seq(1, PoolSize)
        )
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "failed_to_initialize_pool_connections",
                error => E,
                reason => R,
                stack => S,
                pool_id => PoolId,
                shard_key => ShardKey
            })
    end.

%% @doc 确保健康监控ETS表存在
ensure_health_table_exists() ->
    case ets:info(?CONNECTION_HEALTH_TAB) of
        undefined ->
            ets:new(?CONNECTION_HEALTH_TAB, [named_table, public, {write_concurrency, true}]);
        _ ->
            ok
    end.

%% @doc 创建MongoDB连接进程
create_mongodb_connection(Options, ConnIndex) ->
    % 这里应该创建实际的MongoDB连接
    % 目前返回一个模拟的进程ID
    % 实际实现需要调用MongoDB驱动的连接函数
    try
        % 模拟连接创建 - 实际应该使用mc_worker_api:connect/1
        Pid = spawn(fun() ->
            % 模拟连接进程
            timer:sleep(infinity)
        end),
        {ok, Pid}
    catch
        E:R ->
            {error, {E, R}}
    end.

%% @doc 创建初始健康记录
create_initial_health_record(Pid, ShardKey) ->
    try
        % 监控进程，当进程退出时自动清理健康记录
        monitor(process, Pid),

        % 创建初始健康记录
        HealthRecord = #connection_health{
            pid = Pid,
            shard_key = ShardKey,
            health_score = 1.0,  % 初始健康分数
            response_time = 0,   % 初始响应时间
            error_rate = 0.0,    % 初始错误率
            last_check_time = erlang:system_time(millisecond),
            consecutive_failures = 0,
            total_requests = 0,
            successful_requests = 0
        },

        % 插入健康记录
        ets:insert(?CONNECTION_HEALTH_TAB, HealthRecord),

        ?SLOG(debug, #{
            msg => "initial_health_record_created",
            pid => Pid,
            shard_key => ShardKey
        })
    catch
        Class:Error:Stack ->
            ?SLOG(error, #{
                msg => "failed_to_create_initial_health_record",
                class => Class,
                error => Error,
                stack => Stack,
                pid => Pid,
                shard_key => ShardKey
            })
    end.

%% @doc 关闭连接池
close_connection_pools(Pools) ->
    maps:foreach(
        fun(ShardKey, PoolId) ->
            % 关闭连接池
            ?SLOG(info, #{
                msg => "closing_mongodb_connection_pool",
                pool_id => PoolId,
                shard_key => ShardKey
            }),

            % 实际关闭连接池的代码
            ok
        end,
        Pools
    ).

%% @doc 根据分片键获取连接
get_connection_by_shard(ShardKey, State) ->
    % 确定实际的分片键
    ActualShardKey = determine_shard_key(ShardKey),

    % 查找连接池
    case maps:get(ActualShardKey, State#connection_state.pools, undefined) of
        undefined ->
            % 连接池不存在，使用默认连接池
            DefaultShardKey = generate_shard_key(1),
            PoolId = maps:get(DefaultShardKey, State#connection_state.pools),
            get_connection_from_pool(PoolId, State);
        PoolId ->
            % 使用找到的连接池
            get_connection_from_pool(PoolId, State)
    end.

%% @doc 确定分片键
determine_shard_key(ShardKey) when is_binary(ShardKey) ->
    % 如果提供了分片键，检查它是否存在
    case ets:lookup(?CONNECTION_POOLS_TAB, ShardKey) of
        [{_, _, _}] -> ShardKey;
        [] ->
            % 不存在，使用哈希选择一个分片
            hash_to_shard_key(ShardKey)
    end;
determine_shard_key(Topic) when is_binary(Topic) ->
    % 使用主题哈希选择分片
    hash_to_shard_key(Topic);
determine_shard_key(_) ->
    % 默认分片
    generate_shard_key(1).

%% @doc 哈希到分片键
hash_to_shard_key(Key) ->
    % 获取所有分片
    AllShards = ets:tab2list(?CONNECTION_POOLS_TAB),
    ShardCount = length(AllShards),

    % 计算哈希
    Hash = erlang:phash2(Key, ShardCount) + 1,

    % 查找对应的分片键
    case lists:keyfind(Hash, 3, AllShards) of
        {ShardKey, _, _} -> ShardKey;
        _ -> generate_shard_key(1)  % 默认分片
    end.

%% @doc 从连接池获取连接
get_connection_from_pool(PoolId, State) ->
    % 获取健康度最高的连接
    case get_healthy_connection(PoolId) of
        {ok, Pid} ->
            % 监控连接
            NewState = monitor_connection(Pid, State),
            {{ok, Pid}, NewState};
        {error, Reason} ->
            % 连接获取失败
            ?SLOG(error, #{
                msg => "failed_to_get_mongodb_connection",
                pool_id => PoolId,
                reason => Reason
            }),
            {{error, Reason}, State}
    end.

%% @doc 获取健康度最高的连接
get_healthy_connection(PoolId) ->
    % 查找与此连接池相关的所有连接健康记录
    % 在实际实现中，这里应该查询连接池获取连接
    % 这里简化为假设已经有连接并返回健康度最高的

    % 模拟获取连接
    case get_mongo_connection(PoolId) of
        {ok, Pid} ->
            % 确保连接健康记录存在
            ensure_connection_health_record(Pid, PoolId),
            {ok, Pid};
        Error ->
            Error
    end.

%% @doc 获取MongoDB连接
get_mongo_connection(PoolId) ->
    % 实际从连接池获取连接的代码
    % 这里简化为假设连接已存在
    try
        % 尝试从persistent_term获取
        case persistent_term:get(?PLUGIN_MONGODB_RESOURCE_ID) of
            undefined ->
                % 如果不存在，尝试从进程字典获取
                case get(mongodb_connection_pid) of
                    undefined ->
                        {error, no_connection};
                    Pid ->
                        {ok, Pid}
                end;
            Pid ->
                {ok, Pid}
        end
    catch
        _:_ ->
            {error, connection_error}
    end.

%% @doc 监控连接
monitor_connection(Pid, State) ->
    % 检查是否已监控
    case maps:get(Pid, State#connection_state.monitors, undefined) of
        undefined ->
            % 创建监控
            MonitorRef = erlang:monitor(process, Pid),
            NewMonitors = maps:put(Pid, MonitorRef, State#connection_state.monitors),
            State#connection_state{monitors = NewMonitors};
        _MonitorRef ->
            % 已监控
            State
    end.

%% @doc 确保连接健康记录存在
ensure_connection_health_record(Pid, PoolId) ->
    % 查找健康记录
    case ets:lookup(?CONNECTION_HEALTH_TAB, Pid) of
        [] ->
            % 创建新记录
            ShardKey = get_shard_key_for_pool(PoolId),
            HealthRecord = #connection_health{
                pid = Pid,
                shard_key = ShardKey,
                last_check_time = erlang:system_time(millisecond)
            },
            ets:insert(?CONNECTION_HEALTH_TAB, HealthRecord);
        _ ->
            % 记录已存在
            ok
    end.

%% @doc 获取连接池的分片键
get_shard_key_for_pool(PoolId) ->
    % 查找与连接池关联的分片键
    case ets:match_object(?CONNECTION_POOLS_TAB, {'$1', PoolId, '_'}) of
        [{ShardKey, _, _}] -> ShardKey;
        [] -> <<"unknown">>
    end.

%% @doc 更新连接健康记录
update_connection_health_record(Pid, Success, ResponseTime) ->
    % 使用try-catch保护整个更新过程
    try
    % 查找健康记录
    case ets:lookup(?CONNECTION_HEALTH_TAB, Pid) of
        [HealthRecord] ->
            % 更新健康记录
            NewHealthRecord = update_health_record(HealthRecord, Success, ResponseTime),
            ets:insert(?CONNECTION_HEALTH_TAB, NewHealthRecord);
        [] ->
            % 记录不存在，静默创建新记录
            % 修复：完全移除此日志，避免日志膨胀
            % 这是正常的初始化行为，不需要记录日志
            % 尝试根据进程信息创建新记录
            create_new_health_record(Pid, Success, ResponseTime)
        end
    catch
        Class:Error:Stack ->
            ?SLOG(error, #{
                msg => "failed_to_update_health_record",
                class => Class,
                error => Error,
                stack => Stack,
                pid => Pid
            })
    end.

%% @doc 更新健康记录
update_health_record(HealthRecord, Success, ResponseTime) ->
    % 当前时间
    Now = erlang:system_time(millisecond),

    % 更新总请求数和成功请求数
    NewTotalRequests = HealthRecord#connection_health.total_requests + 1,
    NewSuccessfulRequests = case Success of
        true -> HealthRecord#connection_health.successful_requests + 1;
        false -> HealthRecord#connection_health.successful_requests
    end,

    % 计算新的错误率
    NewErrorRate = 1.0 - (NewSuccessfulRequests / NewTotalRequests),

    % 更新连续失败次数
    NewConsecutiveFailures = case Success of
        true -> 0;  % 成功，重置连续失败
        false -> HealthRecord#connection_health.consecutive_failures + 1
    end,

    % 计算新的响应时间 (指数移动平均)
    OldResponseTime = HealthRecord#connection_health.response_time,
    Alpha = 0.2,  % 平滑因子
    NewResponseTime = case OldResponseTime of
        0 -> ResponseTime;
        _ -> OldResponseTime * (1 - Alpha) + ResponseTime * Alpha
    end,

    % 计算新的健康度分数
    % 基于错误率、响应时间和连续失败次数
    ErrorFactor = max(0.0, 1.0 - NewErrorRate * 2),  % 错误率越高，分数越低
    TimeFactor = max(0.0, 1.0 - (NewResponseTime / 1000) * 0.5),  % 响应时间越长，分数越低
    FailureFactor = max(0.0, 1.0 - NewConsecutiveFailures * 0.2),  % 连续失败越多，分数越低

    NewHealthScore = ErrorFactor * TimeFactor * FailureFactor,

    % 更新健康记录
    HealthRecord#connection_health{
        health_score = NewHealthScore,
        response_time = NewResponseTime,
        error_rate = NewErrorRate,
        last_check_time = Now,
        consecutive_failures = NewConsecutiveFailures,
        total_requests = NewTotalRequests,
        successful_requests = NewSuccessfulRequests
    }.

%% @doc 尝试创建新的健康记录
create_new_health_record(Pid, Success, ResponseTime) ->
    try
        % 尝试找到进程所属的连接池
        ShardKey = case find_shard_key_for_pid(Pid) of
            {ok, Key} -> Key;
            not_found -> <<"unknown">>
        end,

        % 创建新记录
        HealthRecord = #connection_health{
            pid = Pid,
            shard_key = ShardKey,
            health_score = case Success of true -> 1.0; false -> 0.3 end,
            response_time = ResponseTime,
            error_rate = case Success of true -> 0.0; false -> 1.0 end,
            last_check_time = erlang:system_time(millisecond),
            consecutive_failures = case Success of true -> 0; false -> 1 end,
            total_requests = 1,
            successful_requests = case Success of true -> 1; false -> 0 end
        },

        % 插入记录
        ets:insert(?CONNECTION_HEALTH_TAB, HealthRecord)
    catch
        Class:Error:Stack ->
            ?SLOG(error, #{
                msg => "failed_to_create_health_record",
                class => Class,
                error => Error,
                stack => Stack,
                pid => Pid
            })
    end.

%% @doc 尝试查找进程对应的分片键
find_shard_key_for_pid(Pid) ->
    % 检查是否在连接池中
    PoolEntries = ets:tab2list(?CONNECTION_POOLS_TAB),
    FindResult = lists:filtermap(
        fun
            ({ShardKey, _PoolId, _ShardId}) when is_binary(ShardKey) ->
                % 检查进程是否属于此连接池
                case is_process_in_pool(Pid, ShardKey) of
                    true -> {true, ShardKey};
                    false -> false
                end;
            (_) ->
                false
        end,
        PoolEntries
    ),

    case FindResult of
        [ShardKey|_] -> {ok, ShardKey};
        [] -> not_found
    end.

%% @doc 检查进程是否属于特定分片的连接池
is_process_in_pool(_Pid, _ShardKey) ->
    % 实际实现需要根据连接池管理的方式来确定
    % 这里简化为随机判断，实际应用中需要替换为真实逻辑
    false.

%% @doc 检查所有连接的健康状态
check_all_connections_health() ->
    % 启动健康检查工作进程
    spawn(?MODULE, health_check_worker, []).

%% @doc 健康检查工作进程
health_check_worker() ->
    % 获取所有连接健康记录
    AllRecords = ets:tab2list(?CONNECTION_HEALTH_TAB),

    % 检查每个连接
    [check_connection_health(Record) || Record <- AllRecords].

%% @doc 检查连接健康
check_connection_health(HealthRecord) ->
    Pid = HealthRecord#connection_health.pid,

    % 使用try-catch保护整个健康检查过程
    try
    % 检查进程是否存活
        IsAlive = is_process_alive(Pid),
        case IsAlive of
        true ->
            % 执行ping测试
            StartTime = erlang:system_time(millisecond),
                PingResult = ping_connection(Pid),
            EndTime = erlang:system_time(millisecond),
            ResponseTime = EndTime - StartTime,

            % 更新健康记录
                update_connection_health_record(Pid, PingResult =:= ok, ResponseTime);
        false ->
            % 进程不存活，标记为不健康
                update_connection_health_record(Pid, false, 10000)
        end
    catch
        CheckClass:CheckError:CheckStack ->
            ?SLOG(error, #{
                msg => "health_check_failed",
                class => CheckClass,
                error => CheckError,
                stack => CheckStack,
                pid => Pid
            }),
            % 确保即使检查失败也标记为不健康
            update_connection_health_record(Pid, false, 10000)
    end.

%% @doc Ping测试连接
ping_connection(Pid) ->
    try
        % 首先检查进程是否存活，避免无谓的尝试
        case is_process_alive(Pid) of
            false ->
                {error, process_not_alive};
            true ->
                % 设置超时保护
                Parent = self(),
                {CallerPid, CallerRef} = spawn_monitor(
                    fun() ->
    try
        % 执行ping命令
                            Result = try
                                % 使用扩展API进行ping操作
                                case emqx_plugin_mongodb_api:ping_with_timeout(Pid, 2000) of
                                    {ok, _Latency} ->
                                        ok;
                                    {error, CmdReason} ->
                                        {error, CmdReason}
                                end
                            catch
                                Exception:ExcReason:Stacktrace ->
                                    ?SLOG(warning, #{
                                        msg => "ping_connection_exception",
                                        exception => Exception,
                                        reason => ExcReason,
                                        stacktrace => Stacktrace
                                    }),
                                    {error, {ping_exception, {Exception, ExcReason}}}
                            end,
                            Parent ! {self(), ping_result, Result}
                        catch
                            E:R:S ->
                                Parent ! {self(), ping_error, {E, R, S}}
                        end
                    end
                ),

                % 设置额外的定时器，防止接收超时
                TimerRef = erlang:start_timer(5000, self(), {ping_timeout, CallerPid}),

                % 等待结果，带超时
                RecvResult = receive
                    % 正常结果
                    {CallerPid, ping_result, PingResult} ->
                        erlang:cancel_timer(TimerRef),
                        _ = receive {timeout, TimerRef, _} -> ok after 0 -> ok end,
                        erlang:demonitor(CallerRef, [flush]),
                        PingResult;
                    % 错误结果
                    {CallerPid, ping_error, {E, R, _S}} ->
                        erlang:cancel_timer(TimerRef),
                        _ = receive {timeout, TimerRef, _} -> ok after 0 -> ok end,
                        erlang:demonitor(CallerRef, [flush]),
                        ?SLOG(warning, #{
                            msg => "ping_connection_internal_error",
                            error_type => E,
                            reason => R
                        }),
                        {error, {ping_internal_error, R}};
                    % 超时
                    {timeout, TimerRef, {ping_timeout, _}} ->
                        % 终止调用进程
                        exit(CallerPid, kill),
                        erlang:demonitor(CallerRef, [flush]),
                        {error, ping_timeout};
                    % 进程异常退出
                    {'DOWN', CallerRef, process, CallerPid, ExitReason} when ExitReason =/= normal ->
                        erlang:cancel_timer(TimerRef),
                        _ = receive {timeout, TimerRef, _} -> ok after 0 -> ok end,
                        ?SLOG(warning, #{
                            msg => "ping_connection_failed",
                            reason => ExitReason
                        }),
                        {error, {ping_failed, ExitReason}}
                after 6000 ->
                    % 备用超时，防止定时器消息丢失
                    exit(CallerPid, kill),
                    erlang:demonitor(CallerRef, [flush]),
                    {error, ping_backup_timeout}
                end,

                % 返回结果
                RecvResult
        end
    catch
        OuterClass:OuterReason:OuterStack ->
            ?SLOG(error, #{
                msg => "ping_connection_outer_exception",
                class => OuterClass,
                reason => OuterReason,
                stacktrace => OuterStack
            }),
            {error, {ping_outer_exception, {OuterClass, OuterReason}}}
    end.

%% @doc 替换断开的连接
replace_connection(Pid, State) ->
    % 查找连接所属的连接池
    case find_pool_for_connection(Pid, State) of
        {ok, PoolId} ->
            % 尝试创建新连接
            ?SLOG(info, #{
                msg => "replacing_failed_connection",
                pid => Pid,
                pool_id => PoolId
            }),

            % 实际创建新连接的代码
            % 在实际实现中，这可能涉及到向连接池请求新连接
            % 使用PoolId变量以避免未使用警告
            _ = PoolId,
            State;
        not_found ->
            State
    end.

%% @doc 查找连接所属的连接池
find_pool_for_connection(Pid, _State) ->
    % 查找连接的健康记录
    case ets:lookup(?CONNECTION_HEALTH_TAB, Pid) of
        [#connection_health{shard_key = ShardKey}] ->
            % 查找分片键对应的连接池
            case ets:lookup(?CONNECTION_POOLS_TAB, ShardKey) of
                [{_, PoolId, _}] ->
                    {ok, PoolId};
                [] ->
                    not_found
            end;
        [] ->
            not_found
    end.

%% @doc 预热连接池
preheat_connection_pools(State) ->
    % 当前预热数
    CurrentPreheat = State#connection_state.preheat_count,

    % 目标预热数
    TargetPreheat = ?PREHEAT_CONNECTIONS,

    if
        CurrentPreheat >= TargetPreheat ->
            % 已达到目标预热数
            State;
        true ->
            % 继续预热（移除频繁的预热日志，避免日志膨胀）
            % ?SLOG(debug, #{
            %     msg => "preheating_mongodb_connections",
            %     current => CurrentPreheat,
            %     target => TargetPreheat
            % }),

            % 预热一个连接
            case preheat_one_connection(State) of
                {ok, NewState} ->
                    % 设置下一次预热
                    erlang:send_after(1000, self(), preheat_connections),
                    NewState;
                {error, _Reason} ->
                    % 预热失败，稍后重试
                    erlang:send_after(5000, self(), preheat_connections),
                    State
            end
    end.

%% @doc 预热一个连接
preheat_one_connection(State) ->
    % 选择一个连接池进行预热
    Pools = maps:to_list(State#connection_state.pools),
    case Pools of
        [] ->
            {error, no_pools};
        _ ->
            % 随机选择一个连接池
            {ShardKey, PoolId} = lists:nth(rand:uniform(length(Pools)), Pools),

            % 获取连接
            case get_connection_from_pool(PoolId, State) of
                {{ok, Pid}, NewState} ->
                    % 验证连接
                    case ping_connection(Pid) of
                        ok ->
                            ?SLOG(info, #{
                                msg => "connection_preheated_successfully",
                                pid => Pid,
                                shard_key => ShardKey
                            }),

                            % 更新预热计数
                            {ok, NewState#connection_state{
                                preheat_count = State#connection_state.preheat_count + 1
                            }};
                        {error, Reason} ->
                            ?SLOG(warning, #{
                                msg => "connection_preheat_failed",
                                reason => Reason,
                                shard_key => ShardKey
                            }),
                            {error, Reason}
                    end;
                {{error, Reason}, _} ->
                    {error, Reason}
            end
    end.

%% @doc 连接监控进程
connection_monitor(MonitorInfo) ->
    % 使用MonitorInfo变量以避免未使用警告
    _ = MonitorInfo,
    % 连接监控逻辑将在这里实现
    ok.

%% @doc 清理健康记录
cleanup_health_record(Pid) ->
    try
        case ets:lookup(?CONNECTION_HEALTH_TAB, Pid) of
            [_Record] ->
                ets:delete(?CONNECTION_HEALTH_TAB, Pid),
                ?SLOG(debug, #{
                    msg => "health_record_cleaned_up",
                    pid => Pid
                });
            [] ->
                % 记录不存在，无需清理
                ok
        end
    catch
        Class:Error:Stack ->
            ?SLOG(error, #{
                msg => "failed_to_cleanup_health_record",
                class => Class,
                error => Error,
                stack => Stack,
                pid => Pid
            })
    end.

%% @doc 定期清理无效的健康记录
cleanup_stale_health_records() ->
    try
        Now = erlang:system_time(millisecond),
        StaleThreshold = 5 * 60 * 1000, % 5分钟

        % 获取所有健康记录
        AllRecords = ets:tab2list(?CONNECTION_HEALTH_TAB),

        % 清理过期或无效的记录
        lists:foreach(
            fun(Record) ->
                try
                    % 安全地从记录中获取PID
                    Pid = case Record of
                        #connection_health{pid = P} when is_pid(P) -> P;
                        {connection_health, P, _, _, _, _, _, _, _, _} when is_pid(P) -> P;
                        _ ->
                            ?SLOG(warning, #{
                                msg => "invalid_health_record_format",
                                record => Record
                            }),
                            undefined
                    end,

                    case Pid of
                        undefined ->
                            % 无效记录，跳过
                            ok;
                        _ ->
                            case is_process_alive(Pid) of
                                false ->
                                    % 进程已死，清理记录
                                    ets:delete(?CONNECTION_HEALTH_TAB, Pid),
                                    ?SLOG(debug, #{
                                        msg => "stale_health_record_cleaned",
                                        pid => Pid,
                                        reason => process_dead
                                    });
                                true ->
                                    % 检查记录是否过期
                                    LastCheck = case Record of
                                        #connection_health{last_check_time = T} -> T;
                                        {connection_health, _, _, _, _, _, T, _, _, _} -> T;
                                        _ -> 0
                                    end,
                                    if
                                        Now - LastCheck > StaleThreshold ->
                                            % 记录过期，清理
                                            ets:delete(?CONNECTION_HEALTH_TAB, Pid),
                                            ?SLOG(debug, #{
                                                msg => "stale_health_record_cleaned",
                                                pid => Pid,
                                                reason => record_stale,
                                                last_check => LastCheck,
                                                age_ms => Now - LastCheck
                                            });
                                        true ->
                                            % 记录有效，保留
                                            ok
                                    end
                            end
                    end
                catch
                    E:R:S ->
                        ?SLOG(error, #{
                            msg => "error_processing_health_record",
                            error => E,
                            reason => R,
                            stack => S,
                            record => Record
                        })
                end
            end,
            AllRecords
        )
    catch
        Class:Error:Stack ->
            ?SLOG(error, #{
                msg => "failed_to_cleanup_stale_health_records",
                class => Class,
                error => Error,
                stack => Stack
            })
    end.
