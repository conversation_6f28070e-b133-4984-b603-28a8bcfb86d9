%% @doc EMQX MongoDB Plugin - MQTT包ID管理模块
%% 这个模块是MQTT协议包ID管理的核心组件，提供企业级的包ID分配和持久化服务
%%
%% 功能概述：
%% 1. 包ID分配和回收 - 智能分配MQTT协议要求的唯一包ID（1-65535）
%% 2. 包ID状态持久化 - 将包ID使用状态持久化到MongoDB，确保会话恢复时的一致性
%% 3. 会话恢复支持 - 在客户端重连或系统重启时恢复包ID分配状态
%% 4. 包ID冲突检测 - 检测和解决包ID分配冲突，确保协议合规性
%% 5. 自动清理机制 - 定期清理过期的包ID分配记录，防止资源泄漏
%% 6. 高性能分配 - 提供高效的包ID分配算法，支持高并发场景
%%
%% MQTT包ID说明：
%% - 包ID范围：1-65535（MQTT协议规定）
%% - 用途：标识QoS 1和QoS 2消息的唯一性
%% - 生命周期：从分配到确认完成后释放
%% - 会话相关：每个客户端会话维护独立的包ID空间
%%
%% 架构设计：
%% - 分布式包ID管理：支持多节点环境下的包ID分配
%% - 持久化存储：使用MongoDB存储包ID分配状态
%% - 内存缓存：结合内存缓存提高分配性能
%% - 定时清理：自动清理过期的包ID记录
%% - 容错恢复：支持系统故障后的状态恢复
%%
%% Java等价概念：
%% 类似于分布式ID生成器或序列号管理器：
%% @Service
%% @Component
%% public class PacketIdManager {
%%     @Autowired private MongoTemplate mongoTemplate;
%%     @Autowired private RedisTemplate redisTemplate; // 缓存层
%%
%%     @Synchronized
%%     public int allocatePacketId(String clientId) {
%%         // 分配唯一的包ID
%%         return idGenerator.nextId(clientId);
%%     }
%%
%%     @Async
%%     public void releasePacketId(String clientId, int packetId) {
%%         // 释放包ID，标记为可重用
%%         idGenerator.releaseId(clientId, packetId);
%%     }
%%
%%     @PostConstruct
%%     public void restorePacketIdState() {
%%         // 系统启动时恢复包ID分配状态
%%         List<PacketIdState> states = repository.findAllActiveStates();
%%         idGenerator.restoreStates(states);
%%     }
%%
%%     @Scheduled(fixedDelay = 600000) // 10分钟清理过期记录
%%     public void cleanupExpiredPacketIds() {
%%         repository.deleteExpiredPacketIds();
%%     }
%% }
%%
%% 设计原则：
%% - 协议合规性：严格遵循MQTT协议的包ID规范
%% - 唯一性保证：确保包ID在客户端会话中的唯一性
%% - 持久化支持：支持包ID状态的持久化和恢复
%% - 高效分配：提供高效的包ID分配算法
%% - 会话集成：与EMQX会话模块深度集成
%% - 容错设计：支持故障恢复和异常处理
%%
%% 设计模式：
%% - 单例模式：每个客户端会话的包ID管理器
%% - 工厂模式：包ID的创建和分配
%% - 状态模式：包ID的生命周期状态管理
%% - 观察者模式：包ID状态变化的通知
%% @end

-module(emqx_plugin_mongodb_packet_id).

-include("emqx_plugin_mongodb.hrl").

%% ============================================================================
%% 生命周期管理API - 模块启动、配置和关闭管理
%% 这些函数管理包ID管理模块的完整生命周期
%% 类似于Java Spring Boot的生命周期管理：
%% @PostConstruct - 初始化资源和配置
%% @PreDestroy - 清理资源和保存状态
%% @ConfigurationProperties - 配置加载和验证
%% ============================================================================
-export([
    init/0,         % 初始化模块 - 类似于@PostConstruct，设置基础环境
    load/1,         % 加载配置 - 类似于@ConfigurationProperties，加载和验证配置
    unload/0        % 卸载模块 - 类似于@PreDestroy，清理资源和保存状态
]).

%% ============================================================================
%% 包ID管理核心API - MQTT包ID的分配、释放和状态管理
%% 这些函数提供包ID管理的核心业务功能
%% 类似于Java的ID生成器服务：
%% @Service
%% public class PacketIdService {
%%     public int allocatePacketId(String clientId);
%%     public void releasePacketId(String clientId, int packetId);
%%     public PacketIdState getNextPacketId(String clientId);
%% }
%% ============================================================================
-export([
    log_persisted_packet_id_info/1, % 记录持久化包ID信息
                                   % 功能：记录客户端的持久化包ID详情
                                   % Java等价：public void logPersistedPacketIdInfo(String clientId)

    allocate_packet_id/1,           % 分配包ID给客户端（已弃用，违反方案A）
                                   % 警告：这个函数在方案A中不应该被调用
                                   % Java等价：@Deprecated public int allocatePacketId(String clientId)

    release_packet_id/2,            % 释放客户端的包ID（已弃用，违反方案A）
                                   % 警告：这个函数在方案A中不应该被调用
                                   % Java等价：@Deprecated public void releasePacketId(String clientId, int packetId)

    get_next_packet_id/1,           % 获取客户端的下一个可用包ID（已弃用，违反方案A）
                                   % 警告：这个函数在方案A中不应该被调用
                                   % Java等价：@Deprecated public int getNextAvailablePacketId(String clientId)

    restore_packet_id_state/1,      % 恢复客户端的包ID分配状态（已弃用，违反方案A）
                                   % 警告：这个函数在方案A中不应该被调用
                                   % Java等价：@Deprecated public void restorePacketIdState(String clientId)

    save_packet_id_state/2,         % 保存客户端的包ID分配状态
                                   % 功能：将客户端的包ID使用状态持久化到MongoDB
                                   % Java等价：public void savePacketIdState(String clientId, PacketIdState state)

    cleanup_expired_packet_ids/0,   % 清理过期的包ID记录
                                   % 功能：定期清理过期的包ID分配记录，防止资源泄漏
                                   % Java等价：@Scheduled public void cleanupExpiredPacketIds()

    save_packet_id_state_with_options/3, % 保存包ID状态（带选项）
                                        % 功能：支持MQTT 5.0协议的完整包ID管理
                                        % Java等价：public void savePacketIdStateWithOptions(String clientId, int packetId, Options options)

    check_receive_maximum_limit/2,      % 检查Receive Maximum限制
                                       % 功能：检查客户端是否超过了MQTT 5.0 Receive Maximum限制
                                       % Java等价：public boolean checkReceiveMaximumLimit(String clientId, int receiveMaximum)

    calculate_packet_id_expiry_time/1,  % 计算包ID过期时间
                                       % 功能：考虑会话过期时间和包ID配置
                                       % Java等价：public long calculatePacketIdExpiryTime(Integer sessionExpiryInterval)

    update_packet_id_state/3,           % 更新包ID状态
                                       % 功能：支持包ID生命周期状态管理
                                       % Java等价：public void updatePacketIdState(String clientId, int packetId, State newState)

    get_packet_id_stats/1,              % 获取客户端的包ID统计信息
                                       % 功能：获取包ID使用统计，用于监控和调试
                                       % Java等价：public PacketIdStats getPacketIdStats(String clientId)

    find_available_packet_id_efficient/1, % 高效的包ID分配算法
                                         % 功能：使用优化算法提高包ID分配效率
                                         % Java等价：public int findAvailablePacketIdEfficient(List<Integer> usedIds)

    find_available_packet_id_circular/2 % 循环包ID分配算法
                                       % 功能：实现MQTT协议推荐的包ID重用策略
                                       % Java等价：public int findAvailablePacketIdCircular(List<Integer> usedIds, Integer lastUsedId)
]).

%% ============================================================================
%% 内部辅助函数 - 模块内部使用的工具和配置函数
%% 这些函数提供模块内部的辅助功能，类似于Java的private方法
%% ============================================================================
-export([
    get_packet_id_collection/0,     % 获取包ID集合名称 - 配置获取方法
    start_cleanup_timer/0           % 启动清理定时器 - 类似于@Scheduled任务启动
]).

%% ============================================================================
%% 配置常量定义 - MQTT包ID管理的核心配置参数
%% 这些常量控制包ID分配的行为和性能特征
%% 类似于Java中的配置常量或@ConfigurationProperties
%% ============================================================================

%% 注意：MongoDB集合名称现在统一定义在 emqx_plugin_mongodb.hrl 中
%% 这样可以保持整个项目的命名一致性和集中管理

%% MQTT协议包ID范围常量定义
%% 根据MQTT协议规范，包ID必须在1-65535范围内
%% 0是保留值，不能用作包ID
%% Java等价：public static final int MIN_PACKET_ID = 1;
-define(MIN_PACKET_ID, 1).      % 最小包ID：1（MQTT协议规定）
-define(MAX_PACKET_ID, 65535).  % 最大包ID：65535（MQTT协议规定，16位无符号整数最大值）

%% 默认清理间隔：10分钟（600000毫秒）
%% 功能：控制过期包ID记录清理任务的执行频率
%% 定时任务会按此间隔清理过期的包ID分配记录
%% Java等价：@Scheduled(fixedDelay = 600000)
%% 或者：spring.task.scheduling.pool.size=1
-define(DEFAULT_CLEANUP_INTERVAL, 600000).

%% ============================================================================
%% 模块初始化函数 - 包ID管理模块的启动入口
%% ============================================================================

%% @doc 初始化包ID管理模块
%% 这个函数是模块的启动入口，负责初始化所有必要的资源和服务
%%
%% 功能说明：
%% 1. 检查包ID持久化功能是否启用
%% 2. 等待MongoDB资源就绪
%% 3. 创建必要的集合和索引
%% 4. 启动过期包ID清理定时器
%%
%% 返回值：
%% - ok: 初始化成功
%%
%% Java等价概念：
%% @PostConstruct
%% @ConditionalOnProperty(name = "packet.id.persistence.enabled", havingValue = "true")
%% public void initializePacketIdManager() {
%%     if (packetIdPersistenceEnabled) {
%%         waitForMongoDBReady();
%%         createCollectionsAndIndexes();
%%         startCleanupScheduler();
%%     }
%% }
%%
%% 设计特点：
%% - 条件初始化：只有在启用包ID持久化时才执行初始化
%% - 资源等待：确保MongoDB资源可用后再进行后续操作
%% - 完整设置：一次性完成所有必要的初始化工作
init() ->
    ?SLOG(info, #{
        msg => "initializing_mongodb_packet_id_persistence_module",
        approach => "pure_persistence_no_direct_allocation"
    }),

    %% 检查包ID持久化功能是否启用
    case application:get_env(emqx_plugin_mongodb, packet_id_persistence_enabled, false) of
        true ->
            ?SLOG(info, #{
                msg => "packet_id_persistence_enabled",
                module => ?MODULE,
                note => "only_persistence_no_direct_packet_id_management"
            }),

            %% 等待MongoDB资源就绪
            wait_for_resource(),

            %% 创建MongoDB集合和索引（用于持久化包ID使用状态）
            ensure_collections(),

            %% 启动过期包ID清理定时器
            start_cleanup_timer();
        false ->
            ?SLOG(info, #{
                msg => "packet_id_persistence_disabled",
                module => ?MODULE
            })
    end,
    ok.

%% @doc 加载包ID管理模块（带配置参数）
%% 这个函数负责根据配置加载和初始化包ID管理模块
%%
%% 功能说明：
%% 1. 解析包ID持久化相关的配置参数
%% 2. 将配置保存到应用环境变量中
%% 3. 根据配置决定是否启用包ID持久化
%% 4. 如果启用，调用init函数进行初始化
%%
%% 参数说明：
%% - Config: 配置映射，包含packet_id_persistence配置节
%%   格式示例：#{
%%     packet_id_persistence => #{
%%       enabled => true,
%%       cleanup_interval => 600000
%%     }
%%   }
%%
%% 返回值：
%% - ok: 加载完成（无论成功还是失败都返回ok）
%%
%% Java等价概念：
%% @ConfigurationProperties(prefix = "packet.id.persistence")
%% @ConditionalOnProperty(name = "packet.id.persistence.enabled", havingValue = "true")
%% public class PacketIdPersistenceAutoConfiguration {
%%     @PostConstruct
%%     public void load(PacketIdConfig config) {
%%         if (config.isEnabled()) {
%%             initializePacketIdManager();
%%         }
%%     }
%% }
load(Config) ->
    %% 记录模块加载开始的日志，包含配置信息
    ?SLOG(info, #{msg => "loading_mongodb_packet_id_module", config => Config}),

    %% 从总配置中提取包ID持久化的专门配置
    %% 如果配置不存在，使用空映射作为默认值
    %% 在Java中相当于：
    %% PacketIdConfig packetIdConfig = config.getPacketIdPersistence()
    %%     .orElse(new PacketIdConfig());
    PacketIdConfig = maps:get(packet_id_persistence, Config, #{}),

    %% 从包ID配置中提取启用标志
    %% 默认为false，即默认不启用包ID持久化
    %% 在Java中相当于：
    %% boolean enabled = packetIdConfig.isEnabled();
    PacketIdPersistenceEnabled = maps:get(enabled, PacketIdConfig, false),

    %% 将启用状态保存到应用环境变量中
    %% 这样其他函数可以通过application:get_env查询状态
    %% 在Java中相当于：
    %% System.setProperty("packet.id.persistence.enabled", String.valueOf(enabled));
    application:set_env(emqx_plugin_mongodb, packet_id_persistence_enabled, PacketIdPersistenceEnabled),

    %% 根据配置决定是否启用包ID持久化
    case PacketIdPersistenceEnabled of
        true ->
            %% 包ID持久化已启用，开始初始化
            ?SLOG(info, #{msg => "packet_id_persistence_enabled"}),

            %% 调用init函数进行完整的初始化
            %% 在Java中相当于：
            %% @ConditionalOnProperty(name = "packet.id.persistence.enabled", havingValue = "true")
            %% @PostConstruct
            %% public void initialize() { ... }
            init();
        false ->
            %% 包ID持久化未启用，跳过初始化
            ?SLOG(info, #{msg => "packet_id_persistence_disabled"})
    end,
    %% 无论成功还是失败，都返回ok
    ok.

%% @doc 卸载包ID管理模块
%% 这个函数负责清理模块加载时创建的所有资源和服务
%%
%% 功能说明：
%% 1. 记录模块卸载日志
%% 2. 清理定时器和其他资源
%% 3. 保存当前状态（如果需要）
%%
%% 返回值：
%% - ok: 卸载完成
%%
%% Java等价概念：
%% @PreDestroy
%% public void unload() {
%%     stopScheduledTasks();
%%     cleanupResources();
%%     saveCurrentState();
%% }
%%
%% 注意：当前实现比较简单，主要是记录日志
%% 未来可能需要添加更多的清理逻辑
unload() ->
    %% 记录模块卸载的日志
    %% 这有助于跟踪模块的生命周期状态
    ?SLOG(info, #{msg => "unloading_mongodb_packet_id_module"}),
    %% 目前只是记录日志，未来可能需要添加：
    %% - 停止清理定时器
    %% - 保存当前包ID分配状态
    %% - 清理内存缓存
    ok.

%% @doc 确保包ID相关的MongoDB集合存在
%% 这个函数确保包ID管理所需的MongoDB集合和索引正确创建
%%
%% 功能说明：
%% 1. 获取包ID集合的名称
%% 2. 记录集合创建过程的日志
%% 3. 依赖MongoDB的自动集合创建机制
%% 4. 处理集合创建过程中的异常
%%
%% 返回值：
%% - ok: 集合确保成功
%% - {error, {ensure_collections_failed, Reason}}: 集合创建失败
%%
%% Java等价概念：
%% @PostConstruct
%% public void ensureCollections() {
%%     try {
%%         String collectionName = getPacketIdCollectionName();
%%         if (!mongoTemplate.collectionExists(collectionName)) {
%%             mongoTemplate.createCollection(collectionName);
%%             createIndexes(collectionName);
%%         }
%%     } catch (Exception e) {
%%         logger.error("Failed to ensure collections", e);
%%         throw new CollectionCreationException(e);
%%     }
%% }
%%
%% 设计说明：
%% - 延迟创建：MongoDB会在第一次插入数据时自动创建集合
%% - 索引管理：索引会在实际使用时根据需要创建
%% - 异常处理：捕获所有异常并记录详细信息
ensure_collections() ->
    try
        %% 获取包ID集合的名称
        %% 集合名称在emqx_plugin_mongodb.hrl中统一定义
        %% 在Java中相当于：
        %% String collectionName = configProperties.getPacketIdCollectionName();
        PacketIdCollection = get_packet_id_collection(),

        %% 记录集合确保过程开始的日志
        %% 包含集合名称信息，便于调试和监控
        ?SLOG(info, #{
            msg => "ensuring_packet_id_collections",
            packet_id_collection => PacketIdCollection
        }),

        %% MongoDB集合创建策略说明：
        %% MongoDB会在第一次插入文档时自动创建集合
        %% 这种延迟创建策略避免了预先创建空集合的开销
        %% 在Java中相当于：
        %% // MongoDB会在第一次插入时自动创建集合
        %% // mongoTemplate.insert(document, collectionName); // 自动创建

        %% 记录集合确保完成的日志
        %% 表明集合创建准备工作已完成
        ?SLOG(info, #{msg => "packet_id_collections_ensured",
                     packet_id_collection => PacketIdCollection})
    catch
        %% 捕获集合创建过程中的所有异常
        %% E: 异常类别（error、exit、throw）
        %% R: 异常原因
        %% S: 调用栈信息
        E:R:S ->
            %% 记录详细的错误信息，包含完整的上下文
            %% 这有助于问题诊断和调试
            ?SLOG(error, #{
                msg => "error_ensuring_packet_id_collections",
                error => E,                    %% 异常类别
                reason => R,                   %% 异常原因
                stacktrace => S                %% 调用栈，用于调试
            }),
            %% 返回结构化的错误信息
            %% 在Java中相当于：
            %% throw new CollectionCreationException("Failed to ensure collections", e);
            {error, {ensure_collections_failed, R}}
    end.

%%--------------------------------------------------------------------
%% 包ID管理函数
%%--------------------------------------------------------------------

%% @doc 记录持久化包ID信息
%% 修复：采用方案A - 纯持久化方案
%%
%% 功能说明：
%% 1. 记录客户端的持久化包ID详细信息
%% 2. 不直接管理包ID分配，避免与EMQX内置包ID管理器冲突
%% 3. 供管理员参考和故障排查使用
%%
%% 参数说明：
%% - ClientId: 客户端ID
%%
%% Java等价概念：
%% public void logPersistedPacketIdInfo(String clientId) {
%%     List<PacketIdState> packetIds = packetIdRepository.findByClientId(clientId);
%%     if (!packetIds.isEmpty()) {
%%         logger.warn("Client {} has {} persisted packet IDs after reconnect", clientId, packetIds.size());
%%         for (PacketIdState state : packetIds) {
%%             logger.warn("  - Packet ID: id={}, allocated_at={}, expires_at={}",
%%                        state.getPacketId(), state.getAllocatedAt(), state.getExpiresAt());
%%         }
%%     }
%% }
log_persisted_packet_id_info(ClientId) ->
    try
        Collection = get_packet_id_collection(),

        ?SLOG(debug, #{
            msg => "checking_persisted_packet_ids",
            client_id => ClientId
        }),

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {find, Collection,
                                #{<<"client_id">> => ClientId}}) of
            {ok, PacketIdDocs} when is_list(PacketIdDocs), length(PacketIdDocs) > 0 ->
                PacketIdCount = length(PacketIdDocs),
                ?SLOG(warning, #{
                    msg => "client_has_persisted_packet_ids_after_reconnect",
                    client_id => ClientId,
                    packet_id_count => PacketIdCount,
                    note => "packet_ids_will_be_handled_by_emqx_builtin_manager"
                }),

                %% 记录每个包ID的详细信息（限制数量避免日志过多）
                MaxLogPacketIds = 10,
                PacketIdsToLog = case PacketIdCount > MaxLogPacketIds of
                    true -> lists:sublist(PacketIdDocs, MaxLogPacketIds);
                    false -> PacketIdDocs
                end,

                lists:foreach(fun(PacketIdDoc) ->
                    try
                        PacketId = maps:get(<<"packet_id">>, PacketIdDoc, 0),
                        AllocatedAt = maps:get(<<"allocated_at">>, PacketIdDoc, 0),
                        ExpiresAt = maps:get(<<"expires_at">>, PacketIdDoc, 0),

                        ?SLOG(warning, #{
                            msg => "persisted_packet_id_details_after_reconnect",
                            client_id => ClientId,
                            packet_id => PacketId,
                            allocated_at => AllocatedAt,
                            expires_at => ExpiresAt,
                            note => "will_be_handled_by_emqx_builtin_manager_when_needed"
                        })
                    catch
                        E:R:S ->
                            ?SLOG(error, #{
                                msg => "error_logging_packet_id_details",
                                client_id => ClientId,
                                error => E,
                                reason => R,
                                stacktrace => S,
                                packet_id_doc => PacketIdDoc
                            })
                    end
                end, PacketIdsToLog),

                %% 如果包ID数量超过限制，记录省略信息
                case PacketIdCount > MaxLogPacketIds of
                    true ->
                        ?SLOG(warning, #{
                            msg => "additional_persisted_packet_ids_not_logged",
                            client_id => ClientId,
                            logged_count => MaxLogPacketIds,
                            total_count => PacketIdCount,
                            omitted_count => PacketIdCount - MaxLogPacketIds
                        });
                    false ->
                        ok
                end;
            {ok, []} ->
                ?SLOG(debug, #{
                    msg => "no_persisted_packet_ids_found",
                    client_id => ClientId
                });
            {ok, PacketIdDocs} when is_list(PacketIdDocs) ->
                ?SLOG(debug, #{
                    msg => "no_persisted_packet_ids_found",
                    client_id => ClientId,
                    count => length(PacketIdDocs)
                });
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_check_persisted_packet_ids",
                    client_id => ClientId,
                    reason => Reason
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_checking_persisted_packet_ids",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 为客户端分配一个新的包ID
%% 警告：这个函数在方案A中不应该被调用！
%%
%% 修复说明：
%% - 这个函数违反了方案A的设计原则（插件不应该直接管理EMQX包ID分配）
%% - 保留此函数仅用于向后兼容，但会记录警告日志
%% - 在新的方案A中，应该让EMQX的内置包ID管理器处理分配
allocate_packet_id(ClientId) ->
    %% 记录警告：这个函数不应该在方案A中被调用
    ?SLOG(warning, #{
        msg => "allocate_packet_id_called_violates_plan_a",
        client_id => ClientId,
        note => "this_function_should_not_be_called_in_pure_persistence_approach"
    }),

    try
        %% 获取下一个可用的包ID（违反方案A原则）
        case get_next_packet_id(ClientId) of
            {ok, PacketId} ->
                %% 保存包ID状态
                save_packet_id_state(ClientId, PacketId),
                {ok, PacketId};
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_allocate_packet_id",
                    client_id => ClientId,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_allocating_packet_id",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 释放客户端的包ID
%% 警告：这个函数在方案A中不应该被调用！
%%
%% 修复说明：
%% - 这个函数违反了方案A的设计原则（插件不应该直接管理EMQX包ID释放）
%% - 保留此函数仅用于向后兼容，但会记录警告日志
%% - 在新的方案A中，应该让EMQX的内置包ID管理器处理释放
release_packet_id(ClientId, PacketId) ->
    %% 记录警告：这个函数不应该在方案A中被调用
    ?SLOG(warning, #{
        msg => "release_packet_id_called_violates_plan_a",
        client_id => ClientId,
        packet_id => PacketId,
        note => "this_function_should_not_be_called_in_pure_persistence_approach"
    }),

    try
        Collection = get_packet_id_collection(),

        %% 删除包ID记录（违反方案A原则）
        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {delete_one, Collection,
                                #{<<"client_id">> => ClientId, <<"packet_id">> => PacketId}}) of
            {ok, _} ->
                ?SLOG(debug, #{
                    msg => "packet_id_released_but_violates_plan_a",
                    client_id => ClientId,
                    packet_id => PacketId
                }),
                ok;
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_release_packet_id",
                    client_id => ClientId,
                    packet_id => PacketId,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_releasing_packet_id",
                client_id => ClientId,
                packet_id => PacketId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 获取客户端的下一个可用包ID
%% 警告：这个函数在方案A中不应该被调用！
%%
%% 修复说明：
%% - 这个函数违反了方案A的设计原则（插件不应该直接管理EMQX包ID分配）
%% - 保留此函数仅用于向后兼容，但会记录警告日志
%% - 在新的方案A中，应该让EMQX的内置包ID管理器处理分配
get_next_packet_id(ClientId) ->
    %% 记录警告：这个函数不应该在方案A中被调用
    ?SLOG(warning, #{
        msg => "get_next_packet_id_called_violates_plan_a",
        client_id => ClientId,
        note => "this_function_should_not_be_called_in_pure_persistence_approach"
    }),

    try
        Collection = get_packet_id_collection(),

        %% 查询客户端已使用的包ID（违反方案A原则）
        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {find, Collection,
                                #{<<"client_id">> => ClientId},
                                #{<<"packet_id">> => 1}}) of
            {ok, UsedPacketIds} ->
                %% 提取已使用的包ID列表
                UsedIds = [maps:get(<<"packet_id">>, Doc) || Doc <- UsedPacketIds],

                %% 找到下一个可用的包ID
                find_available_packet_id(UsedIds);
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_query_used_packet_ids",
                    client_id => ClientId,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_getting_next_packet_id",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 找到可用的包ID
%% 修复：使用高效的包ID分配算法，支持MQTT协议最佳实践
find_available_packet_id(UsedIds) ->
    find_available_packet_id_efficient(UsedIds).

%% @doc 高效的包ID分配算法
%% 使用集合数据结构提高查找效率，从O(n²)优化到O(n log n)
find_available_packet_id_efficient(UsedIds) ->
    % 将列表转换为集合，提高查找效率
    UsedIdSet = sets:from_list(UsedIds),
    find_available_packet_id_from_set(?MIN_PACKET_ID, UsedIdSet).

find_available_packet_id_from_set(PacketId, UsedIdSet) when PacketId =< ?MAX_PACKET_ID ->
    case sets:is_element(PacketId, UsedIdSet) of
        false ->
            {ok, PacketId};
        true ->
            find_available_packet_id_from_set(PacketId + 1, UsedIdSet)
    end;
find_available_packet_id_from_set(_, _) ->
    {error, no_available_packet_id}.

%% @doc 循环包ID分配算法
%% 实现MQTT协议推荐的包ID重用策略
find_available_packet_id_circular(UsedIds, LastUsedId) ->
    UsedIdSet = sets:from_list(UsedIds),
    StartId = case LastUsedId of
        undefined -> ?MIN_PACKET_ID;
        Id when Id >= ?MAX_PACKET_ID -> ?MIN_PACKET_ID;
        Id -> Id + 1
    end,

    % 从上次使用的ID开始查找
    case find_available_packet_id_from_range(StartId, ?MAX_PACKET_ID, UsedIdSet) of
        {ok, PacketId} -> {ok, PacketId};
        {error, _} ->
            % 如果从StartId到最大值没找到，从最小值开始查找
            case StartId > ?MIN_PACKET_ID of
                true -> find_available_packet_id_from_range(?MIN_PACKET_ID, StartId - 1, UsedIdSet);
                false -> {error, no_available_packet_id}
            end
    end.

find_available_packet_id_from_range(StartId, EndId, UsedIdSet) when StartId =< EndId ->
    case sets:is_element(StartId, UsedIdSet) of
        false -> {ok, StartId};
        true -> find_available_packet_id_from_range(StartId + 1, EndId, UsedIdSet)
    end;
find_available_packet_id_from_range(_, _, _) ->
    {error, no_available_packet_id}.

%% 保留原始函数以向后兼容
find_available_packet_id(PacketId, UsedIds) when PacketId =< ?MAX_PACKET_ID ->
    case lists:member(PacketId, UsedIds) of
        false ->
            {ok, PacketId};
        true ->
            find_available_packet_id(PacketId + 1, UsedIds)
    end;
find_available_packet_id(_, _) ->
    {error, no_available_packet_id}.

%% @doc 保存包ID状态
%% 修复：增强包ID状态管理，支持MQTT 5.0 Receive Maximum
save_packet_id_state(ClientId, PacketId) ->
    save_packet_id_state_with_options(ClientId, PacketId, #{}).

%% @doc 保存包ID状态（带选项）
%% 支持MQTT 5.0协议的完整包ID管理
save_packet_id_state_with_options(ClientId, PacketId, Options) ->
    try
        Collection = get_packet_id_collection(),
        Now = erlang:system_time(millisecond),

        % 解析MQTT 5.0选项
        ReceiveMaximum = maps:get(receive_maximum, Options, 65535),
        QoS = maps:get(qos, Options, 1),
        MessageType = maps:get(message_type, Options, <<"publish">>),
        SessionExpiryInterval = maps:get(session_expiry_interval, Options, undefined),

        % 检查Receive Maximum限制
        case check_receive_maximum_limit(ClientId, ReceiveMaximum) of
            ok ->
                % 计算包ID过期时间（考虑会话过期时间）
                ExpiryTime = calculate_packet_id_expiry_time(SessionExpiryInterval),

                PacketIdDoc = #{
                    <<"client_id">> => ClientId,
                    <<"packet_id">> => PacketId,
                    <<"qos">> => QoS,
                    <<"message_type">> => MessageType,
                    <<"state">> => <<"allocated">>,
                    <<"allocated_at">> => Now,
                    <<"expires_at">> => ExpiryTime,
                    <<"last_updated">> => Now,

                    % MQTT 5.0属性
                    <<"receive_maximum">> => ReceiveMaximum,
                    <<"session_expiry_interval">> => SessionExpiryInterval,

                    % 状态跟踪
                    <<"in_use_at">> => undefined,
                    <<"acknowledged_at">> => undefined,
                    <<"released_at">> => undefined,

                    % 节点信息
                    <<"node">> => atom_to_binary(node(), utf8)
                },

        % 插入包ID记录
        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {insert_one, Collection, PacketIdDoc}) of
            {ok, _} ->
                ?SLOG(debug, #{
                    msg => "packet_id_state_saved",
                    client_id => ClientId,
                    packet_id => PacketId
                }),
                ok;
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_save_packet_id_state",
                    client_id => ClientId,
                    packet_id => PacketId,
                    reason => Reason
                }),
                {error, Reason}
            end;
        {error, receive_maximum_exceeded} ->
            ?SLOG(warning, #{
                msg => "packet_id_allocation_blocked_by_receive_maximum",
                client_id => ClientId,
                packet_id => PacketId
            }),
            {error, receive_maximum_exceeded}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_saving_packet_id_state",
                client_id => ClientId,
                packet_id => PacketId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 恢复客户端的包ID状态
%% 警告：这个函数在方案A中不应该被调用！
%%
%% 修复说明：
%% - 这个函数违反了方案A的设计原则（插件不应该直接恢复EMQX包ID状态）
%% - 保留此函数仅用于向后兼容，但会记录警告日志
%% - 在新的方案A中，应该让EMQX的内置包ID管理器处理状态恢复
restore_packet_id_state(ClientId) ->
    %% 记录警告：这个函数不应该在方案A中被调用
    ?SLOG(warning, #{
        msg => "restore_packet_id_state_called_violates_plan_a",
        client_id => ClientId,
        note => "this_function_should_not_be_called_in_pure_persistence_approach"
    }),

    try
        Collection = get_packet_id_collection(),

        %% 查询客户端的包ID状态（违反方案A原则）
        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {find, Collection,
                                #{<<"client_id">> => ClientId}}) of
            {ok, PacketIdDocs} ->
                PacketIds = [maps:get(<<"packet_id">>, Doc) || Doc <- PacketIdDocs],
                ?SLOG(info, #{
                    msg => "packet_id_state_restored_but_violates_plan_a",
                    client_id => ClientId,
                    packet_ids => PacketIds
                }),
                {ok, PacketIds};
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_restore_packet_id_state",
                    client_id => ClientId,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_restoring_packet_id_state",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 清理过期的包ID
cleanup_expired_packet_ids() ->
    ?SLOG(info, #{msg => "cleaning_expired_packet_ids"}),
    spawn(fun() -> do_cleanup_expired_packet_ids() end),
    % 重新启动定时器
    start_cleanup_timer(),
    ok.

%% @doc 执行过期包ID清理
do_cleanup_expired_packet_ids() ->
    try
        Collection = get_packet_id_collection(),
        Now = erlang:system_time(millisecond),

        % 删除已过期的包ID
        Filter = #{<<"expires_at">> => #{<<"$lt">> => Now}},

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {delete_many, Collection, Filter}) of
            {ok, #{<<"deletedCount">> := DeletedCount}} ->
                ?SLOG(info, #{msg => "expired_packet_ids_cleaned", count => DeletedCount});
            {error, Reason} ->
                ?SLOG(error, #{msg => "failed_to_clean_expired_packet_ids", reason => Reason})
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_cleaning_expired_packet_ids",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%%--------------------------------------------------------------------
%% 辅助函数
%%--------------------------------------------------------------------

%% @doc 获取包ID集合名称
get_packet_id_collection() ->
    Config = application:get_env(emqx_plugin_mongodb, config, #{}),
    PacketIdConfig = maps:get(packet_id_persistence, Config, #{}),
    maps:get(collection, PacketIdConfig, ?DEFAULT_PACKET_ID_COLLECTION).

%% @doc 获取包ID过期时间
get_packet_id_expiry() ->
    Config = application:get_env(emqx_plugin_mongodb, config, #{}),
    PacketIdConfig = maps:get(packet_id_persistence, Config, #{}),
    maps:get(packet_id_expiry, PacketIdConfig, 3600000). % 默认1小时

%% @doc 启动清理定时器
start_cleanup_timer() ->
    CleanupInterval = ?DEFAULT_CLEANUP_INTERVAL,
    ?SLOG(info, #{msg => "starting_packet_id_cleanup_timer", interval => CleanupInterval}),
    erlang:send_after(CleanupInterval, self(), cleanup_expired_packet_ids),
    ok.

%% @doc 等待MongoDB资源就绪
wait_for_resource() ->
    wait_for_resource(10).

wait_for_resource(0) ->
    ?SLOG(warning, #{msg => "mongodb_resource_not_ready_after_retries"}),
    ok;
wait_for_resource(Retries) ->
    case emqx_resource:health_check(?PLUGIN_MONGODB_RESOURCE_ID) of
        ok ->
            ?SLOG(info, #{msg => "mongodb_resource_ready"}),
            ok;
        {ok, _} ->
            ?SLOG(info, #{msg => "mongodb_resource_ready"}),
            ok;
        _ ->
            ?SLOG(warning, #{msg => "mongodb_resource_not_ready", retries_left => Retries}),
            timer:sleep(1000),
            wait_for_resource(Retries - 1)
    end.

%%--------------------------------------------------------------------
%% MQTT 5.0包ID管理辅助函数
%%--------------------------------------------------------------------

%% @doc 检查Receive Maximum限制
%% MQTT 5.0协议：检查客户端是否超过了Receive Maximum限制
check_receive_maximum_limit(ClientId, ReceiveMaximum) ->
    try
        Collection = get_packet_id_collection(),

        % 查询客户端当前未确认的包ID数量
        Filter = #{
            <<"client_id">> => ClientId,
            <<"state">> => #{<<"$in">> => [<<"allocated">>, <<"in_use">>]}
        },

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {count_documents, Collection, Filter}) of
            {ok, CurrentCount} ->
                case CurrentCount >= ReceiveMaximum of
                    true ->
                        ?SLOG(warning, #{
                            msg => "receive_maximum_limit_exceeded",
                            client_id => ClientId,
                            current_count => CurrentCount,
                            receive_maximum => ReceiveMaximum
                        }),
                        {error, receive_maximum_exceeded};
                    false ->
                        ok
                end;
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_check_receive_maximum_limit",
                    client_id => ClientId,
                    reason => Reason
                }),
                ok % 检查失败时允许继续，避免阻塞
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_checking_receive_maximum_limit",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            ok % 异常时允许继续
    end.

%% @doc 计算包ID过期时间
%% 考虑会话过期时间和包ID配置
calculate_packet_id_expiry_time(undefined) ->
    % 没有会话过期时间，使用包ID配置
    erlang:system_time(millisecond) + get_packet_id_expiry();
calculate_packet_id_expiry_time(SessionExpiryInterval) when is_integer(SessionExpiryInterval) ->
    % 使用会话过期时间和包ID过期时间的较小值
    SessionExpiryMs = SessionExpiryInterval * 1000,
    PacketIdExpiryMs = get_packet_id_expiry(),
    ExpiryMs = min(SessionExpiryMs, PacketIdExpiryMs),
    erlang:system_time(millisecond) + ExpiryMs;
calculate_packet_id_expiry_time(_) ->
    % 无效值，使用包ID配置
    erlang:system_time(millisecond) + get_packet_id_expiry().

%% @doc 更新包ID状态
%% 支持包ID生命周期状态管理
update_packet_id_state(ClientId, PacketId, NewState) ->
    update_packet_id_state_with_data(ClientId, PacketId, NewState, #{}).

update_packet_id_state_with_data(ClientId, PacketId, NewState, UpdateData) ->
    try
        Collection = get_packet_id_collection(),
        Now = erlang:system_time(millisecond),

        % 构建更新文档
        UpdateDoc = maps:merge(#{
            <<"state">> => atom_to_binary(NewState, utf8),
            <<"last_updated">> => Now
        }, UpdateData),

        % 根据状态设置特定字段
        FinalUpdateDoc = case NewState of
            in_use ->
                UpdateDoc#{<<"in_use_at">> => Now};
            acknowledged ->
                UpdateDoc#{<<"acknowledged_at">> => Now};
            released ->
                UpdateDoc#{<<"released_at">> => Now};
            _ ->
                UpdateDoc
        end,

        Filter = #{
            <<"client_id">> => ClientId,
            <<"packet_id">> => PacketId
        },

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {update_one, Collection, Filter, #{<<"$set">> => FinalUpdateDoc}}) of
            {ok, _} ->
                ?SLOG(debug, #{
                    msg => "packet_id_state_updated",
                    client_id => ClientId,
                    packet_id => PacketId,
                    new_state => NewState
                }),
                ok;
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_update_packet_id_state",
                    client_id => ClientId,
                    packet_id => PacketId,
                    new_state => NewState,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_updating_packet_id_state",
                client_id => ClientId,
                packet_id => PacketId,
                new_state => NewState,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 获取客户端的包ID统计信息
get_packet_id_stats(ClientId) ->
    try
        Collection = get_packet_id_collection(),

        % 聚合查询获取统计信息
        Pipeline = [
            #{<<"$match">> => #{<<"client_id">> => ClientId}},
            #{<<"$group">> => #{
                <<"_id">> => <<"$state">>,
                <<"count">> => #{<<"$sum">> => 1}
            }}
        ],

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {aggregate, Collection, Pipeline}) of
            {ok, Results} ->
                % 转换结果为易读格式
                Stats = lists:foldl(fun(#{<<"_id">> := State, <<"count">> := Count}, Acc) ->
                    Acc#{State => Count}
                end, #{}, Results),
                {ok, Stats};
            {error, Reason} ->
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_getting_packet_id_stats",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.
