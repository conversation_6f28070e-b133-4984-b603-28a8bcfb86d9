%%%-------------------------------------------------------------------
%%% @doc
%%% EMQX MongoDB插件扩展API模块 - 高级数据库操作封装
%%%
%%% 这个模块是MongoDB操作的核心封装层，类似于Java中的Repository + Service组合。
%%% 在Java Spring Boot项目中，这相当于：
%%%
%%% @Repository
%%% @Service
%%% public class MongodbApiService {
%%%     @Autowired private MongoTemplate mongoTemplate;
%%%     @Autowired private RetryTemplate retryTemplate;
%%%     @Autowired private HealthIndicator healthIndicator;
%%% }
%%%
%%% 主要功能模块：
%%% 1. 批量操作API - 类似于Spring Data的批量操作
%%% 2. 智能重试机制 - 类似于Spring Retry
%%% 3. 连接健康检查 - 类似于Spring Actuator Health Check
%%% 4. 事务支持 - 类似于@Transactional
%%% 5. 索引管理 - 类似于MongoDB索引管理工具
%%% 6. 性能监控 - 类似于Micrometer指标收集
%%% 7. 数据聚合 - 类似于MongoDB Aggregation Pipeline
%%% 8. 错误分类和处理 - 类似于异常处理策略
%%% 9. 会话管理 - 类似于数据库连接池管理
%%% 10. 消息持久化优化 - 专门为MQTT消息优化的存储策略
%%%
%%% @end
%%%-------------------------------------------------------------------
-module(emqx_plugin_mongodb_api).

-include("emqx_plugin_mongodb.hrl").
-include_lib("mongodb/include/mongo_types.hrl").

%% ============================================================================
%% 批量操作API - 类似于Java Spring Data的批量操作
%% 在Java中相当于：
%% public interface BulkOperations {
%%     BulkWriteResult bulkInsert(String collection, List<Document> documents);
%%     BulkWriteResult bulkUpsert(String collection, List<Document> documents);
%%     BulkWriteResult bulkDelete(String collection, List<Query> queries);
%% }
%% ============================================================================
-export([
    bulk_insert/3,          % 批量插入 - bulkInsert(Connection, Collection, Documents)
    bulk_insert/4,          % 带选项的批量插入 - bulkInsert(Connection, Collection, Documents, Options)
    bulk_upsert/3,          % 批量更新插入 - bulkUpsert(Connection, Collection, Documents)
    bulk_upsert/4,          % 带选项的批量更新插入
    bulk_delete/3,          % 批量删除 - bulkDelete(Connection, Collection, Filters)
    bulk_delete/4,          % 带选项的批量删除
    bulk_mixed_operations/3, % 混合批量操作 - 支持插入、更新、删除的组合操作
    bulk_replace/3,         % 批量替换 - bulkReplace(Connection, Collection, Documents)
    bulk_replace/4          % 带选项的批量替换
]).

%% ============================================================================
%% 智能重试和错误处理API - 类似于Spring Retry + 异常处理策略
%% 在Java中相当于：
%% @Service
%% public class RetryableMongoService {
%%     @Retryable(value = {TransientException.class}, maxAttempts = 3)
%%     public Result safeExecute(Operation operation);
%% }
%% ============================================================================
-export([
    safe_execute/3,             % 安全执行 - safeExecute(Connection, Operation, Timeout)
    safe_execute/4,             % 带重试选项的安全执行
    retry_operation/3,          % 重试操作 - retryOperation(Operation, MaxRetries, BackoffStrategy)
    classify_error/1,           % 错误分类 - classifyError(Exception) -> ErrorType
    is_retryable_error/1,       % 判断是否可重试错误 - isRetryableError(Exception) -> boolean
    handle_error_with_fallback/3 % 带降级的错误处理 - handleErrorWithFallback(Error, FallbackAction, Context)
]).

%% ============================================================================
%% 连接和健康检查API - 类似于Spring Actuator Health Indicators
%% 在Java中相当于：
%% @Component
%% public class MongoHealthIndicator implements HealthIndicator {
%%     public Health health() { ... }
%% }
%% ============================================================================
-export([
    ping/1,                     % 简单ping检查 - ping(Connection)
    ping_with_timeout/2,        % 带超时的ping检查 - ping(Connection, TimeoutMs)
    check_connection_health/1,  % 检查连接健康状态 - checkHealth(Connection) -> HealthStatus
    get_server_status/1,        % 获取服务器状态 - getServerStatus(Connection) -> ServerInfo
    get_replica_set_status/1,   % 获取副本集状态 - getReplicaSetStatus(Connection) -> ReplicaSetInfo
    test_write_concern/2,       % 测试写关注 - testWriteConcern(Connection, WriteConcern)
    validate_connection/1,      % 验证连接 - validateConnection(Connection) -> ValidationResult

    % MongoDB版本兼容性检查
    detect_mongodb_version_async/1,  % 异步检测MongoDB版本
    check_mongodb_version_compatibility/1,  % 检查版本兼容性
    parse_mongodb_version/1     % 解析版本字符串
]).

%% 高级查询和聚合API
-export([
    find_with_cursor/4, find_with_cursor/5,
    find_one_safe/3, find_one_safe/4,
    find_safe/6,  % 新增find_safe函数
    aggregate_pipeline/3, aggregate_pipeline/4,
    count_documents/3, count_documents/4,
    distinct_values/3, distinct_values/4,
    explain_query/3,
    text_search/4
]).

%% 索引管理API
-export([
    ensure_index/3, ensure_index/4,
    list_indexes/2,
    drop_index/3,
    get_index_stats/2,
    optimize_indexes/2,
    create_compound_index/3,
    create_text_index/3,
    create_geospatial_index/3
]).

%% 基础MongoDB操作API
-export([
    command/2, command/3,
    insert/3, insert/4,
    update/5, update/6,
    find/4, find/5,
    find_one/3, find_one/4,
    delete/3, delete/4,
    delete_one/2, delete_one/3,
    delete_many/3, delete_many/4,
    upsert/4, upsert/5,
    replace/4, replace/5
]).

%% 高级MongoDB操作API
-export([
    execute_command/2, execute_command/3,
    smart_insert/3, smart_insert/4,
    smart_update/4, smart_update/5,
    smart_find/3, smart_find/4
]).

%% 连接管理API
-export([
    connect_with_options/1,
    connect_with_event_wrapper/1,
    disconnect_safe/1,
    get_mongodb_connection/0,
    find/3, find/4, find/5,
    find_one_safe/3, find_one_safe/4,
    find_documents/2, find_documents/3,
    delete_many/2
]).

%% 基础辅助函数
-export([
    split_into_batches/2,
    merge_bulk_results/1,
    generate_index_name/1,
    validate_documents/1,
    validate_connection/1
]).

%% 部署模式支持API
-export([
    detect_deployment_mode/1,
    get_deployment_config/2,
    optimize_for_deployment/2,
    get_read_preference/2,
    get_write_concern/2,
    get_optimal_read_preference/2,
    get_optimal_write_concern/2
]).

%% 故障转移和负载均衡API
-export([
    handle_failover/3,
    select_optimal_connection/2,
    get_connection_health_score/1,
    rebalance_connections/2,
    handle_topology_change/2
]).

%% 分片集群特定API
-export([
    get_shard_key/2,
    route_to_shard/3,
    get_shard_distribution/1,
    optimize_shard_queries/2
]).

%% 副本集特定API
-export([
    get_primary_connection/1,
    get_secondary_connections/1,
    handle_primary_election/2,
    balance_read_load/2
]).

%% 数据恢复API
-export([
    trigger_data_restoration_on_connection/0,
    manual_trigger_data_restoration/0
]).

%% 数据类型定义

-type bulk_operation() :: {insert, map()} |
                         {update, map(), map()} |
                         {upsert, map(), map()} |
                         {replace, map(), map()} |
                         {delete, map()}.

-type bulk_result() :: #{
    inserted_count => integer(),
    modified_count => integer(),
    deleted_count => integer(),
    upserted_count => integer(),
    matched_count => integer(),
    errors => [map()]
}.

-type retry_options() :: #{
    max_attempts => integer(),
    initial_delay => integer(),
    max_delay => integer(),
    backoff_factor => float(),
    retry_on => [atom()]
}.

-type health_status() :: #{
    status => ok | warning | error | critical,
    latency => integer() | undefined,
    last_error => term() | undefined,
    server_info => map(),
    connection_pool_status => map(),
    check_time => integer()
}.

-type session_options() :: #{
    read_concern => map(),
    write_concern => map(),
    read_preference => map(),
    max_commit_time_ms => integer()
}.

-type query_options() :: #{
    limit => integer(),
    skip => integer(),
    sort => map(),
    projection => map(),
    batch_size => integer(),
    max_time_ms => integer(),
    hint => map() | binary(),
    read_preference => map()
}.

%% 部署模式相关类型
-type deployment_mode() :: single | {replica_set, binary()} | {sharded_cluster, map()}.
-type read_preference() :: primary | primary_preferred | secondary | secondary_preferred | nearest.
-type write_concern_level() :: #{
    w => integer() | binary() | majority,
    j => boolean(),
    wtimeout => integer()
}.
-type connection_health() :: #{
    latency => integer(),
    error_rate => float(),
    load => float(),
    last_check => integer()
}.
-type shard_info() :: #{
    shard_id => binary(),
    host => binary(),
    port => integer(),
    tags => map()
}.

%%%===================================================================
%%% 批量操作API
%%%===================================================================

%% @doc 高性能批量插入操作 - 类似于Java Spring Data的批量插入
%%
%% 在Java中的等价实现：
%% @Service
%% public class MongoBulkService {
%%     public BulkWriteResult bulkInsert(String collection, List<Document> documents) {
%%         return bulkInsert(collection, documents, BulkOptions.defaultOptions());
%%     }
%%
%%     public BulkWriteResult bulkInsert(String collection, List<Document> documents, BulkOptions options) {
%%         try {
%%             validateDocuments(documents);
%%             List<List<Document>> batches = splitIntoBatches(documents, options.getBatchSize());
%%
%%             List<BulkWriteResult> results = batches.stream()
%%                 .map(batch -> executeInsertBatch(collection, batch, options))
%%                 .collect(Collectors.toList());
%%
%%             return mergeBulkResults(results);
%%         } catch (Exception e) {
%%             logger.error("Bulk insert failed", e);
%%             return BulkWriteResult.failure(e);
%%         }
%%     }
%% }
%%
%% @param Connection MongoDB连接进程ID - 类似于Java的MongoTemplate实例
%% @param Collection 集合名称 - 类似于Java的collection name
%% @param Documents 要插入的文档列表 - 类似于Java的List<Document>
%% @return 批量操作结果 - 类似于Java的BulkWriteResult
%% @doc 高性能批量插入操作（简化版本）
%% 这是批量插入的便捷方法，使用默认选项执行批量插入操作
%%
%% 功能说明：
%% 1. 使用默认的批量插入选项（批处理大小、写关注等）
%% 2. 内部调用带选项的bulk_insert/4函数
%% 3. 适用于大多数常见的批量插入场景
%%
%% 参数说明：
%% - Connection: MongoDB连接进程ID，类似于Java中的MongoTemplate实例
%% - Collection: 目标集合名称，类似于Java中的collection name
%% - Documents: 要插入的文档列表，类似于Java中的List<Document>
%%
%% 返回值：
%% - bulk_result(): 批量操作结果映射，包含插入计数、错误信息等
%%
%% 使用场景：
%% - 简单的批量数据插入
%% - 不需要特殊配置的批量操作
%% - 快速原型开发和测试
-spec bulk_insert(pid(), binary(), [map()]) -> bulk_result().
bulk_insert(Connection, Collection, Documents) ->
    %% 调用带默认选项的重载方法 - 类似于Java的方法重载
    %% 这种设计模式在Java中很常见：
    %% public Result method(param1, param2) {
    %%     return method(param1, param2, defaultOptions);
    %% }
    bulk_insert(Connection, Collection, Documents, #{}).

%% @doc 带选项的批量插入操作（完整版本）
%% 这是批量插入的完整实现，支持自定义选项和高级配置
%%
%% 功能说明：
%% 1. 支持自定义批处理大小、写关注级别等选项
%% 2. 自动分批处理大量文档，避免内存溢出
%% 3. 支持有序和无序插入模式
%% 4. 提供完整的错误处理和结果合并
%%
%% 参数说明：
%% - Connection: MongoDB连接进程ID，类似于Java中的MongoTemplate
%% - Collection: 目标集合名称，类似于Java中的collection name
%% - Documents: 要插入的文档列表，类似于Java中的List<Document>
%% - Options: 批量操作选项映射，类似于Java的BulkOptions配置对象
%%   * batch_size: 批处理大小（默认1000）
%%   * ordered: 是否有序插入（默认false）
%%   * write_concern: 写关注级别（默认w:1）
%%   * bypass_document_validation: 是否跳过文档验证（默认false）
%%
%% 返回值：
%% - bulk_result(): 批量操作结果映射，包含：
%%   * inserted_count: 成功插入的文档数量
%%   * errors: 错误信息列表
%%
%% 使用场景：
%% - 需要自定义批处理配置的场景
%% - 大量数据的高性能插入
%% - 需要特定写关注级别的场景
-spec bulk_insert(pid(), binary(), [map()], map()) -> bulk_result().
bulk_insert(Connection, Collection, Documents, Options) ->
    %% 设置默认选项 - 类似于Java的Builder模式默认值
    %% 在Java中相当于：
    %% BulkOptions defaultOptions = BulkOptions.builder()
    %%     .batchSize(1000)
    %%     .ordered(false)
    %%     .writeConcern(WriteConcern.W1)
    %%     .bypassDocumentValidation(false)
    %%     .build();
    %% 设置默认选项 - 类似于Java的Builder模式默认值
    %% 在Java中相当于：
    %% BulkOptions defaultOptions = BulkOptions.builder()
    %%     .batchSize(1000)                    // 批处理大小，平衡内存使用和性能
    %%     .ordered(false)                     // 无序插入，允许并行处理提高性能
    %%     .writeConcern(WriteConcern.W1)      // 写关注级别，至少1个节点确认
    %%     .bypassDocumentValidation(false)   // 启用文档验证，确保数据完整性
    %%     .build();
    DefaultOptions = #{
        batch_size => 1000,                              % 批处理大小 - 每批最多1000个文档
        ordered => false,                                % 无序插入 - 提高性能，允许并行处理
        write_concern => #{<<"w">> => 1},               % 写关注级别 - 至少1个节点确认写入
        bypass_document_validation => false             % 不跳过文档验证 - 确保数据完整性
    },

    %% 合并用户选项和默认选项 - 类似于Java的Optional.orElse()
    %% 在Java中相当于：
    %% BulkOptions finalOptions = userOptions.isEmpty() ?
    %%     defaultOptions : defaultOptions.merge(userOptions);
    MergedOptions = maps:merge(DefaultOptions, Options),

    try
        %% 验证文档格式 - 类似于Java的@Valid注解验证
        %% 在Java中相当于：
        %% @Valid List<Document> documents
        %% if (!validator.isValid(documents)) {
        %%     throw new ValidationException("Invalid documents");
        %% }
        validate_documents(Documents),

        %% 获取批处理大小并分批 - 类似于Java的Lists.partition()
        %% 在Java中相当于：
        %% int batchSize = options.getBatchSize();
        %% List<List<Document>> batches = Lists.partition(documents, batchSize);
        BatchSize = maps:get(batch_size, MergedOptions),
        Batches = split_into_batches(Documents, BatchSize),

        %% 并行处理每个批次 - 类似于Java的parallelStream()
        %% 在Java中相当于：
        %% List<BulkWriteResult> results = batches.parallelStream()
        %%     .map(batch -> executeInsertBatch(collection, batch, options))
        %%     .collect(Collectors.toList());
        %%
        %% 这里使用lists:map/2进行顺序处理，每个批次调用execute_insert_batch/4
        Results = lists:map(
            fun(Batch) ->
                % 对每个批次执行插入操作
                % Batch是当前批次的文档列表
                % MergedOptions包含合并后的操作选项
                execute_insert_batch(Connection, Collection, Batch, MergedOptions)
            end,
            Batches  % 所有批次的列表
        ),

        %% 合并所有批次的结果 - 类似于Java的reduce操作
        %% 在Java中相当于：
        %% BulkWriteResult finalResult = results.stream()
        %%     .reduce(BulkWriteResult.empty(), BulkWriteResult::merge);
        merge_bulk_results(Results)
    catch
        %% 异常处理 - 类似于Java的try-catch块
        %% 在Java中相当于：
        %% } catch (Exception e) {
        %%     logger.error("Bulk insert failed", e);
        %%     return BulkWriteResult.failure(e);
        %% }
        E:R:Stack ->
            %% 记录详细的错误日志 - 类似于Java的Logger.error()
            %% 在Java中相当于：
            %% logger.error("Bulk insert failed for collection: {}, document count: {}",
            %%              collection, documents.size(), e);
            ?SLOG(error, #{
                msg => "bulk_insert_failed",
                error => E,                          % 异常类型（如error、throw、exit）
                reason => R,                         % 具体的错误原因
                stack => Stack,                      % 完整的堆栈跟踪信息
                collection => Collection,            % 目标集合名称
                doc_count => length(Documents)       % 文档数量，用于调试
            }),
            %% 返回标准化的失败结果 - 类似于Java的错误响应对象
            %% 在Java中相当于：
            %% return BulkWriteResult.builder()
            %%     .insertedCount(0)
            %%     .modifiedCount(0)
            %%     .deletedCount(0)
            %%     .upsertedCount(0)
            %%     .matchedCount(0)
            %%     .errors(Arrays.asList(new BulkWriteError(e)))
            %%     .build();
            #{
                inserted_count => 0,                % 插入成功的文档数量
                modified_count => 0,                % 修改的文档数量（插入操作中为0）
                deleted_count => 0,                 % 删除的文档数量（插入操作中为0）
                upserted_count => 0,                % 更新插入的文档数量（插入操作中为0）
                matched_count => 0,                 % 匹配的文档数量（插入操作中为0）
                errors => [#{error => E, reason => R}]  % 错误信息列表
            }
    end.

%% @doc 批量更新插入操作（Upsert）- 简化版本
%% Upsert = Update + Insert，如果文档存在则更新，不存在则插入
%%
%% 功能说明：
%% 1. 使用默认选项执行批量upsert操作
%% 2. 对每个操作，如果匹配的文档存在则更新，否则插入新文档
%% 3. 适用于数据同步和增量更新场景
%%
%% 参数说明：
%% - Connection: MongoDB连接进程ID
%% - Collection: 目标集合名称
%% - Operations: 更新插入操作列表，每个操作包含查询条件和更新内容
%%
%% 返回值：
%% - bulk_result(): 包含upserted_count、modified_count等字段的结果映射
%%
%% 使用场景：
%% - 数据同步和去重
%% - 增量数据更新
%% - 配置数据的批量更新
-spec bulk_upsert(pid(), binary(), [map()]) -> bulk_result().
bulk_upsert(Connection, Collection, Operations) ->
    %% 调用带默认选项的完整版本
    bulk_upsert(Connection, Collection, Operations, #{}).

%% @doc 批量更新插入操作（Upsert）- 完整版本
%% 这是批量upsert的完整实现，支持自定义选项和高级配置
%%
%% 功能说明：
%% 1. 支持自定义批处理大小、写关注级别等选项
%% 2. 自动分批处理大量操作，避免内存溢出
%% 3. 支持有序和无序upsert模式
%% 4. 提供完整的错误处理和结果合并
%%
%% 参数说明：
%% - Connection: MongoDB连接进程ID
%% - Collection: 目标集合名称
%% - Operations: 更新插入操作列表，格式：[{Filter, Update, Options}, ...]
%% - Options: 批量操作选项映射
%%
%% 返回值：
%% - bulk_result(): 批量操作结果，包含upserted_count、modified_count等
-spec bulk_upsert(pid(), binary(), [map()], map()) -> bulk_result().
bulk_upsert(Connection, Collection, Operations, Options) ->
    DefaultOptions = #{
        batch_size => 1000,
        write_concern => #{<<"w">> => 1},
        bypass_document_validation => false
    },
    MergedOptions = maps:merge(DefaultOptions, Options),
    
    try
        validate_upsert_operations(Operations),
        BatchSize = maps:get(batch_size, MergedOptions),
        Batches = split_into_batches(Operations, BatchSize),
        
        Results = lists:map(
            fun(Batch) ->
                execute_upsert_batch(Connection, Collection, Batch, MergedOptions)
            end,
            Batches
        ),
        merge_bulk_results(Results)
    catch
        E:R:Stack ->
            ?SLOG(error, #{
                msg => "bulk_upsert_failed",
                error => E,
                reason => R,
                stack => Stack,
                collection => Collection,
                op_count => length(Operations)
            }),
            #{
                inserted_count => 0,
                modified_count => 0,
                deleted_count => 0,
                upserted_count => 0,
                matched_count => 0,
                errors => [#{error => E, reason => R}]
            }
    end.

%% @doc 批量删除操作
-spec bulk_delete(pid(), binary(), [map()]) -> bulk_result().
bulk_delete(Connection, Collection, Selectors) ->
    bulk_delete(Connection, Collection, Selectors, #{}).

-spec bulk_delete(pid(), binary(), [map()], map()) -> bulk_result().
bulk_delete(Connection, Collection, Selectors, Options) ->
    DefaultOptions = #{
        batch_size => 1000,
        write_concern => #{<<"w">> => 1},
        limit => 0  % 0 = delete all matching, 1 = delete one
    },
    MergedOptions = maps:merge(DefaultOptions, Options),
    
    try
        validate_selectors(Selectors),
        BatchSize = maps:get(batch_size, MergedOptions),
        Batches = split_into_batches(Selectors, BatchSize),
        
        Results = lists:map(
            fun(Batch) ->
                execute_delete_batch(Connection, Collection, Batch, MergedOptions)
            end,
            Batches
        ),
        merge_bulk_results(Results)
    catch
        E:R:Stack ->
            ?SLOG(error, #{
                msg => "bulk_delete_failed",
                error => E,
                reason => R,
                stack => Stack,
                collection => Collection,
                selector_count => length(Selectors)
            }),
            #{
                inserted_count => 0,
                modified_count => 0,
                deleted_count => 0,
                upserted_count => 0,
                matched_count => 0,
                errors => [#{error => E, reason => R}]
            }
    end.

%% @doc 批量替换操作
-spec bulk_replace(pid(), binary(), [map()]) -> bulk_result().
bulk_replace(Connection, Collection, Operations) ->
    bulk_replace(Connection, Collection, Operations, #{}).

-spec bulk_replace(pid(), binary(), [map()], map()) -> bulk_result().
bulk_replace(Connection, Collection, Operations, Options) ->
    DefaultOptions = #{
        batch_size => 1000,
        write_concern => #{<<"w">> => 1},
        upsert => false
    },
    MergedOptions = maps:merge(DefaultOptions, Options),

    try
        validate_replace_operations(Operations),
        BatchSize = maps:get(batch_size, MergedOptions),
        Batches = split_into_batches(Operations, BatchSize),

        Results = lists:map(
            fun(Batch) ->
                execute_replace_batch(Connection, Collection, Batch, MergedOptions)
            end,
            Batches
        ),
        merge_bulk_results(Results)
    catch
        E:R:Stack ->
            ?SLOG(error, #{
                msg => "bulk_replace_failed",
                error => E,
                reason => R,
                stack => Stack,
                collection => Collection,
                op_count => length(Operations)
            }),
            #{
                inserted_count => 0,
                modified_count => 0,
                deleted_count => 0,
                upserted_count => 0,
                matched_count => 0,
                errors => [#{error => E, reason => R}]
            }
    end.

%% @doc 混合批量操作（插入、更新、删除、替换）
-spec bulk_mixed_operations(pid(), binary(), [bulk_operation()]) -> bulk_result().
bulk_mixed_operations(Connection, Collection, Operations) ->
    try
        % 按操作类型分组
        {Inserts, Updates, Deletes, Replaces} = group_mixed_operations(Operations),

        % 执行各类操作
        Results = [
            case Inserts of
                [] -> #{inserted_count => 0, errors => []};
                _ -> bulk_insert(Connection, Collection, Inserts)
            end,
            case Updates of
                [] -> #{modified_count => 0, upserted_count => 0, errors => []};
                _ -> bulk_upsert(Connection, Collection, Updates)
            end,
            case Deletes of
                [] -> #{deleted_count => 0, errors => []};
                _ -> bulk_delete(Connection, Collection, Deletes)
            end,
            case Replaces of
                [] -> #{matched_count => 0, errors => []};
                _ -> bulk_replace(Connection, Collection, Replaces)
            end
        ],

        merge_bulk_results(Results)
    catch
        E:R:Stack ->
            ?SLOG(error, #{
                msg => "bulk_mixed_operations_failed",
                error => E,
                reason => R,
                stack => Stack,
                collection => Collection,
                op_count => length(Operations)
            }),
            #{
                inserted_count => 0,
                modified_count => 0,
                deleted_count => 0,
                upserted_count => 0,
                matched_count => 0,
                errors => [#{error => E, reason => R}]
            }
    end.

%%%===================================================================
%%% 智能重试和错误处理API
%%%===================================================================

%% @doc 安全执行操作，带有自动重试
-spec safe_execute(pid(), fun(), retry_options()) -> {ok, term()} | {error, term()}.
safe_execute(Connection, Operation, Options) ->
    safe_execute(Connection, Operation, Options, undefined).

-spec safe_execute(pid(), fun(), retry_options(), term()) -> {ok, term()} | {error, term()}.
safe_execute(Connection, Operation, Options, Context) ->
    DefaultOptions = #{
        max_attempts => 3,
        initial_delay => 100,
        max_delay => 5000,
        backoff_factor => 2.0,
        retry_on => [connection_error, timeout_error, network_error]
    },
    MergedOptions = maps:merge(DefaultOptions, Options),

    retry_operation(Operation, MergedOptions, #{
        connection => Connection,
        context => Context
    }).

%% @doc 重试操作实现
-spec retry_operation(fun(), retry_options(), map()) -> {ok, term()} | {error, term()}.
retry_operation(Operation, Options, Context) ->
    MaxAttempts = maps:get(max_attempts, Options),
    InitialDelay = maps:get(initial_delay, Options),
    retry_loop(Operation, MaxAttempts, InitialDelay, Options, Context).



%% @doc 重试循环实现
retry_loop(Operation, AttemptsLeft, Delay, Options, Context) when AttemptsLeft > 0 ->
    try
        Result = Operation(),
        {ok, Result}
    catch
        E:R:Stack ->
            ErrorType = classify_error({E, R}),
            RetryOn = maps:get(retry_on, Options, []),

            case lists:member(ErrorType, RetryOn) andalso AttemptsLeft > 1 of
                true ->
                    ?SLOG(warning, #{
                        msg => "operation_failed_retrying",
                        error => E,
                        reason => R,
                        error_type => ErrorType,
                        attempts_left => AttemptsLeft - 1,
                        delay => Delay,
                        context => Context
                    }),
                    timer:sleep(Delay),
                    NextDelay = min(
                        maps:get(max_delay, Options, 5000),
                        round(Delay * maps:get(backoff_factor, Options, 2.0))
                    ),
                    retry_loop(Operation, AttemptsLeft - 1, NextDelay, Options, Context);
                false ->
                    ?SLOG(error, #{
                        msg => "operation_failed_permanently",
                        error => E,
                        reason => R,
                        error_type => ErrorType,
                        stack => Stack,
                        attempts_left => AttemptsLeft,
                        context => Context
                    }),
                    {error, {E, R}}
            end
    end;
retry_loop(_Operation, 0, _Delay, _Options, _Context) ->
    {error, max_attempts_exceeded}.

%% @doc 分类错误类型
-spec classify_error(term()) -> atom().
classify_error({error, {connection_error, _}}) -> connection_error;
classify_error({error, timeout}) -> timeout_error;
classify_error({error, {network_error, _}}) -> network_error;
classify_error({error, {authentication_error, _}}) -> auth_error;
classify_error({error, {write_concern_error, _}}) -> write_concern_error;
classify_error({error, {duplicate_key, _}}) -> duplicate_key_error;
classify_error({error, {validation_error, _}}) -> validation_error;
classify_error({error, {index_error, _}}) -> index_error;
classify_error({error, {transaction_error, _}}) -> transaction_error;
classify_error({timeout, _}) -> timeout_error;
classify_error({error, _}) -> general_error;
classify_error(_) -> unknown_error.

%% @doc 判断错误是否可重试
-spec is_retryable_error(term()) -> boolean().
is_retryable_error(Error) ->
    case classify_error(Error) of
        connection_error -> true;
        timeout_error -> true;
        network_error -> true;
        write_concern_error -> true;
        transaction_error -> true;
        general_error -> true;
        _ -> false
    end.

%% @doc 带回退策略的错误处理
-spec handle_error_with_fallback(term(), fun(), term()) -> {ok, term()} | {error, term()}.
handle_error_with_fallback(Error, FallbackFun, DefaultValue) ->
    case is_retryable_error(Error) of
        true ->
            try
                Result = FallbackFun(),
                {ok, Result}
            catch
                _:_ ->
                    {ok, DefaultValue}
            end;
        false ->
            {error, Error}
    end.

%%%===================================================================
%%% 连接和健康检查API
%%%===================================================================

%% @doc 简单ping检查
%% 功能：检查MongoDB连接是否可用，使用默认超时
%% Java等价：public boolean ping(Connection connection)
-spec ping(pid()) -> ok | {error, term()}.
ping(Connection) ->
    case ping_with_timeout(Connection, 3000) of
        {ok, _Latency} -> ok;
        {error, Reason} -> {error, Reason}
    end.

%% @doc 带超时的ping检查
-spec ping_with_timeout(pid(), integer()) -> {ok, integer()} | {error, term()}.
ping_with_timeout(Connection, TimeoutMs) ->
    StartTime = erlang:system_time(millisecond),
    try
        % 使用简单的连接检查，而不是MongoDB命令
        case is_process_alive(Connection) of
            true ->
                % 尝试获取连接信息来验证连接有效性
                case catch erlang:process_info(Connection, [status, message_queue_len]) of
                    [{status, Status}, {message_queue_len, _}] when Status =/= undefined ->
                        Latency = erlang:system_time(millisecond) - StartTime,
                        {ok, Latency};
                    _ ->
                        {error, connection_not_responsive}
                end;
            false ->
                {error, connection_dead}
        end
    catch
        E:R:Stack ->
            ?SLOG(error, #{
                msg => "ping_failed",
                error => E,
                reason => R,
                stack => Stack
            }),
            {error, {E, R}}
    end.

%% @doc 检查连接健康状态
-spec check_connection_health(pid()) -> health_status().
check_connection_health(Connection) ->
    StartTime = erlang:system_time(millisecond),

    % 执行ping测试
    PingResult = ping_with_timeout(Connection, 5000),

    % 获取服务器状态
    ServerStatus = get_server_status(Connection),

    % 计算总体健康状态
    Status = case {PingResult, ServerStatus} of
        {{ok, Latency}, {ok, _ServerInfo}} when Latency < 100 ->
            ok;
        {{ok, Latency}, {ok, _ServerInfo}} when Latency < 1000 ->
            warning;
        {{ok, Latency}, {ok, _ServerInfo}} when Latency < 5000 ->
            error;
        {{ok, _}, {ok, _}} ->
            critical;
        _ ->
            critical
    end,

    #{
        status => Status,
        latency => case PingResult of
            {ok, L} -> L;
            _ -> undefined
        end,
        last_error => case PingResult of
            {error, E} -> E;
            _ -> undefined
        end,
        server_info => case ServerStatus of
            {ok, Info} -> Info;
            _ -> #{}
        end,
        connection_pool_status => get_connection_pool_status(Connection),
        check_time => erlang:system_time(millisecond) - StartTime
    }.

%% @doc 获取服务器状态
-spec get_server_status(pid()) -> {ok, map()} | {error, term()}.
get_server_status(Connection) ->
    try
        case mc_worker_api:command(Connection, #{<<"serverStatus">> => 1}) of
            {true, Result} ->
                {ok, Result};
            {false, Error} ->
                {error, Error};
            Other ->
                {error, {unexpected_response, Other}}
        end
    catch
        E:R ->
            {error, {E, R}}
    end.

%% @doc 获取副本集状态
-spec get_replica_set_status(pid()) -> {ok, map()} | {error, term()}.
get_replica_set_status(Connection) ->
    try
        case mc_worker_api:command(Connection, #{<<"replSetGetStatus">> => 1}) of
            {true, Result} ->
                {ok, Result};
            {false, Error} ->
                {error, Error};
            Other ->
                {error, {unexpected_response, Other}}
        end
    catch
        E:R ->
            {error, {E, R}}
    end.

%% @doc 测试写关注设置
-spec test_write_concern(pid(), map()) -> {ok, map()} | {error, term()}.
test_write_concern(Connection, WriteConcern) ->
    TestCollection = <<"__test_write_concern__">>,
    TestDoc = #{
        <<"_id">> => <<"test_", (integer_to_binary(erlang:system_time()))/binary>>,
        <<"test">> => true,
        <<"timestamp">> => erlang:system_time(millisecond)
    },

    try
        % 插入测试文档
        case mc_worker_api:insert(Connection, TestCollection, [TestDoc], WriteConcern) of
            {true, Result} ->
                % 清理测试文档
                mc_worker_api:delete_one(Connection, TestCollection, #{<<"_id">> => maps:get(<<"_id">>, TestDoc)}),
                {ok, Result};
            {false, Error} ->
                {error, Error};
            Other ->
                {error, {unexpected_response, Other}}
        end
    catch
        E:R ->
            {error, {E, R}}
    end.

%% @doc 验证连接有效性
-spec validate_connection(pid()) -> {ok, map()} | {error, term()}.
validate_connection(Connection) ->
    try
        % 执行多项检查
        Checks = [
            {ping, fun() -> ping_with_timeout(Connection, 3000) end},
            {server_status, fun() -> get_server_status(Connection) end},
            {list_databases, fun() -> mc_worker_api:command(Connection, #{<<"listDatabases">> => 1}) end}
        ],

        Results = lists:map(
            fun({Name, CheckFun}) ->
                try
                    case CheckFun() of
                        {ok, _} -> {Name, ok};
                        {true, _} -> {Name, ok};
                        {false, Error} -> {Name, {error, Error}};
                        {error, Error} -> {Name, {error, Error}};
                        Other -> {Name, {error, {unexpected_response, Other}}}
                    end
                catch
                    CheckE:CheckR ->
                        {Name, {error, {CheckE, CheckR}}}
                end
            end,
            Checks
        ),

        % 分析结果
        Errors = [E || {_, {error, E}} <- Results],
        case Errors of
            [] ->
                {ok, #{status => healthy, checks => Results}};
            _ ->
                {error, #{status => unhealthy, checks => Results, errors => Errors}}
        end
    catch
        E:R ->
            {error, {E, R}}
    end.

%%%===================================================================
%%% 高级查询和聚合API
%%%===================================================================

%% @doc 带游标的查找操作，支持大结果集
-spec find_with_cursor(pid(), binary(), map(), query_options()) -> {ok, pid()} | {error, term()}.
find_with_cursor(Connection, Collection, Selector, Options) ->
    find_with_cursor(Connection, Collection, Selector, #{}, Options).

-spec find_with_cursor(pid(), binary(), map(), map(), query_options()) -> {ok, pid()} | {error, term()}.
find_with_cursor(Connection, Collection, Selector, Projector, Options) ->
    DefaultOptions = #{
        batch_size => 1000,
        skip => 0,
        limit => 0,
        sort => #{},
        max_time_ms => 30000,
        hint => undefined,
        read_preference => #{<<"mode">> => <<"primary">>}
    },
    MergedOptions = maps:merge(DefaultOptions, Options),

    try
        Query = #{
            <<"find">> => Collection,
            <<"filter">> => Selector,
            <<"projection">> => Projector,
            <<"batchSize">> => maps:get(batch_size, MergedOptions),
            <<"skip">> => maps:get(skip, MergedOptions),
            <<"maxTimeMS">> => maps:get(max_time_ms, MergedOptions)
        },

        % 添加可选参数
        QueryWithOptions = add_optional_query_params(Query, MergedOptions),

        case mc_worker_api:command(Connection, QueryWithOptions) of
            {true, #{<<"cursor">> := CursorInfo}} ->
                {ok, CursorInfo};
            {false, Error} ->
                {error, Error};
            Other ->
                {error, {unexpected_response, Other}}
        end
    catch
        E:R ->
            {error, {E, R}}
    end.

%% @doc 安全的查找单个文档
-spec find_one_safe(pid(), binary(), map()) -> {ok, map() | undefined} | {error, term()}.
find_one_safe(Connection, Collection, Selector) ->
    find_one_safe(Connection, Collection, Selector, #{}).

-spec find_one_safe(pid(), binary(), map(), query_options()) -> {ok, map() | undefined} | {error, term()}.
find_one_safe(Connection, Collection, Selector, Options) ->
    DefaultOptions = #{
        projection => #{},
        skip => 0,
        max_time_ms => 10000,
        read_preference => #{<<"mode">> => <<"primary">>}
    },
    MergedOptions = maps:merge(DefaultOptions, Options),

    try
        Projector = maps:get(projection, MergedOptions),
        Skip = maps:get(skip, MergedOptions),
        ReadPref = maps:get(read_preference, MergedOptions),

        case mc_worker_api:find_one(Connection, Collection, Selector, #{
            projector => Projector,
            skip => Skip,
            readopts => ReadPref
        }) of
            undefined ->
                {ok, undefined};
            Document when is_map(Document) ->
                {ok, Document};
            Other ->
                {error, {unexpected_response, Other}}
        end
    catch
        E:R ->
            {error, {E, R}}
    end.

%% @doc 安全的查找多个文档（兼容会话模块的调用）
-spec find_safe(pid(), binary(), map(), map(), integer(), integer()) -> {ok, [map()]} | {error, term()}.
find_safe(Connection, Collection, Filter, Projection, Skip, Limit) ->
    try
        % 使用MongoDB原生API进行查找
        Command = #{
            <<"find">> => Collection,
            <<"filter">> => Filter,
            <<"projection">> => Projection,
            <<"skip">> => Skip,
            <<"limit">> => Limit,
            <<"maxTimeMS">> => 10000
        },

        case command(Connection, Command) of
            {ok, #{<<"cursor">> := #{<<"firstBatch">> := Results}}} ->
                {ok, Results};
            {ok, #{<<"cursor">> := #{<<"firstBatch">> := Results, <<"id">> := CursorId}}} when CursorId =/= 0 ->
                % 如果有更多结果，获取剩余的
                try
                    MoreResults = get_more_results(Connection, Collection, CursorId),
                    {ok, Results ++ MoreResults}
                catch
                    _:_ ->
                        % 如果获取更多结果失败，至少返回第一批
                        {ok, Results}
                end;
            {ok, Result} ->
                % 处理其他格式的响应
                case maps:get(<<"cursor">>, Result, undefined) of
                    undefined ->
                        % 没有游标，可能是直接的结果
                        {ok, []};
                    Cursor ->
                        Results = maps:get(<<"firstBatch">>, Cursor, []),
                        {ok, Results}
                end;
            {error, Reason} ->
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "find_safe_exception",
                error => E,
                reason => R,
                stack => S,
                collection => Collection,
                filter => Filter
            }),
            {error, {E, R}}
    end.

%% @doc 获取游标的更多结果
get_more_results(Connection, Collection, CursorId) ->
    get_more_results(Connection, Collection, CursorId, []).

get_more_results(Connection, Collection, CursorId, Acc) when CursorId =:= 0 ->
    Acc;
get_more_results(Connection, Collection, CursorId, Acc) ->
    Command = #{
        <<"getMore">> => CursorId,
        <<"collection">> => Collection,
        <<"batchSize">> => 1000
    },

    case command(Connection, Command) of
        {ok, #{<<"cursor">> := #{<<"nextBatch">> := NextBatch, <<"id">> := NextCursorId}}} ->
            get_more_results(Connection, Collection, NextCursorId, Acc ++ NextBatch);
        {ok, #{<<"cursor">> := #{<<"nextBatch">> := NextBatch}}} ->
            Acc ++ NextBatch;
        _ ->
            Acc
    end.

%% @doc 聚合管道操作
-spec aggregate_pipeline(pid(), binary(), [map()]) -> {ok, [map()]} | {error, term()}.
aggregate_pipeline(Connection, Collection, Pipeline) ->
    aggregate_pipeline(Connection, Collection, Pipeline, #{}).

-spec aggregate_pipeline(pid(), binary(), [map()], map()) -> {ok, [map()]} | {error, term()}.
aggregate_pipeline(Connection, Collection, Pipeline, Options) ->
    DefaultOptions = #{
        batch_size => 1000,
        allow_disk_use => false,
        max_time_ms => 30000,
        read_preference => #{<<"mode">> => <<"primary">>},
        hint => undefined
    },
    MergedOptions = maps:merge(DefaultOptions, Options),

    try
        Command = #{
            <<"aggregate">> => Collection,
            <<"pipeline">> => Pipeline,
            <<"cursor">> => #{<<"batchSize">> => maps:get(batch_size, MergedOptions)},
            <<"allowDiskUse">> => maps:get(allow_disk_use, MergedOptions),
            <<"maxTimeMS">> => maps:get(max_time_ms, MergedOptions)
        },

        % 添加可选参数
        CommandWithOptions = add_optional_aggregate_params(Command, MergedOptions),

        case mc_worker_api:command(Connection, CommandWithOptions) of
            {true, #{<<"cursor">> := #{<<"firstBatch">> := Results}}} ->
                {ok, Results};
            {true, #{<<"cursor">> := CursorInfo}} ->
                % 处理需要多次获取的情况
                fetch_all_from_cursor(Connection, CursorInfo);
            {false, Error} ->
                {error, Error};
            Other ->
                {error, {unexpected_response, Other}}
        end
    catch
        E:R ->
            {error, {E, R}}
    end.

%% @doc 计数文档
-spec count_documents(pid(), binary(), map()) -> {ok, integer()} | {error, term()}.
count_documents(Connection, Collection, Filter) ->
    count_documents(Connection, Collection, Filter, #{}).

-spec count_documents(pid(), binary(), map(), map()) -> {ok, integer()} | {error, term()}.
count_documents(Connection, Collection, Filter, Options) ->
    DefaultOptions = #{
        limit => 0,
        skip => 0,
        max_time_ms => 10000,
        hint => undefined
    },
    MergedOptions = maps:merge(DefaultOptions, Options),

    try
        % 使用聚合管道进行计数（推荐方式）
        Pipeline = [
            #{<<"$match">> => Filter},
            #{<<"$count">> => <<"total">>}
        ],

        % 添加可选的skip和limit
        PipelineWithOptions = add_count_pipeline_options(Pipeline, MergedOptions),

        case aggregate_pipeline(Connection, Collection, PipelineWithOptions, MergedOptions) of
            {ok, [#{<<"total">> := Count}]} ->
                {ok, Count};
            {ok, []} ->
                {ok, 0};
            {error, Error} ->
                {error, Error};
            Other ->
                {error, {unexpected_response, Other}}
        end
    catch
        E:R ->
            {error, {E, R}}
    end.

%% @doc 获取字段的不同值
-spec distinct_values(pid(), binary(), binary()) -> {ok, [term()]} | {error, term()}.
distinct_values(Connection, Collection, Field) ->
    distinct_values(Connection, Collection, Field, #{}).

-spec distinct_values(pid(), binary(), binary(), map()) -> {ok, [term()]} | {error, term()}.
distinct_values(Connection, Collection, Field, Options) ->
    DefaultOptions = #{
        query => #{},
        max_time_ms => 10000
    },
    MergedOptions = maps:merge(DefaultOptions, Options),

    try
        Command = #{
            <<"distinct">> => Collection,
            <<"key">> => Field,
            <<"query">> => maps:get(query, MergedOptions),
            <<"maxTimeMS">> => maps:get(max_time_ms, MergedOptions)
        },

        case mc_worker_api:command(Connection, Command) of
            {true, #{<<"values">> := Values}} ->
                {ok, Values};
            {false, Error} ->
                {error, Error};
            Other ->
                {error, {unexpected_response, Other}}
        end
    catch
        E:R ->
            {error, {E, R}}
    end.

%% @doc 解释查询执行计划
-spec explain_query(pid(), binary(), map()) -> {ok, map()} | {error, term()}.
explain_query(Connection, Collection, Query) ->
    try
        Command = #{
            <<"explain">> => #{
                <<"find">> => Collection,
                <<"filter">> => Query
            },
            <<"verbosity">> => <<"executionStats">>
        },

        case mc_worker_api:command(Connection, Command) of
            {true, Result} ->
                {ok, Result};
            {false, Error} ->
                {error, Error};
            Other ->
                {error, {unexpected_response, Other}}
        end
    catch
        E:R ->
            {error, {E, R}}
    end.

%% @doc 文本搜索
-spec text_search(pid(), binary(), binary(), map()) -> {ok, [map()]} | {error, term()}.
text_search(Connection, Collection, SearchText, Options) ->
    DefaultOptions = #{
        language => undefined,
        case_sensitive => false,
        diacritic_sensitive => false,
        limit => 100
    },
    MergedOptions = maps:merge(DefaultOptions, Options),

    try
        TextQuery = #{
            <<"$text">> => #{
                <<"$search">> => SearchText,
                <<"$caseSensitive">> => maps:get(case_sensitive, MergedOptions),
                <<"$diacriticSensitive">> => maps:get(diacritic_sensitive, MergedOptions)
            }
        },

        % 添加语言选项
        TextQueryWithLang = case maps:get(language, MergedOptions) of
            undefined -> TextQuery;
            Lang -> maps:put(<<"$language">>, Lang, maps:get(<<"$text">>, TextQuery))
        end,

        QueryOptions = #{
            limit => maps:get(limit, MergedOptions),
            sort => #{<<"score">> => #{<<"$meta">> => <<"textScore">>}},
            projection => #{<<"score">> => #{<<"$meta">> => <<"textScore">>}}
        },

        find_with_cursor(Connection, Collection, TextQueryWithLang, #{}, QueryOptions)
    catch
        E:R ->
            {error, {E, R}}
    end.

%%%===================================================================
%%% 索引管理API
%%%===================================================================

%% @doc 确保索引存在
-spec ensure_index(pid(), binary(), map()) -> {ok, map()} | {error, term()}.
ensure_index(Connection, Collection, IndexSpec) ->
    ensure_index(Connection, Collection, IndexSpec, #{}).

-spec ensure_index(pid(), binary(), map(), map()) -> {ok, map()} | {error, term()}.
ensure_index(Connection, Collection, IndexSpec, Options) ->
    DefaultOptions = #{
        name => generate_index_name(IndexSpec),
        background => true,
        unique => false,
        sparse => false,
        partial_filter_expression => undefined,
        expire_after_seconds => undefined
    },
    MergedOptions = maps:merge(DefaultOptions, Options),

    try
        IndexName = maps:get(name, MergedOptions),

        % 首先检查索引是否已存在
        case check_index_exists(Connection, Collection, IndexName) of
            {ok, true} ->
                ?SLOG(info, #{msg => "index_already_exists", collection => Collection, name => IndexName}),
                {ok, #{<<"ok">> => 1, <<"note">> => <<"index already exists">>}};
            {ok, false} ->
                % 创建索引
                IndexDoc = build_index_document(IndexSpec, MergedOptions),
                Command = #{
                    <<"createIndexes">> => Collection,
                    <<"indexes">> => [IndexDoc]
                },

                case mc_worker_api:command(Connection, Command) of
                    {true, Result} ->
                        ?SLOG(info, #{msg => "index_created", collection => Collection, name => IndexName}),
                        {ok, Result};
                    {false, Error} ->
                        {error, Error};
                    Other ->
                        {error, {unexpected_response, Other}}
                end;
            {error, Reason} ->
                {error, Reason}
        end
    catch
        E:R ->
            {error, {E, R}}
    end.

%% @doc 列出集合的所有索引
-spec list_indexes(pid(), binary()) -> {ok, [map()]} | {error, term()}.
list_indexes(Connection, Collection) ->
    try
        Command = #{
            <<"listIndexes">> => Collection
        },

        case mc_worker_api:command(Connection, Command) of
            {true, #{<<"cursor">> := CursorInfo}} ->
                fetch_all_from_cursor(Connection, CursorInfo);
            {false, Error} ->
                {error, Error};
            Other ->
                {error, {unexpected_response, Other}}
        end
    catch
        E:R ->
            {error, {E, R}}
    end.

%% @doc 删除索引
-spec drop_index(pid(), binary(), binary()) -> {ok, map()} | {error, term()}.
drop_index(Connection, Collection, IndexName) ->
    try
        % 首先检查索引是否存在
        case check_index_exists(Connection, Collection, IndexName) of
            {ok, true} ->
                Command = #{
                    <<"dropIndexes">> => Collection,
                    <<"index">> => IndexName
                },

                case mc_worker_api:command(Connection, Command) of
                    {true, Result} ->
                        ?SLOG(info, #{msg => "index_dropped", collection => Collection, name => IndexName}),
                        {ok, Result};
                    {false, Error} ->
                        {error, Error};
                    Other ->
                        {error, {unexpected_response, Other}}
                end;
            {ok, false} ->
                ?SLOG(warning, #{msg => "index_not_found", collection => Collection, name => IndexName}),
                {ok, #{<<"ok">> => 1, <<"note">> => <<"index not found">>}};
            {error, Reason} ->
                {error, Reason}
        end
    catch
        E:R ->
            {error, {E, R}}
    end.

%% @doc 获取索引统计信息
-spec get_index_stats(pid(), binary()) -> {ok, [map()]} | {error, term()}.
get_index_stats(Connection, Collection) ->
    try
        Pipeline = [
            #{<<"$indexStats">> => #{}}
        ],

        aggregate_pipeline(Connection, Collection, Pipeline)
    catch
        E:R ->
            {error, {E, R}}
    end.

%% @doc 优化索引（重建索引）
-spec optimize_indexes(pid(), binary()) -> {ok, map()} | {error, term()}.
optimize_indexes(Connection, Collection) ->
    try
        Command = #{
            <<"reIndex">> => Collection
        },

        case mc_worker_api:command(Connection, Command) of
            {true, Result} ->
                ?SLOG(info, #{msg => "indexes_optimized", collection => Collection}),
                {ok, Result};
            {false, Error} ->
                {error, Error};
            Other ->
                {error, {unexpected_response, Other}}
        end
    catch
        E:R ->
            {error, {E, R}}
    end.

%% @doc 创建复合索引
-spec create_compound_index(pid(), binary(), [map()]) -> {ok, map()} | {error, term()}.
create_compound_index(Connection, Collection, Fields) ->
    try
        IndexSpec = lists:foldl(
            fun(#{field := Field, direction := Direction}, Acc) ->
                Acc#{Field => Direction}
            end,
            #{},
            Fields
        ),
        ensure_index(Connection, Collection, IndexSpec, #{background => true})
    catch
        E:R ->
            {error, {E, R}}
    end.

%% @doc 创建文本索引
-spec create_text_index(pid(), binary(), [binary()]) -> {ok, map()} | {error, term()}.
create_text_index(Connection, Collection, Fields) ->
    try
        IndexSpec = lists:foldl(
            fun(Field, Acc) ->
                Acc#{Field => <<"text">>}
            end,
            #{},
            Fields
        ),
        ensure_index(Connection, Collection, IndexSpec, #{background => true})
    catch
        E:R ->
            {error, {E, R}}
    end.

%% @doc 创建地理空间索引
-spec create_geospatial_index(pid(), binary(), binary()) -> {ok, map()} | {error, term()}.
create_geospatial_index(Connection, Collection, Field) ->
    try
        IndexSpec = #{Field => <<"2dsphere">>},
        ensure_index(Connection, Collection, IndexSpec, #{background => true})
    catch
        E:R ->
            {error, {E, R}}
    end.

%%%===================================================================
%%% 辅助函数实现
%%%===================================================================

%% @doc 将文档列表分割成批次
%% 这个函数将大的文档列表分割成小的批次，用于批量处理
%%
%% 功能说明：
%% 1. 避免一次性处理过多文档导致内存溢出
%% 2. 提高MongoDB批量操作的效率
%% 3. 支持并行处理多个批次
%%
%% 参数说明：
%% - List: 要分割的元素列表（通常是文档列表）
%% - BatchSize: 每个批次的大小
%%
%% 返回值：
%% - [[term()]]: 分割后的批次列表，每个批次包含最多BatchSize个元素
%%
%% 算法说明：
%% 使用尾递归优化，避免栈溢出
%%
%% Java等价实现：
%% public static <T> List<List<T>> splitIntoBatches(List<T> list, int batchSize) {
%%     List<List<T>> batches = new ArrayList<>();
%%     for (int i = 0; i < list.size(); i += batchSize) {
%%         batches.add(list.subList(i, Math.min(i + batchSize, list.size())));
%%     }
%%     return batches;
%% }
-spec split_into_batches([term()], integer()) -> [[term()]].
split_into_batches([], _BatchSize) ->
    %% 空列表直接返回空结果
    [];
split_into_batches(List, BatchSize) when BatchSize > 0 ->
    %% 调用尾递归版本，初始累加器为空列表
    split_into_batches(List, BatchSize, []).

%% @doc 尾递归版本的批次分割函数
%% 这是split_into_batches/2的内部实现，使用尾递归优化性能
%%
%% 参数说明：
%% - List: 剩余要处理的元素列表
%% - BatchSize: 批次大小
%% - Acc: 累加器，存储已经分割好的批次
%%
%% 实现细节：
%% 1. 使用lists:split/2将列表分割成当前批次和剩余部分
%% 2. 将当前批次添加到累加器
%% 3. 递归处理剩余部分
%% 4. 最后反转累加器得到正确顺序的结果
split_into_batches([], _BatchSize, Acc) ->
    %% 递归终止条件：没有更多元素需要处理
    %% 反转累加器得到正确的批次顺序
    lists:reverse(Acc);
split_into_batches(List, BatchSize, Acc) ->
    %% 分割当前批次：取min(BatchSize, length(List))个元素
    %% lists:split/2返回{前N个元素, 剩余元素}的元组
    {Batch, Rest} = lists:split(min(BatchSize, length(List)), List),
    %% 递归处理剩余元素，将当前批次添加到累加器头部
    split_into_batches(Rest, BatchSize, [Batch | Acc]).

%% @doc 合并批量操作结果
%% 这个函数将多个批次的操作结果合并成一个总的结果
%%
%% 功能说明：
%% 1. 将多个批量操作的结果统计信息合并
%% 2. 累加各种计数（插入、更新、删除等）
%% 3. 合并所有批次的错误信息
%%
%% 参数说明：
%% - Results: 批量操作结果列表，每个结果包含计数和错误信息
%%
%% 返回值：
%% - bulk_result(): 合并后的总结果，包含所有批次的累计统计
%%
%% 实现原理：
%% 使用lists:foldl/3进行累加操作，类似于Java的Stream.reduce()
%%
%% Java等价实现：
%% public BulkWriteResult mergeBulkResults(List<BulkWriteResult> results) {
%%     return results.stream().reduce(
%%         BulkWriteResult.empty(),
%%         (acc, result) -> BulkWriteResult.builder()
%%             .insertedCount(acc.getInsertedCount() + result.getInsertedCount())
%%             .modifiedCount(acc.getModifiedCount() + result.getModifiedCount())
%%             .deletedCount(acc.getDeletedCount() + result.getDeletedCount())
%%             .upsertedCount(acc.getUpsertedCount() + result.getUpsertedCount())
%%             .matchedCount(acc.getMatchedCount() + result.getMatchedCount())
%%             .errors(Stream.concat(acc.getErrors().stream(), result.getErrors().stream()).collect(toList()))
%%             .build()
%%     );
%% }
-spec merge_bulk_results([bulk_result()]) -> bulk_result().
merge_bulk_results(Results) ->
    %% 使用lists:foldl/3进行累加操作
    %% foldl(Function, InitialAccumulator, List)
    %% 对列表中的每个元素调用Function，累加结果
    lists:foldl(
        fun(Result, Acc) ->
            %% 对每个批次结果，累加各种计数
            #{
                % 累加插入成功的文档数量
                inserted_count => maps:get(inserted_count, Acc, 0) + maps:get(inserted_count, Result, 0),
                % 累加修改的文档数量
                modified_count => maps:get(modified_count, Acc, 0) + maps:get(modified_count, Result, 0),
                % 累加删除的文档数量
                deleted_count => maps:get(deleted_count, Acc, 0) + maps:get(deleted_count, Result, 0),
                % 累加更新插入的文档数量
                upserted_count => maps:get(upserted_count, Acc, 0) + maps:get(upserted_count, Result, 0),
                % 累加匹配的文档数量
                matched_count => maps:get(matched_count, Acc, 0) + maps:get(matched_count, Result, 0),
                % 合并错误列表，使用++操作符连接两个列表
                errors => maps:get(errors, Acc, []) ++ maps:get(errors, Result, [])
            }
        end,
        %% 初始累加器：所有计数为0，错误列表为空
        #{inserted_count => 0, modified_count => 0, deleted_count => 0,
          upserted_count => 0, matched_count => 0, errors => []},
        %% 要处理的结果列表
        Results
    ).

%% @doc 生成索引名称
-spec generate_index_name(map()) -> binary().
generate_index_name(IndexSpec) ->
    Fields = maps:fold(
        fun(Key, Value, Acc) ->
            Direction = case Value of
                1 -> <<"_1">>;
                -1 -> <<"_-1">>;
                <<"text">> -> <<"_text">>;
                <<"2d">> -> <<"_2d">>;
                <<"2dsphere">> -> <<"_2dsphere">>;
                _ -> <<"_1">>
            end,
            [<<Key/binary, Direction/binary>> | Acc]
        end,
        [],
        IndexSpec
    ),
    iolist_to_binary(lists:join(<<"_">>, lists:reverse(Fields))).

%% @doc 验证文档列表
-spec validate_documents([map()]) -> ok | {error, term()}.
validate_documents([]) ->
    {error, empty_document_list};
validate_documents(Documents) when is_list(Documents) ->
    case lists:all(fun is_map/1, Documents) of
        true -> ok;
        false -> {error, invalid_document_format}
    end;
validate_documents(_) ->
    {error, invalid_document_list}.

%% @doc 验证更新插入操作
-spec validate_upsert_operations([map()]) -> ok | {error, term()}.
validate_upsert_operations([]) ->
    {error, empty_operation_list};
validate_upsert_operations(Operations) when is_list(Operations) ->
    case lists:all(
        fun(Op) ->
            is_map(Op) andalso
            maps:is_key(filter, Op) andalso
            maps:is_key(update, Op)
        end,
        Operations
    ) of
        true -> ok;
        false -> {error, invalid_upsert_operation_format}
    end;
validate_upsert_operations(_) ->
    {error, invalid_operation_list}.

%% @doc 验证选择器列表
-spec validate_selectors([map()]) -> ok | {error, term()}.
validate_selectors([]) ->
    {error, empty_selector_list};
validate_selectors(Selectors) when is_list(Selectors) ->
    case lists:all(fun is_map/1, Selectors) of
        true -> ok;
        false -> {error, invalid_selector_format}
    end;
validate_selectors(_) ->
    {error, invalid_selector_list}.

%% @doc 验证替换操作
-spec validate_replace_operations([map()]) -> ok | {error, term()}.
validate_replace_operations([]) ->
    {error, empty_operation_list};
validate_replace_operations(Operations) when is_list(Operations) ->
    case lists:all(
        fun(Op) ->
            is_map(Op) andalso
            maps:is_key(filter, Op) andalso
            maps:is_key(replacement, Op)
        end,
        Operations
    ) of
        true -> ok;
        false -> {error, invalid_replace_operation_format}
    end;
validate_replace_operations(_) ->
    {error, invalid_operation_list}.

%% @doc 按操作类型分组混合操作
-spec group_mixed_operations([bulk_operation()]) -> {[map()], [map()], [map()], [map()]}.
group_mixed_operations(Operations) ->
    lists:foldl(
        fun(Op, {Inserts, Updates, Deletes, Replaces}) ->
            case Op of
                {insert, Doc} ->
                    {[Doc | Inserts], Updates, Deletes, Replaces};
                {update, Filter, Update} ->
                    {Inserts, [#{filter => Filter, update => Update} | Updates], Deletes, Replaces};
                {upsert, Filter, Update} ->
                    {Inserts, [#{filter => Filter, update => Update} | Updates], Deletes, Replaces};
                {replace, Filter, Replacement} ->
                    {Inserts, Updates, Deletes, [#{filter => Filter, replacement => Replacement} | Replaces]};
                {delete, Filter} ->
                    {Inserts, Updates, [Filter | Deletes], Replaces}
            end
        end,
        {[], [], [], []},
        Operations
    ).

%% @doc 执行插入批次
-spec execute_insert_batch(pid(), binary(), [map()], map()) -> bulk_result().
execute_insert_batch(Connection, Collection, Documents, Options) ->
    WriteConcern = maps:get(write_concern, Options),
    try
        case mc_worker_api:insert(Connection, Collection, Documents, WriteConcern) of
            {true, Result} when is_map(Result) ->
                % 从复杂的结果中提取插入计数
                Count = maps:get(<<"n">>, Result, length(Documents)),
                #{
                    inserted_count => Count,
                    modified_count => 0,
                    deleted_count => 0,
                    upserted_count => 0,
                    matched_count => 0,
                    errors => []
                };
            {{true, Result}, _InsertedDocs} when is_map(Result) ->
                % 处理 {{true, Result}, [Documents]} 格式
                Count = maps:get(<<"n">>, Result, length(Documents)),
                #{
                    inserted_count => Count,
                    modified_count => 0,
                    deleted_count => 0,
                    upserted_count => 0,
                    matched_count => 0,
                    errors => []
                };
            {false, Error} ->
                #{
                    inserted_count => 0,
                    modified_count => 0,
                    deleted_count => 0,
                    upserted_count => 0,
                    matched_count => 0,
                    errors => [Error]
                };
            Other ->
                % 处理意外的返回格式 - 降级为 debug 级别
                ?SLOG(debug, #{
                    msg => "unexpected_insert_result_format",
                    result => Other,
                    collection => Collection
                }),
                #{
                    inserted_count => length(Documents),  % 假设全部插入成功
                    modified_count => 0,
                    deleted_count => 0,
                    upserted_count => 0,
                    matched_count => 0,
                    errors => []
                }
        end
    catch
        E:R ->
            #{
                inserted_count => 0,
                modified_count => 0,
                deleted_count => 0,
                upserted_count => 0,
                matched_count => 0,
                errors => [#{error => E, reason => R}]
            }
    end.

%% @doc 执行更新插入批次
-spec execute_upsert_batch(pid(), binary(), [map()], map()) -> bulk_result().
execute_upsert_batch(Connection, Collection, Operations, Options) ->
    WriteConcern = maps:get(write_concern, Options),
    try
        Updates = lists:map(
            fun(#{filter := Filter, update := Update}) ->
                #{
                    <<"q">> => Filter,
                    <<"u">> => Update,
                    <<"upsert">> => true,
                    <<"multi">> => false
                }
            end,
            Operations
        ),

        Command = #{
            <<"update">> => Collection,
            <<"updates">> => Updates,
            <<"writeConcern">> => WriteConcern
        },

        case mc_worker_api:command(Connection, Command) of
            {true, #{<<"n">> := Modified, <<"upserted">> := Upserted}} ->
                #{
                    inserted_count => 0,
                    modified_count => Modified,
                    deleted_count => 0,
                    upserted_count => length(Upserted),
                    matched_count => Modified,
                    errors => []
                };
            {true, #{<<"n">> := Modified}} ->
                #{
                    inserted_count => 0,
                    modified_count => Modified,
                    deleted_count => 0,
                    upserted_count => 0,
                    matched_count => Modified,
                    errors => []
                };
            {false, Error} ->
                #{
                    inserted_count => 0,
                    modified_count => 0,
                    deleted_count => 0,
                    upserted_count => 0,
                    matched_count => 0,
                    errors => [Error]
                }
        end
    catch
        E:R ->
            #{
                inserted_count => 0,
                modified_count => 0,
                deleted_count => 0,
                upserted_count => 0,
                matched_count => 0,
                errors => [#{error => E, reason => R}]
            }
    end.

%% @doc 执行删除批次
-spec execute_delete_batch(pid(), binary(), [map()], map()) -> bulk_result().
execute_delete_batch(Connection, Collection, Selectors, Options) ->
    WriteConcern = maps:get(write_concern, Options),
    Limit = maps:get(limit, Options, 0),
    try
        Deletes = lists:map(
            fun(Selector) ->
                #{
                    <<"q">> => Selector,
                    <<"limit">> => Limit
                }
            end,
            Selectors
        ),

        Command = #{
            <<"delete">> => Collection,
            <<"deletes">> => Deletes,
            <<"writeConcern">> => WriteConcern
        },

        case mc_worker_api:command(Connection, Command) of
            {true, #{<<"n">> := Deleted}} ->
                #{
                    inserted_count => 0,
                    modified_count => 0,
                    deleted_count => Deleted,
                    upserted_count => 0,
                    matched_count => 0,
                    errors => []
                };
            {false, Error} ->
                #{
                    inserted_count => 0,
                    modified_count => 0,
                    deleted_count => 0,
                    upserted_count => 0,
                    matched_count => 0,
                    errors => [Error]
                }
        end
    catch
        E:R ->
            #{
                inserted_count => 0,
                modified_count => 0,
                deleted_count => 0,
                upserted_count => 0,
                matched_count => 0,
                errors => [#{error => E, reason => R}]
            }
    end.

%% @doc 执行替换批次
-spec execute_replace_batch(pid(), binary(), [map()], map()) -> bulk_result().
execute_replace_batch(Connection, Collection, Operations, Options) ->
    WriteConcern = maps:get(write_concern, Options),
    Upsert = maps:get(upsert, Options, false),
    try
        Replaces = lists:map(
            fun(#{filter := Filter, replacement := Replacement}) ->
                #{
                    <<"q">> => Filter,
                    <<"u">> => Replacement,
                    <<"upsert">> => Upsert,
                    <<"multi">> => false
                }
            end,
            Operations
        ),

        Command = #{
            <<"update">> => Collection,
            <<"updates">> => Replaces,
            <<"writeConcern">> => WriteConcern
        },

        case mc_worker_api:command(Connection, Command) of
            {true, #{<<"n">> := Matched, <<"nModified">> := Modified}} ->
                #{
                    inserted_count => 0,
                    modified_count => Modified,
                    deleted_count => 0,
                    upserted_count => 0,
                    matched_count => Matched,
                    errors => []
                };
            {true, #{<<"n">> := Matched}} ->
                #{
                    inserted_count => 0,
                    modified_count => Matched,
                    deleted_count => 0,
                    upserted_count => 0,
                    matched_count => Matched,
                    errors => []
                };
            {false, Error} ->
                #{
                    inserted_count => 0,
                    modified_count => 0,
                    deleted_count => 0,
                    upserted_count => 0,
                    matched_count => 0,
                    errors => [Error]
                }
        end
    catch
        E:R ->
            #{
                inserted_count => 0,
                modified_count => 0,
                deleted_count => 0,
                upserted_count => 0,
                matched_count => 0,
                errors => [#{error => E, reason => R}]
            }
    end.

%% @doc 检查索引是否存在
-spec check_index_exists(pid(), binary(), binary()) -> {ok, boolean()} | {error, term()}.
check_index_exists(Connection, Collection, IndexName) ->
    try
        case list_indexes(Connection, Collection) of
            {ok, Indexes} ->
                Exists = lists:any(
                    fun(Index) ->
                        maps:get(<<"name">>, Index, undefined) =:= IndexName
                    end,
                    Indexes
                ),
                {ok, Exists};
            {error, Error} ->
                {error, Error}
        end
    catch
        E:R ->
            {error, {E, R}}
    end.

%% @doc 构建索引文档
-spec build_index_document(map(), map()) -> map().
build_index_document(IndexSpec, Options) ->
    BaseDoc = #{
        <<"key">> => IndexSpec,
        <<"name">> => maps:get(name, Options),
        <<"background">> => maps:get(background, Options, true),
        <<"unique">> => maps:get(unique, Options, false),
        <<"sparse">> => maps:get(sparse, Options, false)
    },

    % 添加可选参数
    OptionalParams = [
        {partial_filter_expression, <<"partialFilterExpression">>},
        {expire_after_seconds, <<"expireAfterSeconds">>}
    ],

    lists:foldl(
        fun({Key, MongoKey}, Acc) ->
            case maps:get(Key, Options, undefined) of
                undefined -> Acc;
                Value -> Acc#{MongoKey => Value}
            end
        end,
        BaseDoc,
        OptionalParams
    ).

%% @doc 获取连接池状态
-spec get_connection_pool_status(pid()) -> map().
get_connection_pool_status(_Connection) ->
    % 这里可以添加实际的连接池状态检查逻辑
    #{
        status => unknown,
        active_connections => 0,
        idle_connections => 0,
        total_connections => 0
    }.

%% @doc 添加可选查询参数
-spec add_optional_query_params(map(), map()) -> map().
add_optional_query_params(Query, Options) ->
    OptionalParams = [
        {limit, <<"limit">>},
        {sort, <<"sort">>},
        {hint, <<"hint">>}
    ],

    lists:foldl(
        fun({Key, MongoKey}, Acc) ->
            case maps:get(Key, Options, undefined) of
                undefined -> Acc;
                0 when Key =:= limit -> Acc;  % 跳过limit=0
                Value when is_map(Value), map_size(Value) =:= 0, Key =:= sort -> Acc;  % 跳过空sort
                Value -> Acc#{MongoKey => Value}
            end
        end,
        Query,
        OptionalParams
    ).

%% @doc 添加可选聚合参数
-spec add_optional_aggregate_params(map(), map()) -> map().
add_optional_aggregate_params(Command, Options) ->
    OptionalParams = [
        {hint, <<"hint">>},
        {read_preference, <<"$readPreference">>}
    ],

    lists:foldl(
        fun({Key, MongoKey}, Acc) ->
            case maps:get(Key, Options, undefined) of
                undefined -> Acc;
                Value -> Acc#{MongoKey => Value}
            end
        end,
        Command,
        OptionalParams
    ).

%% @doc 添加计数管道选项
-spec add_count_pipeline_options([map()], map()) -> [map()].
add_count_pipeline_options(Pipeline, Options) ->
    Skip = maps:get(skip, Options, 0),
    Limit = maps:get(limit, Options, 0),

    % 在$match之后添加$skip和$limit
    PipelineWithSkip = case Skip of
        0 -> Pipeline;
        _ -> Pipeline ++ [#{<<"$skip">> => Skip}]
    end,

    case Limit of
        0 -> PipelineWithSkip;
        _ -> PipelineWithSkip ++ [#{<<"$limit">> => Limit}]
    end.

%% @doc 从游标获取所有结果
-spec fetch_all_from_cursor(pid(), map()) -> {ok, [map()]} | {error, term()}.
fetch_all_from_cursor(Connection, CursorInfo) ->
    CursorId = maps:get(<<"id">>, CursorInfo, 0),
    FirstBatch = maps:get(<<"firstBatch">>, CursorInfo, []),
    Namespace = maps:get(<<"ns">>, CursorInfo, <<"unknown">>),

    case CursorId of
        0 ->
            % 没有更多数据
            {ok, FirstBatch};
        _ ->
            % 需要获取更多数据
            fetch_remaining_from_cursor(Connection, CursorId, Namespace, FirstBatch)
    end.

%% @doc 从游标获取剩余数据
-spec fetch_remaining_from_cursor(pid(), integer(), binary(), [map()]) -> {ok, [map()]} | {error, term()}.
fetch_remaining_from_cursor(Connection, CursorId, Namespace, Acc) ->
    try
        Command = #{
            <<"getMore">> => CursorId,
            <<"collection">> => Namespace,
            <<"batchSize">> => 1000
        },

        case mc_worker_api:command(Connection, Command) of
            {true, #{<<"cursor">> := #{<<"nextBatch">> := NextBatch, <<"id">> := NextCursorId}}} ->
                NewAcc = Acc ++ NextBatch,
                case NextCursorId of
                    0 ->
                        {ok, NewAcc};
                    _ ->
                        fetch_remaining_from_cursor(Connection, NextCursorId, Namespace, NewAcc)
                end;
            {false, Error} ->
                {error, Error};
            Other ->
                {error, {unexpected_response, Other}}
        end
    catch
        E:R ->
            {error, {E, R}}
    end.

%%%===================================================================
%%% 部署模式支持API实现
%%%===================================================================

%% @doc 检测MongoDB部署模式
-spec detect_deployment_mode(pid()) -> {ok, deployment_mode()} | {error, term()}.
detect_deployment_mode(Connection) ->
    try
        % 首先尝试获取副本集状态
        case get_replica_set_status(Connection) of
            {ok, #{<<"set">> := ReplicaSetName}} ->
                {ok, {replica_set, ReplicaSetName}};
            {error, _} ->
                % 不是副本集，检查是否是分片集群
                case check_sharded_cluster(Connection) of
                    {ok, ShardInfo} ->
                        {ok, {sharded_cluster, ShardInfo}};
                    {error, _} ->
                        % 默认为单节点模式
                        {ok, single}
                end
        end
    catch
        E:R ->
            {error, {E, R}}
    end.

%% @doc 获取部署模式特定的配置
-spec get_deployment_config(deployment_mode(), map()) -> map().
get_deployment_config(single, BaseConfig) ->
    % 从EMQX配置中读取基础配置
    EmqxConfig = get_emqx_mongodb_config(),

    BaseConfig#{
        read_preference => #{<<"mode">> => <<"primary">>},
        write_concern => maps:get(write_concern, EmqxConfig, #{<<"w">> => 1}),
        connection_pool_size => maps:get(pool_size, EmqxConfig, 4),
        max_overflow => maps:get(max_overflow, EmqxConfig, 8),
        server_selection_timeout_ms => maps:get(server_selection_timeout_ms, EmqxConfig, 5000)
    };

get_deployment_config({replica_set, _ReplicaSetName}, BaseConfig) ->
    BaseConfig#{
        read_preference => #{<<"mode">> => <<"primaryPreferred">>},
        write_concern => #{<<"w">> => <<"majority">>, <<"j">> => true},
        connection_pool_size => 8,
        max_overflow => 16,
        server_selection_timeout_ms => 10000,
        heartbeat_frequency_ms => 10000,
        secondary_acceptable_latency_ms => 15
    };

get_deployment_config({sharded_cluster, _ShardInfo}, BaseConfig) ->
    BaseConfig#{
        read_preference => #{<<"mode">> => <<"nearest">>},
        write_concern => #{<<"w">> => <<"majority">>, <<"j">> => true},
        connection_pool_size => 12,
        max_overflow => 24,
        server_selection_timeout_ms => 15000,
        local_threshold_ms => 15
    }.

%% @doc 针对部署模式进行优化
-spec optimize_for_deployment(deployment_mode(), map()) -> map().
optimize_for_deployment(single, Options) ->
    % 单节点优化：简化配置，减少开销
    Options#{
        batch_size => maps:get(batch_size, Options, 1000),
        ordered => true,  % 单节点可以保证顺序
        bypass_document_validation => false
    };

optimize_for_deployment({replica_set, _}, Options) ->
    % 副本集优化：平衡一致性和性能
    Options#{
        batch_size => maps:get(batch_size, Options, 800),
        ordered => false,  % 允许并行写入提高性能
        bypass_document_validation => false,
        read_concern => #{<<"level">> => <<"majority">>}
    };

optimize_for_deployment({sharded_cluster, _}, Options) ->
    % 分片集群优化：最大化并行性
    Options#{
        batch_size => maps:get(batch_size, Options, 500),  % 较小批次，更好的分布
        ordered => false,  % 必须无序以支持分片
        bypass_document_validation => false,
        read_concern => #{<<"level">> => <<"local">>}  % 本地读取减少延迟
    }.

%% @doc 获取读偏好设置
-spec get_read_preference(deployment_mode(), atom()) -> map().
get_read_preference(single, _Operation) ->
    #{<<"mode">> => <<"primary">>};

get_read_preference({replica_set, _}, read_heavy) ->
    #{
        <<"mode">> => <<"secondaryPreferred">>,
        <<"maxStalenessSeconds">> => 90,
        <<"tags">> => [#{<<"usage">> => <<"analytics">>}]
    };
get_read_preference({replica_set, _}, write_heavy) ->
    #{<<"mode">> => <<"primaryPreferred">>};
get_read_preference({replica_set, _}, _) ->
    #{<<"mode">> => <<"primaryPreferred">>};

get_read_preference({sharded_cluster, _}, read_heavy) ->
    #{
        <<"mode">> => <<"nearest">>,
        <<"maxStalenessSeconds">> => 120
    };
get_read_preference({sharded_cluster, _}, _) ->
    #{<<"mode">> => <<"primaryPreferred">>}.

%% @doc 获取写关注设置
-spec get_write_concern(deployment_mode(), atom()) -> map().
get_write_concern(single, _Operation) ->
    #{<<"w">> => 1};

get_write_concern({replica_set, _}, high_consistency) ->
    #{
        <<"w">> => <<"majority">>,
        <<"j">> => true,
        <<"wtimeout">> => 10000
    };
get_write_concern({replica_set, _}, high_performance) ->
    #{
        <<"w">> => 1,
        <<"j">> => false
    };
get_write_concern({replica_set, _}, _) ->
    #{
        <<"w">> => <<"majority">>,
        <<"j">> => true,
        <<"wtimeout">> => 5000
    };

get_write_concern({sharded_cluster, _}, high_consistency) ->
    #{
        <<"w">> => <<"majority">>,
        <<"j">> => true,
        <<"wtimeout">> => 15000
    };
get_write_concern({sharded_cluster, _}, _) ->
    #{
        <<"w">> => <<"majority">>,
        <<"j">> => false,
        <<"wtimeout">> => 10000
    }.

%% @doc 获取最优读偏好
-spec get_optimal_read_preference(deployment_mode(), map()) -> map().
get_optimal_read_preference(DeploymentMode, Context) ->
    Operation = maps:get(operation_type, Context, balanced),
    Latency = maps:get(acceptable_latency_ms, Context, 100),

    BaseReadPref = get_read_preference(DeploymentMode, Operation),

    % 根据延迟要求调整
    case Latency < 50 of
        true ->
            % 低延迟要求，优先本地读取
            BaseReadPref#{<<"mode">> => <<"nearest">>};
        false ->
            BaseReadPref
    end.

%% @doc 获取最优写关注
-spec get_optimal_write_concern(deployment_mode(), map()) -> map().
get_optimal_write_concern(DeploymentMode, Context) ->
    Consistency = maps:get(consistency_level, Context, balanced),
    Performance = maps:get(performance_priority, Context, balanced),

    case {Consistency, Performance} of
        {high, _} ->
            get_write_concern(DeploymentMode, high_consistency);
        {_, high} ->
            get_write_concern(DeploymentMode, high_performance);
        _ ->
            get_write_concern(DeploymentMode, balanced)
    end.

%%%===================================================================
%%% 故障转移和负载均衡API实现
%%%===================================================================

%% @doc 处理故障转移
-spec handle_failover(pid(), deployment_mode(), map()) -> {ok, pid()} | {error, term()}.
handle_failover(FailedConnection, DeploymentMode, Options) ->
    try
        ?SLOG(warning, #{
            msg => "handling_connection_failover",
            failed_connection => FailedConnection,
            deployment_mode => DeploymentMode
        }),

        case DeploymentMode of
            single ->
                % 单节点模式，尝试重新连接到同一节点
                handle_single_node_failover(FailedConnection, Options);
            {replica_set, ReplicaSetName} ->
                % 副本集模式，尝试连接到其他副本
                handle_replica_set_failover(FailedConnection, ReplicaSetName, Options);
            {sharded_cluster, ShardInfo} ->
                % 分片集群模式，尝试连接到其他mongos
                handle_sharded_cluster_failover(FailedConnection, ShardInfo, Options)
        end
    catch
        E:R ->
            {error, {failover_error, {E, R}}}
    end.

%% @doc 选择最优连接
-spec select_optimal_connection([pid()], map()) -> {ok, pid()} | {error, term()}.
select_optimal_connection([], _Options) ->
    {error, no_connections_available};
select_optimal_connection(Connections, Options) ->
    try
        Strategy = maps:get(selection_strategy, Options, health_based),

        case Strategy of
            health_based ->
                select_healthiest_connection(Connections);
            round_robin ->
                select_round_robin_connection(Connections);
            random ->
                select_random_connection(Connections);
            latency_based ->
                select_lowest_latency_connection(Connections)
        end
    catch
        E:R ->
            {error, {selection_error, {E, R}}}
    end.

%% @doc 获取连接健康评分
-spec get_connection_health_score(pid()) -> {ok, float()} | {error, term()}.
get_connection_health_score(Connection) ->
    try
        % 执行健康检查
        case check_connection_health(Connection) of
            #{status := ok, latency := Latency} ->
                % 基于延迟计算健康评分 (0.0-1.0)
                Score = calculate_health_score(Latency, 0.0),
                {ok, Score};
            #{status := warning, latency := Latency} ->
                Score = calculate_health_score(Latency, 0.2),
                {ok, Score};
            #{status := error} ->
                {ok, 0.1};
            #{status := critical} ->
                {ok, 0.0};
            _ ->
                {ok, 0.5}  % 默认评分
        end
    catch
        E:R ->
            {error, {health_check_error, {E, R}}}
    end.

%% @doc 重新平衡连接
-spec rebalance_connections([pid()], map()) -> {ok, [pid()]} | {error, term()}.
rebalance_connections(Connections, Options) ->
    try
        Strategy = maps:get(rebalance_strategy, Options, health_based),
        TargetCount = maps:get(target_connection_count, Options, length(Connections)),

        % 获取所有连接的健康状态
        HealthScores = lists:map(
            fun(Conn) ->
                case get_connection_health_score(Conn) of
                    {ok, Score} -> {Conn, Score};
                    {error, _} -> {Conn, 0.0}
                end
            end,
            Connections
        ),

        % 根据策略重新排序
        SortedConnections = case Strategy of
            health_based ->
                lists:sort(fun({_, Score1}, {_, Score2}) -> Score1 >= Score2 end, HealthScores);
            random ->
                shuffle_connections(HealthScores)
        end,

        % 选择目标数量的连接
        SelectedConnections = lists:sublist([Conn || {Conn, _} <- SortedConnections], TargetCount),

        {ok, SelectedConnections}
    catch
        E:R ->
            {error, {rebalance_error, {E, R}}}
    end.

%% @doc 处理拓扑变化
-spec handle_topology_change(deployment_mode(), map()) -> ok | {error, term()}.
handle_topology_change(DeploymentMode, ChangeInfo) ->
    try
        ?SLOG(info, #{
            msg => "handling_topology_change",
            deployment_mode => DeploymentMode,
            change_info => ChangeInfo
        }),

        case DeploymentMode of
            single ->
                % 单节点模式，拓扑变化较少
                handle_single_node_topology_change(ChangeInfo);
            {replica_set, _} ->
                % 副本集模式，处理主从切换
                handle_replica_set_topology_change(ChangeInfo);
            {sharded_cluster, _} ->
                % 分片集群模式，处理分片变化
                handle_sharded_cluster_topology_change(ChangeInfo)
        end
    catch
        E:R ->
            {error, {topology_change_error, {E, R}}}
    end.

%%%===================================================================
%%% 分片集群特定API实现
%%%===================================================================

%% @doc 获取分片键
-spec get_shard_key(binary(), map()) -> {ok, map()} | {error, term()}.
get_shard_key(Collection, Document) ->
    try
        % 这里应该根据集合的分片配置来确定分片键
        % 简化实现，假设使用_id作为分片键
        case maps:get(<<"_id">>, Document, undefined) of
            undefined ->
                % 如果没有_id，生成一个
                ShardKey = #{<<"_id">> => generate_object_id()},
                {ok, ShardKey};
            Id ->
                {ok, #{<<"_id">> => Id}}
        end
    catch
        E:R ->
            {error, {shard_key_error, {E, R}}}
    end.

%% @doc 路由到特定分片
-spec route_to_shard(map(), binary(), map()) -> {ok, binary()} | {error, term()}.
route_to_shard(ShardKey, Collection, ShardInfo) ->
    try
        % 简化的分片路由逻辑
        % 实际实现应该基于分片配置和范围
        ShardId = calculate_shard_id(ShardKey, ShardInfo),
        {ok, ShardId}
    catch
        E:R ->
            {error, {routing_error, {E, R}}}
    end.

%% @doc 获取分片分布信息
-spec get_shard_distribution(pid()) -> {ok, map()} | {error, term()}.
get_shard_distribution(Connection) ->
    try
        % 获取分片状态信息
        case mc_worker_api:command(Connection, #{<<"sh.status">> => 1}) of
            {true, Result} ->
                {ok, parse_shard_distribution(Result)};
            {false, Error} ->
                {error, Error};
            Other ->
                {error, {unexpected_response, Other}}
        end
    catch
        E:R ->
            {error, {shard_distribution_error, {E, R}}}
    end.

%% @doc 优化分片查询
-spec optimize_shard_queries([map()], map()) -> {ok, [map()]} | {error, term()}.
optimize_shard_queries(Queries, ShardInfo) ->
    try
        % 根据分片信息优化查询
        OptimizedQueries = lists:map(
            fun(Query) ->
                optimize_single_shard_query(Query, ShardInfo)
            end,
            Queries
        ),
        {ok, OptimizedQueries}
    catch
        E:R ->
            {error, {query_optimization_error, {E, R}}}
    end.

%%%===================================================================
%%% 副本集特定API实现
%%%===================================================================

%% @doc 获取主节点连接
-spec get_primary_connection([pid()]) -> {ok, pid()} | {error, term()}.
get_primary_connection(Connections) ->
    try
        % 检查每个连接，找到主节点
        find_primary_in_connections(Connections)
    catch
        E:R ->
            {error, {primary_detection_error, {E, R}}}
    end.

%% @doc 获取从节点连接
-spec get_secondary_connections([pid()]) -> {ok, [pid()]} | {error, term()}.
get_secondary_connections(Connections) ->
    try
        % 检查每个连接，找到从节点
        Secondaries = lists:filter(
            fun(Conn) ->
                case check_node_role(Conn) of
                    {ok, secondary} -> true;
                    _ -> false
                end
            end,
            Connections
        ),
        {ok, Secondaries}
    catch
        E:R ->
            {error, {secondary_detection_error, {E, R}}}
    end.

%% @doc 处理主节点选举
-spec handle_primary_election([pid()], map()) -> {ok, pid()} | {error, term()}.
handle_primary_election(Connections, ElectionInfo) ->
    try
        ?SLOG(info, #{
            msg => "handling_primary_election",
            connections => length(Connections),
            election_info => ElectionInfo
        }),

        % 等待选举完成
        timer:sleep(maps:get(election_wait_ms, ElectionInfo, 5000)),

        % 重新检测主节点
        get_primary_connection(Connections)
    catch
        E:R ->
            {error, {primary_election_error, {E, R}}}
    end.

%% @doc 平衡读负载
-spec balance_read_load([pid()], map()) -> {ok, pid()} | {error, term()}.
balance_read_load(Connections, Options) ->
    try
        ReadPreference = maps:get(read_preference, Options, secondary_preferred),

        case ReadPreference of
            primary ->
                get_primary_connection(Connections);
            secondary ->
                case get_secondary_connections(Connections) of
                    {ok, []} -> {error, no_secondary_available};
                    {ok, Secondaries} -> select_optimal_connection(Secondaries, Options);
                    Error -> Error
                end;
            primary_preferred ->
                case get_primary_connection(Connections) of
                    {ok, Primary} -> {ok, Primary};
                    {error, _} ->
                        case get_secondary_connections(Connections) of
                            {ok, [Secondary|_]} -> {ok, Secondary};
                            _ -> {error, no_connection_available}
                        end
                end;
            secondary_preferred ->
                case get_secondary_connections(Connections) of
                    {ok, [Secondary|_]} -> {ok, Secondary};
                    {error, _} -> get_primary_connection(Connections)
                end;
            nearest ->
                select_optimal_connection(Connections, Options#{selection_strategy => latency_based})
        end
    catch
        E:R ->
            {error, {load_balance_error, {E, R}}}
    end.

%%%===================================================================
%%% 部署模式支持的辅助函数
%%%===================================================================

%% @doc 检查是否为分片集群
-spec check_sharded_cluster(pid()) -> {ok, map()} | {error, term()}.
check_sharded_cluster(Connection) ->
    try
        case mc_worker_api:command(Connection, #{<<"isdbgrid">> => 1}) of
            {true, #{<<"isdbgrid">> := 1}} ->
                % 这是一个mongos实例，获取分片信息
                case mc_worker_api:command(Connection, #{<<"listShards">> => 1}) of
                    {true, #{<<"shards">> := Shards}} ->
                        {ok, #{shards => Shards}};
                    _ ->
                        {ok, #{shards => []}}
                end;
            _ ->
                {error, not_sharded_cluster}
        end
    catch
        E:R ->
            {error, {E, R}}
    end.

%% @doc 计算健康评分
-spec calculate_health_score(integer(), float()) -> float().
calculate_health_score(Latency, ErrorPenalty) ->
    % 基于延迟计算基础评分 (延迟越低评分越高)
    BaseScore = case Latency of
        L when L < 10 -> 1.0;
        L when L < 50 -> 0.9;
        L when L < 100 -> 0.8;
        L when L < 200 -> 0.7;
        L when L < 500 -> 0.6;
        L when L < 1000 -> 0.5;
        L when L < 2000 -> 0.4;
        L when L < 5000 -> 0.3;
        _ -> 0.2
    end,

    % 应用错误惩罚
    max(0.0, BaseScore - ErrorPenalty).

%% @doc 选择最健康的连接
-spec select_healthiest_connection([pid()]) -> {ok, pid()} | {error, term()}.
select_healthiest_connection(Connections) ->
    try
        HealthScores = lists:map(
            fun(Conn) ->
                case get_connection_health_score(Conn) of
                    {ok, Score} -> {Conn, Score};
                    {error, _} -> {Conn, 0.0}
                end
            end,
            Connections
        ),

        case lists:sort(fun({_, Score1}, {_, Score2}) -> Score1 >= Score2 end, HealthScores) of
            [{BestConn, _} | _] -> {ok, BestConn};
            [] -> {error, no_connections}
        end
    catch
        E:R ->
            {error, {E, R}}
    end.

%% @doc 轮询选择连接
-spec select_round_robin_connection([pid()]) -> {ok, pid()} | {error, term()}.
select_round_robin_connection([]) ->
    {error, no_connections};
select_round_robin_connection(Connections) ->
    % 简化的轮询实现，使用时间戳
    Index = erlang:system_time(microsecond) rem length(Connections),
    {ok, lists:nth(Index + 1, Connections)}.

%% @doc 随机选择连接
-spec select_random_connection([pid()]) -> {ok, pid()} | {error, term()}.
select_random_connection([]) ->
    {error, no_connections};
select_random_connection(Connections) ->
    Index = rand:uniform(length(Connections)),
    {ok, lists:nth(Index, Connections)}.

%% @doc 选择延迟最低的连接
-spec select_lowest_latency_connection([pid()]) -> {ok, pid()} | {error, term()}.
select_lowest_latency_connection(Connections) ->
    try
        LatencyScores = lists:map(
            fun(Conn) ->
                case check_connection_health(Conn) of
                    #{latency := Latency} -> {Conn, Latency};
                    _ -> {Conn, 999999}  % 高延迟作为惩罚
                end
            end,
            Connections
        ),

        case lists:sort(fun({_, L1}, {_, L2}) -> L1 =< L2 end, LatencyScores) of
            [{BestConn, _} | _] -> {ok, BestConn};
            [] -> {error, no_connections}
        end
    catch
        E:R ->
            {error, {E, R}}
    end.

%% @doc 随机打乱连接列表
-spec shuffle_connections([{pid(), float()}]) -> [{pid(), float()}].
shuffle_connections(Connections) ->
    % 简单的随机打乱实现
    Tagged = [{rand:uniform(), Conn} || Conn <- Connections],
    Sorted = lists:sort(Tagged),
    [Conn || {_, Conn} <- Sorted].

%% @doc 生成ObjectId
-spec generate_object_id() -> binary().
generate_object_id() ->
    % 简化的ObjectId生成
    Timestamp = erlang:system_time(second),
    Random = rand:uniform(16777216),  % 24位随机数
    Counter = erlang:system_time(microsecond) rem 16777216,

    <<Timestamp:32, Random:24, Counter:24>>.

%% @doc 计算分片ID
-spec calculate_shard_id(map(), map()) -> binary().
calculate_shard_id(ShardKey, ShardInfo) ->
    % 简化的分片ID计算
    KeyHash = erlang:phash2(ShardKey),
    ShardCount = maps:get(shard_count, ShardInfo, 4),
    ShardIndex = KeyHash rem ShardCount,
    iolist_to_binary(io_lib:format("shard~p", [ShardIndex])).

%% @doc 解析分片分布信息
-spec parse_shard_distribution(map()) -> map().
parse_shard_distribution(Result) ->
    % 简化的分片分布解析
    #{
        total_shards => maps:get(<<"totalShards">>, Result, 0),
        active_shards => maps:get(<<"activeShards">>, Result, 0),
        balancer_state => maps:get(<<"balancerState">>, Result, <<"unknown">>)
    }.

%% @doc 优化单个分片查询
-spec optimize_single_shard_query(map(), map()) -> map().
optimize_single_shard_query(Query, _ShardInfo) ->
    % 简化的查询优化
    % 实际实现应该根据分片键优化查询
    Query#{
        <<"hint">> => #{<<"_id">> => 1},
        <<"readConcern">> => #{<<"level">> => <<"local">>}
    }.

%% @doc 在连接中查找主节点
-spec find_primary_in_connections([pid()]) -> {ok, pid()} | {error, term()}.
find_primary_in_connections([]) ->
    {error, no_primary_found};
find_primary_in_connections([Conn | Rest]) ->
    case check_node_role(Conn) of
        {ok, primary} -> {ok, Conn};
        _ -> find_primary_in_connections(Rest)
    end.

%% @doc 检查节点角色
-spec check_node_role(pid()) -> {ok, atom()} | {error, term()}.
check_node_role(Connection) ->
    try
        case mc_worker_api:command(Connection, #{<<"isMaster">> => 1}) of
            {true, #{<<"ismaster">> := true}} ->
                {ok, primary};
            {true, #{<<"secondary">> := true}} ->
                {ok, secondary};
            {true, #{<<"arbiterOnly">> := true}} ->
                {ok, arbiter};
            _ ->
                {ok, unknown}
        end
    catch
        E:R ->
            {error, {E, R}}
    end.

%% @doc 处理单节点故障转移
-spec handle_single_node_failover(pid(), map()) -> {ok, pid()} | {error, term()}.
handle_single_node_failover(_FailedConnection, Options) ->
    % 单节点模式的故障转移通常意味着重新连接
    RetryCount = maps:get(retry_count, Options, 3),
    RetryDelay = maps:get(retry_delay_ms, Options, 1000),

    % 这里应该尝试重新建立连接
    % 简化实现，返回错误让上层处理
    timer:sleep(RetryDelay),
    {error, {single_node_failover_not_implemented, RetryCount}}.

%% @doc 处理副本集故障转移
-spec handle_replica_set_failover(pid(), binary(), map()) -> {ok, pid()} | {error, term()}.
handle_replica_set_failover(_FailedConnection, ReplicaSetName, Options) ->
    % 副本集故障转移应该尝试连接到其他副本
    RetryCount = maps:get(retry_count, Options, 3),

    ?SLOG(info, #{
        msg => "attempting_replica_set_failover",
        replica_set => ReplicaSetName,
        retry_count => RetryCount
    }),

    % 这里应该实现副本集的故障转移逻辑
    {error, {replica_set_failover_not_implemented, ReplicaSetName}}.

%% @doc 处理分片集群故障转移
-spec handle_sharded_cluster_failover(pid(), map(), map()) -> {ok, pid()} | {error, term()}.
handle_sharded_cluster_failover(_FailedConnection, ShardInfo, Options) ->
    % 分片集群故障转移应该尝试连接到其他mongos
    RetryCount = maps:get(retry_count, Options, 3),

    ?SLOG(info, #{
        msg => "attempting_sharded_cluster_failover",
        shard_info => ShardInfo,
        retry_count => RetryCount
    }),

    % 这里应该实现分片集群的故障转移逻辑
    {error, {sharded_cluster_failover_not_implemented, ShardInfo}}.

%% @doc 处理单节点拓扑变化
-spec handle_single_node_topology_change(map()) -> ok.
handle_single_node_topology_change(_ChangeInfo) ->
    % 单节点模式拓扑变化处理
    ok.

%% @doc 处理副本集拓扑变化
-spec handle_replica_set_topology_change(map()) -> ok.
handle_replica_set_topology_change(ChangeInfo) ->
    % 副本集拓扑变化处理
    ?SLOG(info, #{
        msg => "replica_set_topology_changed",
        change_info => ChangeInfo
    }),
    ok.

%% @doc 处理分片集群拓扑变化
-spec handle_sharded_cluster_topology_change(map()) -> ok.
handle_sharded_cluster_topology_change(ChangeInfo) ->
    % 分片集群拓扑变化处理
    ?SLOG(info, #{
        msg => "sharded_cluster_topology_changed",
        change_info => ChangeInfo
    }),
    ok.

%% @doc 验证配置键
-spec validate_config_keys(map(), [atom()]) -> ok | {error, term()}.
validate_config_keys(Config, RequiredKeys) ->
    MissingKeys = [Key || Key <- RequiredKeys, not maps:is_key(Key, Config)],
    case MissingKeys of
        [] -> ok;
        _ -> {error, {missing_required_keys, MissingKeys}}
    end.

%% @doc 验证读偏好设置
-spec validate_read_preference(deployment_mode(), map()) -> ok | {error, term()}.
validate_read_preference(single, #{<<"mode">> := <<"primary">>}) ->
    ok;
validate_read_preference(single, _) ->
    {error, invalid_read_preference_for_single_node};
validate_read_preference({replica_set, _}, #{<<"mode">> := Mode})
    when Mode =:= <<"primary">>; Mode =:= <<"primaryPreferred">>;
         Mode =:= <<"secondary">>; Mode =:= <<"secondaryPreferred">>;
         Mode =:= <<"nearest">> ->
    ok;
validate_read_preference({sharded_cluster, _}, #{<<"mode">> := Mode})
    when Mode =:= <<"primary">>; Mode =:= <<"primaryPreferred">>;
         Mode =:= <<"secondary">>; Mode =:= <<"secondaryPreferred">>;
         Mode =:= <<"nearest">> ->
    ok;
validate_read_preference(_, _) ->
    {error, invalid_read_preference}.

%% @doc 验证写关注设置
-spec validate_write_concern(deployment_mode(), map()) -> ok | {error, term()}.
validate_write_concern(single, #{<<"w">> := W}) when is_integer(W), W >= 1 ->
    ok;
validate_write_concern({replica_set, _}, #{<<"w">> := W})
    when is_integer(W); W =:= <<"majority">> ->
    ok;
validate_write_concern({sharded_cluster, _}, #{<<"w">> := W})
    when is_integer(W); W =:= <<"majority">> ->
    ok;
validate_write_concern(_, _) ->
    {error, invalid_write_concern}.

%% @doc 获取EMQX MongoDB配置
-spec get_emqx_mongodb_config() -> map().
get_emqx_mongodb_config() ->
    try
        case emqx:get_config([plugin_mongodb], undefined) of
            undefined ->
                get_default_mongodb_config();
            Config ->
                extract_mongodb_config(Config)
        end
    catch
        _:_ ->
            get_default_mongodb_config()
    end.

%% @doc 提取MongoDB配置
-spec extract_mongodb_config(map()) -> map().
extract_mongodb_config(Config) ->
    ConnectionConfig = maps:get(connection, Config, #{}),
    BatchConfig = maps:get(batch, ConnectionConfig, #{}),
    TopologyConfig = maps:get(topology, ConnectionConfig, #{}),

    #{
        % 批处理配置
        batch_size => maps:get(max_size, BatchConfig, 1000),
        min_batch_size => maps:get(min_size, BatchConfig, 100),

        % 写关注配置
        write_concern => case maps:get(w_mode, ConnectionConfig, unsafe) of
            safe -> #{<<"w">> => <<"majority">>, <<"j">> => true};
            unsafe -> #{<<"w">> => 1}
        end,

        % 连接池配置
        pool_size => maps:get(pool_size, TopologyConfig, 16),
        max_overflow => maps:get(max_overflow, TopologyConfig, 32),

        % 超时配置
        server_selection_timeout_ms => maps:get(server_selection_timeout_ms, TopologyConfig, 30000),
        connect_timeout_ms => maps:get(connect_timeout_ms, TopologyConfig, 20000),
        socket_timeout_ms => maps:get(socket_timeout_ms, TopologyConfig, 5000),

        % 部署模式配置
        mongo_type => maps:get(mongo_type, ConnectionConfig, single),
        replica_set_name => maps:get(replica_set_name, ConnectionConfig, undefined),

        % 认证配置
        auth_source => maps:get(auth_source, ConnectionConfig, <<"admin">>),

        % 读写偏好配置
        read_preference => maps:get(read_preference, ConnectionConfig, secondaryPreferred),
        read_concern_level => maps:get(read_concern_level, ConnectionConfig, majority),
        write_concern => maps:get(write_concern, ConnectionConfig, majority),
        write_concern_timeout => maps:get(write_concern_timeout, ConnectionConfig, 10000)
    }.

%% ============================================================================
%% 连接配置处理函数
%% ============================================================================

%% @doc 构建MongoDB连接选项，包含认证源等高级配置
-spec build_mongo_connection_options(map()) -> map().
build_mongo_connection_options(ConnectionConfig) ->
    BaseOptions = #{
        % 基础连接配置
        database => maps:get(database, ConnectionConfig, <<"test">>),

        % 认证配置
        username => maps:get(username, ConnectionConfig, undefined),
        password => maps:get(password, ConnectionConfig, undefined),
        auth_source => maps:get(auth_source, ConnectionConfig, <<"admin">>),

        % 读写偏好配置
        read_preference => maps:get(read_preference, ConnectionConfig, secondaryPreferred),
        read_concern_level => maps:get(read_concern_level, ConnectionConfig, majority),
        write_concern => maps:get(write_concern, ConnectionConfig, majority),
        write_concern_timeout => maps:get(write_concern_timeout, ConnectionConfig, 10000),

        % SSL配置
        ssl => build_ssl_options(ConnectionConfig)
    },

    % 添加部署模式配置
    DeploymentOptions = case maps:get(mongo_type, ConnectionConfig, single) of
        rs ->
            BaseOptions#{
                replica_set_name => maps:get(replica_set_name, ConnectionConfig, undefined)
            };
        _ ->
            BaseOptions
    end,

    DeploymentOptions.

%% @doc 构建写关注选项
-spec build_write_concern_options(map()) -> map().
build_write_concern_options(Config) ->
    WriteConcern = maps:get(write_concern, Config, majority),
    Timeout = maps:get(write_concern_timeout, Config, 10000),

    case WriteConcern of
        majority ->
            #{<<"w">> => <<"majority">>, <<"wtimeout">> => Timeout};
        N when is_integer(N) ->
            #{<<"w">> => N, <<"wtimeout">> => Timeout};
        Tag when is_binary(Tag) ->
            #{<<"w">> => Tag, <<"wtimeout">> => Timeout};
        _ ->
            #{<<"w">> => 1, <<"wtimeout">> => Timeout}
    end.

%% @doc 构建读偏好选项
-spec build_read_preference_options(map()) -> map().
build_read_preference_options(Config) ->
    ReadPreference = maps:get(read_preference, Config, secondaryPreferred),
    ReadConcernLevel = maps:get(read_concern_level, Config, majority),

    #{
        <<"$readPreference">> => #{
            <<"mode">> => atom_to_binary(ReadPreference, utf8)
        },
        <<"readConcern">> => #{
            <<"level">> => atom_to_binary(ReadConcernLevel, utf8)
        }
    }.

%% @doc 构建SSL选项
-spec build_ssl_options(map()) -> {boolean(), list()}.
build_ssl_options(ConnectionConfig) ->
    SSLConfig = maps:get(ssl, ConnectionConfig, #{}),
    SSLEnabled = maps:get(enable, SSLConfig, false),

    case SSLEnabled of
        false ->
            {false, []};
        true ->
            SSLOpts = build_ssl_opts_list(SSLConfig),
            {true, SSLOpts}
    end.

%% @doc 构建SSL选项列表
-spec build_ssl_opts_list(map()) -> list().
build_ssl_opts_list(SSLConfig) ->
    BaseOpts = [
        {verify, maps:get(verify, SSLConfig, verify_peer)},
        {depth, maps:get(depth, SSLConfig, 10)},
        {reuse_sessions, maps:get(reuse_sessions, SSLConfig, true)},
        {secure_renegotiate, maps:get(secure_renegotiate, SSLConfig, true)}
    ],

    % 添加证书文件选项
    CertOpts = build_cert_options(SSLConfig),

    % 添加协议版本选项
    VersionOpts = build_version_options(SSLConfig),

    % 添加加密套件选项
    CipherOpts = build_cipher_options(SSLConfig),

    % 添加主机名验证选项
    HostnameOpts = build_hostname_options(SSLConfig),

    % 添加其他选项
    OtherOpts = build_other_ssl_options(SSLConfig),

    lists:flatten([BaseOpts, CertOpts, VersionOpts, CipherOpts, HostnameOpts, OtherOpts]).

%% @doc 构建证书相关选项
-spec build_cert_options(map()) -> list().
build_cert_options(SSLConfig) ->
    CertFile = maps:get(certfile, SSLConfig, <<"">>),
    KeyFile = maps:get(keyfile, SSLConfig, <<"">>),
    CACertFile = maps:get(cacertfile, SSLConfig, <<"">>),
    Password = maps:get(password, SSLConfig, <<"">>),

    Opts = [],

    % 添加客户端证书
    Opts1 = case CertFile of
        <<"">> -> Opts;
        _ -> [{certfile, binary_to_list(CertFile)} | Opts]
    end,

    % 添加私钥文件
    Opts2 = case KeyFile of
        <<"">> -> Opts1;
        _ -> [{keyfile, binary_to_list(KeyFile)} | Opts1]
    end,

    % 添加CA证书
    Opts3 = case CACertFile of
        <<"">> -> Opts2;
        _ -> [{cacertfile, binary_to_list(CACertFile)} | Opts2]
    end,

    % 添加密码
    case Password of
        <<"">> -> Opts3;
        _ -> [{password, binary_to_list(Password)} | Opts3]
    end.

%% @doc 构建协议版本选项
-spec build_version_options(map()) -> list().
build_version_options(SSLConfig) ->
    Versions = maps:get(versions, SSLConfig, [<<"tlsv1.2">>, <<"tlsv1.3">>]),
    case Versions of
        [] -> [];
        _ ->
            VersionAtoms = [binary_to_atom(V, utf8) || V <- Versions],
            [{versions, VersionAtoms}]
    end.

%% @doc 构建加密套件选项
-spec build_cipher_options(map()) -> list().
build_cipher_options(SSLConfig) ->
    Ciphers = maps:get(ciphers, SSLConfig, []),
    case Ciphers of
        [] -> [];
        _ -> [{ciphers, [binary_to_list(C) || C <- Ciphers]}]
    end.

%% @doc 构建主机名验证选项
-spec build_hostname_options(map()) -> list().
build_hostname_options(SSLConfig) ->
    VerifyHostname = maps:get(verify_hostname, SSLConfig, true),
    SNI = maps:get(server_name_indication, SSLConfig, <<"">>),
    CustomizeCheck = maps:get(customize_hostname_check, SSLConfig, []),

    Opts = [],

    % 添加主机名验证
    Opts1 = case VerifyHostname of
        true -> [{verify_fun, {fun ssl_verify_hostname:verify_fun/3, []}} | Opts];
        false -> Opts
    end,

    % 添加SNI
    Opts2 = case SNI of
        <<"">> -> Opts1;
        _ -> [{server_name_indication, binary_to_list(SNI)} | Opts1]
    end,

    % 添加自定义验证
    case CustomizeCheck of
        [] -> Opts2;
        _ -> [{customize_hostname_check, CustomizeCheck} | Opts2]
    end.

%% @doc 构建其他SSL选项
-spec build_other_ssl_options(map()) -> list().
build_other_ssl_options(SSLConfig) ->
    PartialChain = maps:get(partial_chain, SSLConfig, false),
    CRLCheck = maps:get(crl_check, SSLConfig, false),

    Opts = [],

    % 添加部分链验证
    Opts1 = case PartialChain of
        true -> [{partial_chain, fun(_) -> unknown_ca end} | Opts];
        false -> Opts
    end,

    % 添加CRL检查
    case CRLCheck of
        true -> [{crl_check, true} | Opts1];
        false -> Opts1
    end.

%% ============================================================================
%% 高级MongoDB操作API - 统一接口层
%% ============================================================================

%% @doc 智能执行MongoDB命令 - 统一入口点
-spec execute_command(pid(), map()) -> {ok, term()} | {error, term()}.
execute_command(Pid, Command) ->
    execute_command(Pid, Command, #{}).

%% @doc 智能执行MongoDB命令（带选项）
-spec execute_command(pid(), map(), map()) -> {ok, term()} | {error, term()}.
execute_command(Pid, Command, Options) ->
    try
        % 参数验证
        ok = validate_command_params(Pid, Command, Options),

        % 获取执行选项
        Timeout = maps:get(timeout, Options, 30000),
        RetryCount = maps:get(retry_count, Options, 3),

        % 智能执行
        execute_with_smart_retry(Pid, Command, Timeout, RetryCount, Options)
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "execute_command_error",
                command => Command,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 高级插入操作 - 支持批量、事务、分片优化
-spec smart_insert(pid(), binary(), [map()]) -> {ok, map()} | {error, term()}.
smart_insert(Pid, Collection, Documents) ->
    smart_insert(Pid, Collection, Documents, #{}).

%% @doc 高级插入操作（带选项）
-spec smart_insert(pid(), binary(), [map()], map()) -> {ok, map()} | {error, term()}.
smart_insert(Pid, Collection, Documents, Options) ->
    try
        % 参数验证
        ok = validate_insert_params(Pid, Collection, Documents, Options),

        % 文档预处理
        ProcessedDocs = preprocess_documents(Documents, Options),

        % 智能批量处理
        case maps:get(batch_size, Options, auto) of
            auto ->
                OptimalBatchSize = calculate_optimal_batch_size(ProcessedDocs, Options),
                execute_batched_insert(Pid, Collection, ProcessedDocs, OptimalBatchSize, Options);
            BatchSize when is_integer(BatchSize), BatchSize > 0 ->
                execute_batched_insert(Pid, Collection, ProcessedDocs, BatchSize, Options);
            _ ->
                execute_single_insert(Pid, Collection, ProcessedDocs, Options)
        end
    catch
        E:R:S ->
            DocCount = case Documents of
                Docs when is_list(Docs) -> length(Docs);
                _ when is_map(Documents) -> 1;
                _ -> 0
            end,
            ?SLOG(error, #{
                msg => "smart_insert_error",
                collection => Collection,
                doc_count => DocCount,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 高级更新操作 - 支持复杂更新、条件更新、批量更新
-spec smart_update(pid(), binary(), map(), map()) -> {ok, map()} | {error, term()}.
smart_update(Pid, Collection, Filter, Update) ->
    smart_update(Pid, Collection, Filter, Update, #{}).

%% @doc 高级更新操作（带选项）
-spec smart_update(pid(), binary(), map(), map(), map()) -> {ok, map()} | {error, term()}.
smart_update(Pid, Collection, Filter, Update, Options) ->
    try
        % 参数验证
        ok = validate_update_params(Pid, Collection, Filter, Update, Options),

        % 更新类型检测
        UpdateType = detect_update_type(Update, Options),

        % 根据更新类型选择最优执行策略
        case UpdateType of
            upsert ->
                execute_smart_upsert(Pid, Collection, Filter, Update, Options);
            bulk_update ->
                execute_bulk_update(Pid, Collection, Filter, Update, Options);
            atomic_update ->
                execute_atomic_update(Pid, Collection, Filter, Update, Options);
            standard_update ->
                execute_standard_update(Pid, Collection, Filter, Update, Options)
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "smart_update_error",
                collection => Collection,
                filter => Filter,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 高级查询操作 - 支持复杂查询、聚合、分页
-spec smart_find(pid(), binary(), map()) -> {ok, [map()]} | {error, term()}.
smart_find(Pid, Collection, Filter) ->
    smart_find(Pid, Collection, Filter, #{}).

%% @doc 高级查询操作（带选项）
-spec smart_find(pid(), binary(), map(), map()) -> {ok, [map()]} | {error, term()}.
smart_find(Pid, Collection, Filter, Options) ->
    try
        % 参数验证
        ok = validate_find_params(Pid, Collection, Filter, Options),

        % 查询优化
        OptimizedFilter = optimize_query_filter(Filter, Options),
        QueryOptions = build_query_options(Options),

        % 执行查询
        case maps:get(use_cursor, Options, true) of
            true ->
                execute_cursor_find(Pid, Collection, OptimizedFilter, QueryOptions, Options);
            false ->
                execute_direct_find(Pid, Collection, OptimizedFilter, QueryOptions, Options)
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "smart_find_error",
                collection => Collection,
                filter => Filter,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 获取默认MongoDB配置
-spec get_default_mongodb_config() -> map().
get_default_mongodb_config() ->
    #{
        batch_size => 1000,
        min_batch_size => 100,
        write_concern => #{<<"w">> => 1},
        pool_size => 16,
        max_overflow => 32,
        server_selection_timeout_ms => 30000,
        connect_timeout_ms => 20000,
        socket_timeout_ms => 5000,
        mongo_type => single,
        replica_set_name => undefined,
        auth_source => <<"admin">>,
        read_preference => secondaryPreferred,
        read_concern_level => majority,
        write_concern => majority,
        write_concern_timeout => 10000,
        ssl => #{
            enable => false,
            verify => verify_peer,
            depth => 10,
            versions => [<<"tlsv1.2">>, <<"tlsv1.3">>]
        }
    }.

%% ============================================================================
%% 高级操作实现函数
%% ============================================================================

%% @doc 参数验证函数
-spec validate_command_params(pid(), map(), map()) -> ok | {error, term()}.
validate_command_params(Pid, Command, _Options) when is_pid(Pid), is_map(Command) ->
    case is_process_alive(Pid) of
        true -> ok;
        false -> {error, connection_dead}
    end;
validate_command_params(_Pid, _Command, _Options) ->
    {error, invalid_parameters}.

%% @doc 插入参数验证
-spec validate_insert_params(pid(), binary(), [map()], map()) -> ok | {error, term()}.
validate_insert_params(Pid, Collection, Documents, _Options)
    when is_pid(Pid), is_binary(Collection), is_list(Documents) ->
    case {is_process_alive(Pid), validate_documents(Documents)} of
        {true, ok} -> ok;
        {false, _} -> {error, connection_dead};
        {_, Error} -> Error
    end;
validate_insert_params(_Pid, _Collection, _Documents, _Options) ->
    {error, invalid_parameters}.

%% @doc 更新参数验证
-spec validate_update_params(pid(), binary(), map(), map(), map()) -> ok | {error, term()}.
validate_update_params(Pid, Collection, Filter, Update, _Options)
    when is_pid(Pid), is_binary(Collection), is_map(Filter), is_map(Update) ->
    case is_process_alive(Pid) of
        true -> ok;
        false -> {error, connection_dead}
    end;
validate_update_params(_Pid, _Collection, _Filter, _Update, _Options) ->
    {error, invalid_parameters}.

%% @doc 查询参数验证
-spec validate_find_params(pid(), binary(), map(), map()) -> ok | {error, term()}.
validate_find_params(Pid, Collection, Filter, _Options)
    when is_pid(Pid), is_binary(Collection), is_map(Filter) ->
    case is_process_alive(Pid) of
        true -> ok;
        false -> {error, connection_dead}
    end;
validate_find_params(_Pid, _Collection, _Filter, _Options) ->
    {error, invalid_parameters}.

%% @doc 智能重试执行
-spec execute_with_smart_retry(pid(), map(), integer(), integer(), map()) -> {ok, term()} | {error, term()}.
execute_with_smart_retry(Pid, Command, Timeout, RetryCount, Options) ->
    Operation = fun() ->
        case mc_worker_api:command(Pid, Command) of
            {true, Result} -> {ok, Result};
            {false, Error} -> {error, Error};
            Other -> {error, {unexpected_response, Other}}
        end
    end,

    RetryOptions = #{
        max_attempts => RetryCount,
        initial_delay => maps:get(initial_delay, Options, 100),
        max_delay => maps:get(max_delay, Options, 5000),
        backoff_factor => maps:get(backoff_factor, Options, 2.0),
        retry_on => [connection_error, timeout_error, network_error]
    },

    safe_execute(Pid, Operation, RetryOptions).

%% @doc 文档预处理
-spec preprocess_documents([map()], map()) -> [map()].
preprocess_documents(Documents, Options) ->
    AddTimestamp = maps:get(add_timestamp, Options, false),
    AddId = maps:get(add_id, Options, false),

    lists:map(
        fun(Doc) ->
            Doc1 = case AddTimestamp of
                true -> Doc#{<<"created_at">> => erlang:system_time(millisecond)};
                false -> Doc
            end,
            case AddId of
                true ->
                    case maps:is_key(<<"_id">>, Doc1) of
                        false -> Doc1#{<<"_id">> => generate_object_id()};
                        true -> Doc1
                    end;
                false -> Doc1
            end
        end,
        Documents
    ).

%% @doc 计算最优批次大小
-spec calculate_optimal_batch_size([map()], map()) -> integer().
calculate_optimal_batch_size(Documents, Options) ->
    DocCount = length(Documents),
    MaxBatchSize = maps:get(max_batch_size, Options, 1000),
    MinBatchSize = maps:get(min_batch_size, Options, 100),

    % 基于文档数量和大小计算最优批次
    AvgDocSize = estimate_average_document_size(Documents),
    OptimalSize = case AvgDocSize of
        Size when Size < 1024 -> min(MaxBatchSize, max(MinBatchSize, DocCount div 10));
        Size when Size < 10240 -> min(500, max(MinBatchSize, DocCount div 20));
        _ -> min(100, max(MinBatchSize, DocCount div 50))
    end,

    max(MinBatchSize, min(MaxBatchSize, OptimalSize)).

%% @doc 估算文档平均大小
-spec estimate_average_document_size([map()]) -> integer().
estimate_average_document_size([]) -> 0;
estimate_average_document_size(Documents) ->
    SampleSize = min(10, length(Documents)),
    Sample = lists:sublist(Documents, SampleSize),
    TotalSize = lists:sum([estimate_document_size(Doc) || Doc <- Sample]),
    TotalSize div SampleSize.

%% @doc 估算单个文档大小
-spec estimate_document_size(map()) -> integer().
estimate_document_size(Doc) ->
    % 简单估算：每个键值对约50字节，嵌套结构额外计算
    maps:fold(
        fun(Key, Value, Acc) ->
            KeySize = estimate_key_size(Key),
            ValueSize = estimate_value_size(Value),
            Acc + KeySize + ValueSize + 10  % 10字节开销
        end,
        0,
        Doc
    ).

%% @doc 估算键大小
-spec estimate_key_size(term()) -> integer().
estimate_key_size(Key) when is_binary(Key) -> byte_size(Key);
estimate_key_size(Key) when is_atom(Key) -> byte_size(atom_to_binary(Key, utf8));
estimate_key_size(Key) when is_list(Key) -> length(Key);
estimate_key_size(Key) when is_integer(Key) -> 8;
estimate_key_size(_Key) -> 20.  % 默认估算

%% @doc 估算值大小
-spec estimate_value_size(term()) -> integer().
estimate_value_size(Value) when is_binary(Value) -> byte_size(Value);
estimate_value_size(Value) when is_integer(Value) -> 8;
estimate_value_size(Value) when is_float(Value) -> 8;
estimate_value_size(Value) when is_boolean(Value) -> 1;
estimate_value_size(Value) when is_map(Value) -> estimate_document_size(Value);
estimate_value_size(Value) when is_list(Value) ->
    lists:sum([estimate_value_size(V) || V <- Value]);
estimate_value_size(_) -> 50.  % 默认估算



%% @doc 检测更新类型
-spec detect_update_type(map(), map()) -> atom().
detect_update_type(Update, Options) ->
    IsUpsert = maps:get(upsert, Options, false),
    IsMulti = maps:get(multi, Options, false),
    HasAtomicOps = has_atomic_operators(Update),

    case {IsUpsert, IsMulti, HasAtomicOps} of
        {true, _, _} -> upsert;
        {false, true, _} -> bulk_update;
        {false, false, true} -> atomic_update;
        {false, false, false} -> standard_update
    end.

%% @doc 检查是否有原子操作符
-spec has_atomic_operators(map()) -> boolean().
has_atomic_operators(Update) ->
    AtomicOps = [<<"$set">>, <<"$unset">>, <<"$inc">>, <<"$mul">>, <<"$push">>, <<"$pull">>, <<"$addToSet">>],
    lists:any(fun(Op) -> maps:is_key(Op, Update) end, AtomicOps).

%% @doc 执行批量插入
-spec execute_batched_insert(pid(), binary(), [map()], integer(), map()) -> {ok, map()} | {error, term()}.
execute_batched_insert(Pid, Collection, Documents, BatchSize, Options) ->
    Batches = split_into_batches(Documents, BatchSize),
    WriteConcern = build_write_concern_from_options(Options),
    Results = lists:map(
        fun(Batch) ->
            try
                case mc_worker_api:insert(Pid, Collection, Batch, WriteConcern) of
                    ok -> #{inserted_count => length(Batch), errors => []};
                    {ok, _} -> #{inserted_count => length(Batch), errors => []};
                    {{true, _}, _} -> #{inserted_count => length(Batch), errors => []};
                    {{false, Error}, _} -> #{inserted_count => 0, errors => [Error]};
                    Error -> #{inserted_count => 0, errors => [Error]}
                end
            catch
                E:R:S ->
                    ?SLOG(error, #{
                        msg => "batch_insert_error",
                        error => E,
                        reason => R,
                        stacktrace => S
                    }),
                    #{inserted_count => 0, errors => [#{error => E, reason => R}]}
            end
        end,
        Batches
    ),

    % 合并结果
    TotalInserted = lists:sum([maps:get(inserted_count, R, 0) || R <- Results]),
    AllErrors = lists:flatten([maps:get(errors, R, []) || R <- Results]),

    case AllErrors of
        [] -> {ok, #{inserted_count => TotalInserted}};
        _ -> {error, #{inserted_count => TotalInserted, errors => AllErrors}}
    end.

%% @doc 执行单次插入
-spec execute_single_insert(pid(), binary(), [map()], map()) -> {ok, map()} | {error, term()}.
execute_single_insert(Pid, Collection, Documents, Options) ->
    try
        WriteConcern = build_write_concern_from_options(Options),
        case mc_worker_api:insert(Pid, Collection, Documents, WriteConcern) of
            ok -> {ok, #{inserted_count => length(Documents)}};
            {ok, _} -> {ok, #{inserted_count => length(Documents)}};
            {{true, _}, _} -> {ok, #{inserted_count => length(Documents)}};
            {{false, Error}, _} -> {error, Error};
            Error -> {error, Error}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "single_insert_error",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 执行智能upsert
-spec execute_smart_upsert(pid(), binary(), map(), map(), map()) -> {ok, map()} | {error, term()}.
execute_smart_upsert(Pid, Collection, Filter, Update, Options) ->
    try
        Command = #{
            <<"update">> => Collection,
            <<"updates">> => [#{
                <<"q">> => Filter,
                <<"u">> => Update,
                <<"upsert">> => true,
                <<"multi">> => maps:get(multi, Options, false)
            }]
        },

        case mc_worker_api:command(Pid, Command) of
            {true, Result} -> {ok, Result};
            {false, Error} -> {error, Error};
            Other -> {error, {unexpected_response, Other}}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "smart_upsert_error",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 执行批量更新
-spec execute_bulk_update(pid(), binary(), map(), map(), map()) -> {ok, map()} | {error, term()}.
execute_bulk_update(Pid, Collection, Filter, Update, Options) ->
    try
        Command = #{
            <<"update">> => Collection,
            <<"updates">> => [#{
                <<"q">> => Filter,
                <<"u">> => Update,
                <<"upsert">> => maps:get(upsert, Options, false),
                <<"multi">> => true
            }]
        },

        case mc_worker_api:command(Pid, Command) of
            {true, Result} -> {ok, Result};
            {false, Error} -> {error, Error};
            Other -> {error, {unexpected_response, Other}}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "bulk_update_error",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 执行原子更新
-spec execute_atomic_update(pid(), binary(), map(), map(), map()) -> {ok, map()} | {error, term()}.
execute_atomic_update(Pid, Collection, Filter, Update, Options) ->
    try
        Command = #{
            <<"update">> => Collection,
            <<"updates">> => [#{
                <<"q">> => Filter,
                <<"u">> => Update,
                <<"upsert">> => maps:get(upsert, Options, false),
                <<"multi">> => false
            }]
        },

        case mc_worker_api:command(Pid, Command) of
            {true, Result} -> {ok, Result};
            {false, Error} -> {error, Error};
            Other -> {error, {unexpected_response, Other}}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "atomic_update_error",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 执行标准更新
-spec execute_standard_update(pid(), binary(), map(), map(), map()) -> {ok, map()} | {error, term()}.
execute_standard_update(Pid, Collection, Filter, Update, Options) ->
    execute_atomic_update(Pid, Collection, Filter, Update, Options).

%% @doc 优化查询过滤器
-spec optimize_query_filter(map(), map()) -> map().
optimize_query_filter(Filter, Options) ->
    % 添加索引提示
    case maps:get(hint, Options, undefined) of
        undefined -> Filter;
        Hint -> Filter#{<<"$hint">> => Hint}
    end.

%% @doc 构建查询选项
-spec build_query_options(map()) -> map().
build_query_options(Options) ->
    DefaultOptions = #{
        limit => maps:get(limit, Options, 0),
        skip => maps:get(skip, Options, 0),
        sort => maps:get(sort, Options, #{}),
        projection => maps:get(projection, Options, #{})
    },

    % 过滤掉空值
    maps:filter(
        fun(_, Value) ->
            case Value of
                #{} -> false;
                0 -> false;
                _ -> true
            end
        end,
        DefaultOptions
    ).

%% @doc 执行游标查询
-spec execute_cursor_find(pid(), binary(), map(), map(), map()) -> {ok, [map()]} | {error, term()}.
execute_cursor_find(Pid, Collection, Filter, QueryOptions, Options) ->
    try
        Command = #{
            <<"find">> => Collection,
            <<"filter">> => Filter
        },

        % 添加查询选项
        CommandWithOptions = maps:merge(Command, QueryOptions),

        case mc_worker_api:command(Pid, CommandWithOptions) of
            {true, #{<<"cursor">> := #{<<"firstBatch">> := Results}}} ->
                {ok, Results};
            {true, #{<<"cursor">> := CursorInfo}} ->
                fetch_all_from_cursor(Pid, CursorInfo);
            {false, Error} ->
                {error, Error};
            Other ->
                {error, {unexpected_response, Other}}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "cursor_find_error",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 执行直接查询
-spec execute_direct_find(pid(), binary(), map(), map(), map()) -> {ok, [map()]} | {error, term()}.
execute_direct_find(Pid, Collection, Filter, QueryOptions, Options) ->
    try
        % 添加读偏好设置
        ReadPref = build_read_preference_from_options(Options),
        QueryOptionsWithReadPref = QueryOptions#{readopts => ReadPref},
        case mc_worker_api:find(Pid, Collection, Filter, QueryOptionsWithReadPref) of
            {ok, Cursor} ->
                Docs = mc_cursor:rest(Cursor),
                mc_cursor:close(Cursor),
                {ok, Docs};
            Error ->
                {error, Error}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "direct_find_error",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.



%% @doc 获取剩余批次数据
-spec fetch_remaining_batches(pid(), integer(), [map()]) -> {ok, [map()]} | {error, term()}.
fetch_remaining_batches(Pid, CursorId, AccumulatedDocs) ->
    try
        Command = #{
            <<"getMore">> => CursorId,
            <<"collection">> => <<"unknown">>  % 这里需要传入实际的集合名
        },

        case mc_worker_api:command(Pid, Command) of
            {true, #{<<"cursor">> := #{<<"nextBatch">> := NextBatch, <<"id">> := NextCursorId}}} ->
                NewAccumulated = AccumulatedDocs ++ NextBatch,
                case NextCursorId of
                    0 -> {ok, NewAccumulated};
                    _ -> fetch_remaining_batches(Pid, NextCursorId, NewAccumulated)
                end;
            {false, Error} ->
                {error, Error};
            Other ->
                {error, {unexpected_response, Other}}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "fetch_remaining_error",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.



%% ============================================================================
%% 基础MongoDB操作API实现 - 兼容原生驱动
%% ============================================================================

%% @doc 执行MongoDB命令 - 兼容接口
-spec command(pid(), map()) -> {ok, map()} | {error, term()}.
command(Pid, Command) ->
    execute_command(Pid, Command).

%% @doc 执行MongoDB命令（带选项） - 兼容接口
-spec command(pid(), map(), map()) -> {ok, map()} | {error, term()}.
command(Pid, Command, Options) ->
    execute_command(Pid, Command, Options).

%% @doc 插入文档 - 兼容接口
-spec insert(pid(), binary(), [map()] | map()) -> ok | {error, term()}.
insert(Pid, Collection, Documents) when is_list(Documents) ->
    case smart_insert(Pid, Collection, Documents) of
        {ok, _} -> ok;
        {error, Error} -> {error, Error}
    end;
insert(Pid, Collection, Document) when is_map(Document) ->
    % 单个文档转换为列表
    case smart_insert(Pid, Collection, [Document]) of
        {ok, _} -> ok;
        {error, Error} -> {error, Error}
    end.

%% @doc 插入文档（带选项） - 兼容接口
-spec insert(pid(), binary(), [map()], map()) -> ok | {error, term()}.
insert(Pid, Collection, Documents, Options) ->
    case smart_insert(Pid, Collection, Documents, Options) of
        {ok, _} -> ok;
        {error, Error} -> {error, Error}
    end.

%% @doc 更新文档 - 兼容接口
-spec update(pid(), binary(), map(), map(), map()) -> {ok, map()} | {error, term()}.
update(Pid, Collection, Filter, Update, Options) ->
    smart_update(Pid, Collection, Filter, Update, Options).

%% @doc 更新文档（带额外选项） - 兼容接口
-spec update(pid(), binary(), map(), map(), map(), map()) -> {ok, map()} | {error, term()}.
update(Pid, Collection, Filter, Update, UpdateOptions, ApiOptions) ->
    MergedOptions = maps:merge(UpdateOptions, ApiOptions),
    smart_update(Pid, Collection, Filter, Update, MergedOptions).

%% @doc 查找文档 - 兼容接口（3参数版本）
-spec find(binary(), map(), map()) -> {ok, [map()]} | {error, term()}.
find(Collection, Filter, Options) ->
    case get_mongodb_connection() of
        {ok, Pid} ->
            smart_find(Pid, Collection, Filter, Options);
        {error, Reason} ->
            {error, Reason}
    end.

%% @doc 查找文档 - 兼容接口（4参数版本）
-spec find(pid(), binary(), map(), map()) -> {ok, [map()]} | {error, term()}.
find(Pid, Collection, Filter, Options) ->
    smart_find(Pid, Collection, Filter, Options).

%% @doc 查找文档（带投影） - 兼容接口
-spec find(pid(), binary(), map(), map(), map()) -> {ok, [map()]} | {error, term()}.
find(Pid, Collection, Filter, Projection, Options) ->
    MergedOptions = Options#{projection => Projection},
    smart_find(Pid, Collection, Filter, MergedOptions).

%% @doc 查找单个文档 - 兼容接口
-spec find_one(pid(), binary(), map()) -> {ok, map() | undefined} | {error, term()}.
find_one(Pid, Collection, Filter) ->
    find_one_safe(Pid, Collection, Filter).

%% @doc 查找单个文档（带选项） - 兼容接口
-spec find_one(pid(), binary(), map(), map()) -> {ok, map() | undefined} | {error, term()}.
find_one(Pid, Collection, Filter, Options) ->
    find_one_safe(Pid, Collection, Filter, Options).

%% @doc 删除文档 - 兼容接口
-spec delete(pid(), binary(), map()) -> {ok, map()} | {error, term()}.
delete(Pid, Collection, Filter) ->
    delete(Pid, Collection, Filter, #{}).

%% @doc 删除文档（带选项） - 兼容接口
-spec delete(pid(), binary(), map(), map()) -> {ok, map()} | {error, term()}.
delete(Pid, Collection, Filter, Options) ->
    try
        Command = #{
            <<"delete">> => Collection,
            <<"deletes">> => [#{
                <<"q">> => Filter,
                <<"limit">> => maps:get(limit, Options, 1)
            }]
        },

        case mc_worker_api:command(Pid, Command) of
            {true, Result} -> {ok, Result};
            {false, Error} -> {error, Error};
            Other -> {error, {unexpected_response, Other}}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "delete_error",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 删除多个文档 - 兼容接口
-spec delete_many(pid(), binary(), map()) -> {ok, map()} | {error, term()}.
delete_many(Pid, Collection, Filter) ->
    delete_many(Pid, Collection, Filter, #{}).

%% @doc 删除多个文档（带选项） - 兼容接口
-spec delete_many(pid(), binary(), map(), map()) -> {ok, map()} | {error, term()}.
delete_many(Pid, Collection, Filter, Options) ->
    MergedOptions = Options#{limit => 0},  % 0 表示删除所有匹配的文档
    delete(Pid, Collection, Filter, MergedOptions).

%% @doc 删除单个文档 - 兼容接口
-spec delete_one(binary(), map()) -> {ok, map()} | {error, term()}.
delete_one(Collection, Filter) ->
    delete_one(Collection, Filter, #{}).

%% @doc 删除单个文档（带选项） - 兼容接口
-spec delete_one(binary(), map(), map()) -> {ok, map()} | {error, term()}.
delete_one(Collection, Filter, Options) ->
    try
        % 获取MongoDB连接
        case get_mongodb_connection() of
            {ok, Pid} when is_pid(Pid) ->
                % 构建删除命令，limit设为1表示只删除一个文档
                Command = #{
                    <<"delete">> => Collection,
                    <<"deletes">> => [#{
                        <<"q">> => Filter,
                        <<"limit">> => 1  % 只删除一个匹配的文档
                    }]
                },

                % 执行删除
                case command(Pid, Command) of
                    % 处理标准格式
                    {ok, #{<<"n">> := DeletedCount}} ->
                        {ok, #{<<"deletedCount">> => DeletedCount}};
                    {ok, #{<<"deletedCount">> := DeletedCount}} ->
                        {ok, #{<<"deletedCount">> => DeletedCount}};
                    % 处理嵌套的 {ok, {ok, Result}} 格式
                    {ok, {ok, #{<<"n">> := DeletedCount}}} ->
                        {ok, #{<<"deletedCount">> => DeletedCount}};
                    {ok, {ok, #{<<"deletedCount">> := DeletedCount}}} ->
                        {ok, #{<<"deletedCount">> => DeletedCount}};
                    {ok, {ok, NestedResult}} when is_map(NestedResult) ->
                        ?SLOG(warning, #{
                            msg => "unexpected_nested_delete_one_result_format",
                            result => NestedResult,
                            collection => Collection,
                            filter => Filter
                        }),
                        {ok, #{<<"deletedCount">> => 0}};
                    {ok, Result} when is_map(Result) ->
                        ?SLOG(warning, #{
                            msg => "unexpected_delete_one_result_format",
                            result => Result,
                            collection => Collection,
                            filter => Filter
                        }),
                        {ok, #{<<"deletedCount">> => 0}};
                    {error, Reason} ->
                        ?SLOG(error, #{
                            msg => "delete_one_command_failed",
                            collection => Collection,
                            filter => Filter,
                            reason => Reason
                        }),
                        {error, Reason};
                    Other ->
                        ?SLOG(error, #{
                            msg => "unexpected_delete_one_response",
                            response => Other,
                            collection => Collection,
                            filter => Filter
                        }),
                        {error, {unexpected_response, Other}}
                end;
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_get_mongodb_connection_for_delete_one",
                    collection => Collection,
                    filter => Filter,
                    reason => Reason
                }),
                {error, {connection_error, Reason}}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "delete_one_operation_failed",
                collection => Collection,
                filter => Filter,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc Upsert操作 - 兼容接口
-spec upsert(pid(), binary(), map(), map()) -> {ok, map()} | {error, term()}.
upsert(Pid, Collection, Filter, Update) ->
    upsert(Pid, Collection, Filter, Update, #{}).

%% @doc Upsert操作（带选项） - 兼容接口
-spec upsert(pid(), binary(), map(), map(), map()) -> {ok, map()} | {error, term()}.
upsert(Pid, Collection, Filter, Update, Options) ->
    MergedOptions = Options#{upsert => true},
    smart_update(Pid, Collection, Filter, Update, MergedOptions).

%% @doc 替换文档 - 兼容接口
-spec replace(pid(), binary(), map(), map()) -> {ok, map()} | {error, term()}.
replace(Pid, Collection, Filter, Replacement) ->
    replace(Pid, Collection, Filter, Replacement, #{}).

%% @doc 替换文档（带选项） - 兼容接口
-spec replace(pid(), binary(), map(), map(), map()) -> {ok, map()} | {error, term()}.
replace(Pid, Collection, Filter, Replacement, Options) ->
    try
        Command = #{
            <<"update">> => Collection,
            <<"updates">> => [#{
                <<"q">> => Filter,
                <<"u">> => Replacement,
                <<"upsert">> => maps:get(upsert, Options, false),
                <<"multi">> => false
            }]
        },

        case mc_worker_api:command(Pid, Command) of
            {true, Result} -> {ok, Result};
            {false, Error} -> {error, Error};
            Other -> {error, {unexpected_response, Other}}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "replace_error",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% ============================================================================
%% 连接管理API实现
%% ============================================================================

%% @doc 使用选项连接MongoDB
%% 这是MongoDB连接的基础函数，使用指定的选项建立连接
%%
%% 功能说明：
%% 1. 使用mc_worker_api:connect/1建立MongoDB连接
%% 2. 处理连接成功、失败和异常情况
%% 3. 提供统一的错误处理和日志记录
%%
%% 参数说明：
%% - Options: 连接选项列表，包含主机、端口、认证信息等
%%   格式示例：[{host, "localhost"}, {port, 27017}, {database, "test"}]
%%
%% 返回值：
%% - {ok, pid()}: 连接成功，返回连接进程ID
%% - {error, term()}: 连接失败，返回错误信息
%%
%% Java等价实现：
%% public MongoClient connectWithOptions(MongoClientOptions options) {
%%     try {
%%         return MongoClients.create(options);
%%     } catch (Exception e) {
%%         logger.error("MongoDB connection failed", e);
%%         throw new ConnectionException(e);
%%     }
%% }
-spec connect_with_options(list()) -> {ok, pid()} | {error, term()}.
connect_with_options(Options) ->
    try
        %% 调用MongoDB驱动的连接API
        %% mc_worker_api:connect/1是MongoDB Erlang驱动的连接函数
        case mc_worker_api:connect(Options) of
            {ok, Pid} when is_pid(Pid) ->
                %% 连接成功，返回连接进程ID
                {ok, Pid};
            {error, Error} ->
                %% 连接失败，返回错误信息
                {error, Error};
            Other ->
                %% 意外的响应格式，包装成错误
                {error, {unexpected_response, Other}}
        end
    catch
        %% 捕获连接过程中的异常
        E:R:S ->
            %% 记录详细的错误日志，包含选项、异常类型、原因和堆栈跟踪
            ?SLOG(error, #{
                msg => "mongodb_connect_error",
                options => Options,          % 连接选项（可能包含敏感信息，生产环境需要过滤）
                error => E,                  % 异常类型
                reason => R,                 % 错误原因
                stacktrace => S              % 堆栈跟踪
            }),
            %% 返回标准化的错误格式
            {error, {E, R}}
    end.

%% @doc 使用选项连接MongoDB（包装版本，处理事件转换）
-spec connect_with_event_wrapper(list()) -> {ok, pid()} | {error, term()}.
connect_with_event_wrapper(Options) ->
    try
        ?SLOG(info, #{
            msg => "using_mongodb_event_wrapper_connection",
            options_count => length(Options)
        }),

        % 使用原始连接方法建立连接
        case connect_with_options(Options) of
            {ok, Pid} when is_pid(Pid) ->
                ?SLOG(info, #{
                    msg => "mongodb_connection_with_event_wrapper_successful",
                    connection_pid => Pid
                }),

                % 连接成功后立即触发数据恢复操作
                % 这样确保在EMQX重启后能够恢复持久化数据
                ?SLOG(info, #{
                    msg => "mongodb_connected_event_received",
                    connection_pid => Pid,
                    triggering_data_restoration => true
                }),

                % 异步触发数据恢复操作，避免阻塞连接建立过程
                spawn(fun() ->
                    % 稍微延迟以确保连接完全稳定
                    timer:sleep(1000),
                    trigger_data_restoration_on_connection()
                end),

                {ok, Pid};
            {error, Error} ->
                ?SLOG(error, #{
                    msg => "mongodb_connection_with_event_wrapper_failed",
                    error => Error
                }),
                {error, Error}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "mongodb_connect_with_event_wrapper_error",
                options => Options,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 监控MongoDB事件的简单进程
monitor_mongodb_events(ConnectionPid) ->
    ?SLOG(info, #{
        msg => "mongodb_event_monitor_started",
        connection_pid => ConnectionPid
    }),

    % 监控连接进程
    MonitorRef = monitor(process, ConnectionPid),

    receive
        {'DOWN', MonitorRef, process, ConnectionPid, Reason} ->
            case Reason of
                normal ->
                    ?SLOG(info, #{
                        msg => "mongodb_connection_process_exited_normally",
                        connection_pid => ConnectionPid
                    });
                _ ->
                    ?SLOG(warning, #{
                        msg => "mongodb_connection_process_exited_abnormally",
                        connection_pid => ConnectionPid,
                        reason => Reason
                    })
            end;
        Other ->
            ?SLOG(debug, #{
                msg => "mongodb_event_monitor_received_unknown_message",
                connection_pid => ConnectionPid,
                message => Other
            }),
            monitor_mongodb_events(ConnectionPid)
    after 60000 ->
        % 60秒超时，检查连接是否还活着
        case is_process_alive(ConnectionPid) of
            true ->
                monitor_mongodb_events(ConnectionPid);
            false ->
                ?SLOG(info, #{
                    msg => "mongodb_connection_process_dead_exiting_monitor",
                    connection_pid => ConnectionPid
                })
        end
    end.



%% @doc 连接包装进程循环
connection_wrapper_loop(RealConnectionPid) ->
    % 监控真实连接进程
    MonitorRef = monitor(process, RealConnectionPid),

    % 存储状态
    State = #{
        real_connection => RealConnectionPid,
        monitor_ref => MonitorRef,
        status => connected
    },

    % 开始循环
    connection_wrapper_loop_with_state(State).

%% @doc 连接包装进程循环（带状态）
connection_wrapper_loop_with_state(#{real_connection := RealConnectionPid,
                                    monitor_ref := MonitorRef,
                                    status := Status} = State) ->
    receive
        % 处理MongoDB连接事件
        {connected, _Data} = Event ->
            ?SLOG(info, #{
                msg => "mongodb_connected_event_received",
                connection_pid => RealConnectionPid,
                event => Event
            }),

            % 触发数据恢复操作（智能启动时机检测）
            spawn(fun() -> trigger_data_restoration_with_smart_timing() end),

            % 更新状态
            connection_wrapper_loop_with_state(State#{status => connected});

        % 处理MongoDB断开事件
        {disconnected, _Data} = Event ->
            ?SLOG(debug, #{
                msg => "mongodb_disconnected_event_received",
                connection_pid => RealConnectionPid,
                event => Event
            }),
            % 更新状态
            connection_wrapper_loop_with_state(State#{status => disconnected});

        % 处理进程退出事件
        {'DOWN', MonitorRef, process, RealConnectionPid, Reason} ->
            ?SLOG(info, #{
                msg => "mongodb_connection_process_exited",
                connection_pid => RealConnectionPid,
                reason => Reason
            }),
            % 连接进程退出，包装进程也应该退出
            exit(normal);

        % 处理查询请求 - 转发到真实连接
        {query, From, Query} ->
            Result = try
                % 转发查询到真实连接
                mc_worker_api:command(RealConnectionPid, Query)
            catch
                E:R:S ->
                    ?SLOG(error, #{
                        msg => "error_forwarding_query",
                        error => E,
                        reason => R,
                        stacktrace => S
                    }),
                    {error, {E, R}}
            end,
            % 回复查询结果
            From ! {query_result, Result},
            % 继续循环
            connection_wrapper_loop_with_state(State);

        % 处理其他未知事件
        UnknownEvent ->
            ?SLOG(debug, #{
                msg => "mongodb_wrapper_received_unknown_event",
                connection_pid => RealConnectionPid,
                event => UnknownEvent
            }),
            % 继续循环
            connection_wrapper_loop_with_state(State)
    after 30000 ->
        % 30秒超时，检查连接是否还活着
        case is_process_alive(RealConnectionPid) of
            true ->
                connection_wrapper_loop_with_state(State);
            false ->
                ?SLOG(info, #{
                    msg => "mongodb_connection_process_dead_exiting_wrapper",
                    connection_pid => RealConnectionPid
                }),
                exit(normal)
        end
    end.

%% @doc 异步检测MongoDB版本并更新状态
-spec detect_mongodb_version_async(pid()) -> ok.
detect_mongodb_version_async(Pid) ->
    try
        % 等待一段时间，确保连接已经完全建立
        timer:sleep(1000),

        % 执行版本检测
        Version = case command(Pid, #{<<"buildInfo">> => 1}, 5000) of
            {ok, #{<<"version">> := V}} when is_binary(V) ->
                V;
            {ok, #{<<"versionString">> := V}} when is_binary(V) ->
                V;
            {ok, _} ->
                ?SLOG(warning, #{msg => "mongodb_version_detection_unexpected_response"}),
                <<"unknown">>;
            {error, DetectionReason} ->
                ?SLOG(warning, #{
                    msg => "mongodb_version_detection_failed",
                    reason => DetectionReason
                }),
                <<"unknown">>
        end,

        % 记录检测到的版本并进行兼容性检查
        ?SLOG(info, #{
            msg => "mongodb_version_detected_async",
            version => Version,
            connection_pid => Pid
        }),

        % 执行版本兼容性检查
        case check_mongodb_version_compatibility(Version) of
            {ok, VersionInfo} ->
                ?SLOG(info, #{
                    msg => "mongodb_version_compatibility_check_passed",
                    version => Version,
                    version_info => VersionInfo
                });
            {warning, VersionInfo} ->
                ?SLOG(warning, #{
                    msg => "mongodb_version_compatibility_warning",
                    version => Version,
                    version_info => VersionInfo
                });
            {error, CompatibilityReason} ->
                ?SLOG(error, #{
                    msg => "mongodb_version_compatibility_check_failed",
                    version => Version,
                    reason => CompatibilityReason
                })
        end,

        % 尝试更新资源状态中的版本信息
        try
            % 获取资源ID
            ResourceId = persistent_term:get(?PLUGIN_MONGODB_RESOURCE_ID),

            % 如果资源ID与当前连接匹配，则更新状态
            case ResourceId =:= Pid of
                true ->
                    % 更新资源状态
                    emqx_resource:update_status(?PLUGIN_MONGODB_RESOURCE_ID, connected, #{
                        mongo_version => Version
                    }),
                    ?SLOG(info, #{
                        msg => "mongodb_version_updated_in_resource_state",
                        version => Version
                    });
                false ->
                    ?SLOG(warning, #{
                        msg => "mongodb_resource_id_mismatch_cannot_update_version",
                        current_pid => Pid,
                        resource_id => ResourceId
                    })
            end
        catch
            E:R:S ->
                ?SLOG(warning, #{
                    msg => "failed_to_update_mongodb_version_in_resource_state",
                    error => E,
                    reason => R,
                    stacktrace => S
                })
        end
    catch
        Class:Error:Stack ->
            ?SLOG(warning, #{
                msg => "mongodb_version_detection_exception",
                class => Class,
                error => Error,
                stack => Stack
            })
    end,
    ok.

%% @doc 检查MongoDB版本兼容性
%% 支持MongoDB 4.x及以上版本，提供详细的兼容性信息
-spec check_mongodb_version_compatibility(binary()) ->
    {ok, map()} | {warning, map()} | {error, term()}.
check_mongodb_version_compatibility(Version) when is_binary(Version) ->
    try
        case parse_mongodb_version(Version) of
            {ok, {Major, Minor, Patch}} ->
                check_version_support(Major, Minor, Patch, Version);
            {error, Reason} ->
                {error, {version_parse_failed, Reason}}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "mongodb_version_compatibility_check_exception",
                version => Version,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {compatibility_check_failed, {E, R}}}
    end;
check_mongodb_version_compatibility(Version) ->
    {error, {invalid_version_format, Version}}.

%% @doc 解析MongoDB版本字符串
%% 支持格式：4.4.15, 5.0.9, 6.0.1等
-spec parse_mongodb_version(binary()) -> {ok, {integer(), integer(), integer()}} | {error, term()}.
parse_mongodb_version(Version) when is_binary(Version) ->
    try
        % 移除可能的前缀和后缀
        CleanVersion = re:replace(Version, "^v?([0-9]+\\.[0-9]+\\.[0-9]+).*", "\\1",
                                 [{return, binary}, global]),

        % 分割版本号
        case binary:split(CleanVersion, <<".">>, [global]) of
            [MajorBin, MinorBin, PatchBin] ->
                try
                    Major = binary_to_integer(MajorBin),
                    Minor = binary_to_integer(MinorBin),
                    Patch = binary_to_integer(PatchBin),
                    {ok, {Major, Minor, Patch}}
                catch
                    _:_ ->
                        {error, {invalid_version_numbers, CleanVersion}}
                end;
            [MajorBin, MinorBin] ->
                % 处理只有主版本和次版本的情况
                try
                    Major = binary_to_integer(MajorBin),
                    Minor = binary_to_integer(MinorBin),
                    {ok, {Major, Minor, 0}}
                catch
                    _:_ ->
                        {error, {invalid_version_numbers, CleanVersion}}
                end;
            _ ->
                {error, {invalid_version_format, CleanVersion}}
        end
    catch
        E:R ->
            {error, {parse_exception, {E, R}}}
    end.

%% @doc 检查具体版本的支持情况
-spec check_version_support(integer(), integer(), integer(), binary()) ->
    {ok, map()} | {warning, map()} | {error, term()}.
check_version_support(Major, Minor, Patch, OriginalVersion) ->
    VersionInfo = #{
        major => Major,
        minor => Minor,
        patch => Patch,
        original => OriginalVersion,
        parsed_at => erlang:system_time(millisecond)
    },

    case Major of
        M when M >= 7 ->
            % MongoDB 7.x+：完全支持，最新特性
            {ok, VersionInfo#{
                support_level => full,
                features => [op_msg_protocol, transactions, change_streams,
                           aggregation_pipeline, text_search, geospatial],
                recommendations => [<<"Latest MongoDB version with all features supported">>],
                protocol_version => latest
            }};
        6 ->
            % MongoDB 6.x：完全支持
            {ok, VersionInfo#{
                support_level => full,
                features => [op_msg_protocol, transactions, change_streams,
                           aggregation_pipeline, text_search, geospatial],
                recommendations => [<<"Excellent MongoDB version with full feature support">>],
                protocol_version => modern
            }};
        5 ->
            % MongoDB 5.x：完全支持
            case Minor of
                M when M >= 1 ->
                    % MongoDB 5.1+：移除了旧协议支持
                    {ok, VersionInfo#{
                        support_level => full,
                        features => [op_msg_protocol, transactions, change_streams,
                                   aggregation_pipeline, text_search, geospatial],
                        recommendations => [<<"Modern MongoDB version, legacy protocol removed">>],
                        protocol_version => op_msg_only,
                        notes => [<<"Legacy wire protocol support removed in 5.1+">>]
                    }};
                _ ->
                    % MongoDB 5.0：完全支持，仍支持旧协议
                    {ok, VersionInfo#{
                        support_level => full,
                        features => [op_msg_protocol, legacy_protocol, transactions,
                                   change_streams, aggregation_pipeline, text_search, geospatial],
                        recommendations => [<<"Stable MongoDB version with full compatibility">>],
                        protocol_version => hybrid
                    }}
            end;
        4 ->
            % MongoDB 4.x：基础支持，需要检查具体版本
            case Minor of
                M when M >= 4 ->
                    % MongoDB 4.4+：完全支持
                    {ok, VersionInfo#{
                        support_level => full,
                        features => [op_msg_protocol, legacy_protocol, transactions,
                                   change_streams, aggregation_pipeline, text_search, geospatial],
                        recommendations => [<<"Stable MongoDB 4.4+ version with good compatibility">>],
                        protocol_version => hybrid,
                        notes => [<<"Recommended minimum version for production use">>]
                    }};
                M when M >= 2 ->
                    % MongoDB 4.2-4.3：基础支持，有一些限制
                    {warning, VersionInfo#{
                        support_level => basic,
                        features => [op_msg_protocol, legacy_protocol, transactions,
                                   aggregation_pipeline, text_search, geospatial],
                        recommendations => [
                            <<"Consider upgrading to MongoDB 4.4+ for better stability">>,
                            <<"Some advanced features may have limitations">>
                        ],
                        protocol_version => hybrid,
                        limitations => [<<"Change streams may have limited functionality">>]
                    }};
                _ ->
                    % MongoDB 4.0-4.1：最低支持版本
                    {warning, VersionInfo#{
                        support_level => minimal,
                        features => [op_msg_protocol, legacy_protocol, basic_transactions,
                                   aggregation_pipeline, text_search, geospatial],
                        recommendations => [
                            <<"Strongly recommend upgrading to MongoDB 4.4+">>,
                            <<"This version has known limitations and performance issues">>
                        ],
                        protocol_version => hybrid,
                        limitations => [
                            <<"Limited transaction support">>,
                            <<"Change streams not fully supported">>,
                            <<"Performance may be suboptimal">>
                        ]
                    }}
            end;
        M when M < 4 ->
            % MongoDB 3.x及以下：不支持
            {error, {unsupported_version, VersionInfo#{
                support_level => none,
                reason => <<"MongoDB version below 4.0 is not supported">>,
                minimum_required => <<"4.0.0">>,
                recommendations => [
                    <<"Please upgrade to MongoDB 4.4 or higher">>,
                    <<"MongoDB 3.x has reached end-of-life">>
                ]
            }}};
        _ ->
            % 未知版本
            {warning, VersionInfo#{
                support_level => unknown,
                reason => <<"Unknown MongoDB major version">>,
                recommendations => [
                    <<"Please verify MongoDB version compatibility">>,
                    <<"Supported versions: 4.0+">>
                ]
            }}
    end.

%% @doc 安全断开MongoDB连接
-spec disconnect_safe(pid()) -> ok | {error, term()}.
disconnect_safe(Pid) ->
    try
        case is_process_alive(Pid) of
            true ->
                case mc_worker_api:disconnect(Pid) of
                    ok -> ok;
                    {error, Error} -> {error, Error};
                    Other -> {error, {unexpected_response, Other}}
                end;
            false ->
                ok  % 进程已经死亡，认为断开成功
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "mongodb_disconnect_error",
                pid => Pid,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% ============================================================================
%% 读写偏好辅助函数
%% ============================================================================

%% @doc 从配置选项构建读偏好设置
-spec build_read_preference_from_options(map()) -> map().
build_read_preference_from_options(Options) ->
    ReadPref = maps:get(read_preference, Options, primary),
    build_read_preference(ReadPref).

%% @doc 构建读偏好设置
-spec build_read_preference(atom() | binary()) -> map().
build_read_preference(primary) ->
    #{<<"mode">> => <<"primary">>};
build_read_preference(primaryPreferred) ->
    #{<<"mode">> => <<"primaryPreferred">>};
build_read_preference(secondary) ->
    #{<<"mode">> => <<"secondary">>};
build_read_preference(secondaryPreferred) ->
    #{<<"mode">> => <<"secondaryPreferred">>};
build_read_preference(nearest) ->
    #{<<"mode">> => <<"nearest">>};
build_read_preference(ReadPref) when is_binary(ReadPref) ->
    #{<<"mode">> => ReadPref};
build_read_preference(_) ->
    #{<<"mode">> => <<"primary">>}.

%% @doc 从配置选项构建写关注设置
-spec build_write_concern_from_options(map()) -> map().
build_write_concern_from_options(Options) ->
    WriteConcern = maps:get(write_concern, Options, majority),
    Timeout = maps:get(write_concern_timeout, Options, 10000),
    build_write_concern(WriteConcern, Timeout).

%% @doc 构建写关注设置
-spec build_write_concern(atom() | binary() | integer(), integer()) -> map().
build_write_concern(majority, Timeout) ->
    #{<<"w">> => <<"majority">>, <<"wtimeout">> => Timeout};
build_write_concern(N, Timeout) when is_integer(N) ->
    #{<<"w">> => N, <<"wtimeout">> => Timeout};
build_write_concern(Tag, Timeout) when is_binary(Tag) ->
    #{<<"w">> => Tag, <<"wtimeout">> => Timeout};
build_write_concern(Tag, Timeout) when is_atom(Tag) ->
    #{<<"w">> => atom_to_binary(Tag, utf8), <<"wtimeout">> => Timeout};
build_write_concern(_, Timeout) ->
    #{<<"w">> => 1, <<"wtimeout">> => Timeout}.

%%--------------------------------------------------------------------
%% 查询和删除文档API
%%--------------------------------------------------------------------

%% @doc 查找多个文档 - 简化版本
-spec find_documents(binary(), map()) -> {ok, list()} | {error, term()}.
find_documents(Collection, Filter) ->
    find_documents(Collection, Filter, #{}).

%% @doc 查找多个文档 - 完整版本
-spec find_documents(binary(), map(), map()) -> {ok, list()} | {error, term()}.
find_documents(Collection, Filter, Options) ->
    try
        % 获取MongoDB连接
        case get_mongodb_connection() of
            {ok, Pid} when is_pid(Pid) ->
                % 构建查询命令
                Command = #{
                    <<"find">> => Collection,
                    <<"filter">> => Filter
                },

                % 添加可选参数
                Command1 = case maps:get(projection, Options, #{}) of
                    Proj when map_size(Proj) =:= 0 -> Command;
                    Proj -> Command#{<<"projection">> => Proj}
                end,

                Command2 = case maps:get(limit, Options, 0) of
                    0 -> Command1;
                    Limit -> Command1#{<<"limit">> => Limit}
                end,

                Command3 = case maps:get(skip, Options, 0) of
                    0 -> Command2;
                    Skip -> Command2#{<<"skip">> => Skip}
                end,

                Command4 = case maps:get(sort, Options, #{}) of
                    Sort when map_size(Sort) =:= 0 -> Command3;
                    Sort -> Command3#{<<"sort">> => Sort}
                end,

                % 执行查询
                case command(Pid, Command4) of
                    % 处理标准MongoDB响应格式
                    {ok, #{<<"cursor">> := #{<<"firstBatch">> := Docs}}} ->
                        {ok, Docs};
                    {ok, #{<<"cursor">> := #{<<"id">> := CursorId, <<"firstBatch">> := Docs}}} when CursorId =/= 0 ->
                        % 如果有游标，获取所有文档
                        AllDocs = get_all_cursor_documents(Pid, Collection, CursorId, Docs),
                        {ok, AllDocs};
                    % 处理嵌套的 {ok, {ok, Result}} 格式
                    {ok, {ok, #{<<"cursor">> := #{<<"firstBatch">> := Docs}}}} ->
                        {ok, Docs};
                    {ok, {ok, #{<<"cursor">> := #{<<"id">> := CursorId, <<"firstBatch">> := Docs}}}} when CursorId =/= 0 ->
                        % 如果有游标，获取所有文档
                        AllDocs = get_all_cursor_documents(Pid, Collection, CursorId, Docs),
                        {ok, AllDocs};
                    % 处理其他可能的响应格式
                    {ok, {ok, Other}} ->
                        ?SLOG(warning, #{
                            msg => "unexpected_nested_find_result_format",
                            result => Other,
                            collection => Collection,
                            filter => Filter
                        }),
                        % 尝试从结果中提取文档
                        case maps:get(<<"cursor">>, Other, undefined) of
                            #{<<"firstBatch">> := Docs} -> {ok, Docs};
                            _ -> {ok, []}
                        end;
                    {ok, Other} ->
                        ?SLOG(warning, #{
                            msg => "unexpected_find_result_format",
                            result => Other,
                            collection => Collection,
                            filter => Filter
                        }),
                        % 尝试从结果中提取文档
                        case maps:get(<<"cursor">>, Other, undefined) of
                            #{<<"firstBatch">> := Docs} -> {ok, Docs};
                            _ -> {ok, []}
                        end;
                    {error, Reason} ->
                        ?SLOG(error, #{
                            msg => "find_documents_failed",
                            collection => Collection,
                            filter => Filter,
                            reason => Reason
                        }),
                        {error, Reason}
                end;
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_get_mongodb_connection_for_find",
                    reason => Reason
                }),
                {error, no_connection}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "exception_in_find_documents",
                collection => Collection,
                filter => Filter,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 删除多个文档
-spec delete_many(binary(), map()) -> {ok, integer()} | {error, term()}.
delete_many(Collection, Filter) ->
    try
        % 获取MongoDB连接
        case get_mongodb_connection() of
            {ok, Pid} when is_pid(Pid) ->
                % 构建删除命令
                Command = #{
                    <<"delete">> => Collection,
                    <<"deletes">> => [
                        #{
                            <<"q">> => Filter,
                            <<"limit">> => 0  % 0表示删除所有匹配的文档
                        }
                    ]
                },

                % 执行删除
                case command(Pid, Command) of
                    % 处理标准格式
                    {ok, #{<<"n">> := DeletedCount}} ->
                        {ok, DeletedCount};
                    {ok, #{<<"deletedCount">> := DeletedCount}} ->
                        {ok, DeletedCount};
                    % 处理嵌套的 {ok, {ok, Result}} 格式
                    {ok, {ok, #{<<"n">> := DeletedCount}}} ->
                        {ok, DeletedCount};
                    {ok, {ok, #{<<"deletedCount">> := DeletedCount}}} ->
                        {ok, DeletedCount};
                    {ok, {ok, NestedResult}} when is_map(NestedResult) ->
                        ?SLOG(warning, #{
                            msg => "unexpected_nested_delete_result_format",
                            result => NestedResult,
                            collection => Collection,
                            filter => Filter
                        }),
                        % 安全地从嵌套结果中提取删除计数
                        DeletedCount = maps:get(<<"n">>, NestedResult,
                                      maps:get(<<"deletedCount">>, NestedResult, 0)),
                        {ok, DeletedCount};
                    {ok, Other} when is_map(Other) ->
                        ?SLOG(warning, #{
                            msg => "unexpected_delete_result_format",
                            result => Other,
                            collection => Collection,
                            filter => Filter
                        }),
                        % 安全地从结果中提取删除计数
                        DeletedCount = maps:get(<<"n">>, Other,
                                      maps:get(<<"deletedCount">>, Other, 0)),
                        {ok, DeletedCount};
                    {ok, Other} ->
                        ?SLOG(warning, #{
                            msg => "unexpected_delete_result_type",
                            result => Other,
                            collection => Collection,
                            filter => Filter
                        }),
                        {ok, 0};
                    {error, Reason} ->
                        ?SLOG(error, #{
                            msg => "delete_many_failed",
                            collection => Collection,
                            filter => Filter,
                            reason => Reason
                        }),
                        {error, Reason}
                end;
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_get_mongodb_connection_for_delete",
                    reason => Reason
                }),
                {error, no_connection}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "exception_in_delete_many",
                collection => Collection,
                filter => Filter,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 获取游标的所有文档
-spec get_all_cursor_documents(pid(), binary(), integer(), list()) -> list().
get_all_cursor_documents(Pid, Collection, CursorId, AccDocs) ->
    try
        GetMoreCommand = #{
            <<"getMore">> => CursorId,
            <<"collection">> => Collection
        },

        case command(Pid, GetMoreCommand) of
            {ok, #{<<"cursor">> := #{<<"nextBatch">> := NextDocs, <<"id">> := NextCursorId}}} ->
                NewAccDocs = AccDocs ++ NextDocs,
                case NextCursorId of
                    0 -> NewAccDocs;  % 游标结束
                    _ -> get_all_cursor_documents(Pid, Collection, NextCursorId, NewAccDocs)
                end;
            {ok, _} ->
                AccDocs;  % 没有更多文档
            {error, Reason} ->
                ?SLOG(warning, #{
                    msg => "failed_to_get_more_cursor_documents",
                    cursor_id => CursorId,
                    reason => Reason
                }),
                AccDocs  % 返回已获取的文档
        end
    catch
        E:R:S ->
            ?SLOG(warning, #{
                msg => "exception_getting_cursor_documents",
                cursor_id => CursorId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            AccDocs  % 返回已获取的文档
    end.

%% @doc 获取MongoDB连接
-spec get_mongodb_connection() -> {ok, pid()} | {error, term()}.
get_mongodb_connection() ->
    try
        case persistent_term:get(?PLUGIN_MONGODB_RESOURCE_ID, undefined) of
            Pid when is_pid(Pid) ->
                case erlang:is_process_alive(Pid) of
                    true -> {ok, Pid};
                    false -> {error, connection_dead}
                end;
            undefined ->
                {error, no_resource_id};
            Other ->
                {error, {invalid_resource_id, Other}}
        end
    catch
        _:_ ->
            {error, resource_id_not_found}
    end.

%% 在MongoDB连接成功时触发数据恢复操作
%% @spec trigger_data_restoration_on_connection() -> ok
trigger_data_restoration_on_connection() ->
    ?SLOG(info, #{msg => "starting_data_restoration_on_mongodb_connection"}),

    try
        % 修复：直接读取配置文件，避免依赖application环境变量的设置时机
        ConfigResult = emqx_plugin_mongodb:read_config(),
        ?SLOG(info, #{msg => "config_read_result", result => ConfigResult}),
        Env = case ConfigResult of
            {error, Reason} ->
                ?SLOG(error, #{msg => "config_read_error", reason => Reason}),
                #{};
            Config when is_map(Config) ->
                ?SLOG(info, #{msg => "config_read_success", config_keys => maps:keys(Config)}),
                Config;
            Other ->
                ?SLOG(error, #{msg => "config_read_unexpected", result => Other}),
                #{}
        end,

        % 修复：使用多线程异步并行执行数据恢复操作
        % 这样可以大大提高恢复速度，避免串行等待
        % 每个恢复操作在独立的进程中异步执行
        ?SLOG(info, #{msg => "starting_parallel_data_restoration_operations"}),

        % 修复：真正的并行异步执行，移除所有人为延迟
        % 每个数据恢复操作同时开始，让它们自己处理依赖关系
        % 这样可以最大化并行性能，减少总恢复时间

        % 1. 智能恢复保留消息 - 等待EMQX消息系统就绪
        spawn(fun() ->
            try
                ?SLOG(info, #{msg => "retained_restoration_spawn_started"}),
                case maps:get(retained_persistence, Env, #{}) of
                    #{enabled := true} ->
                        ?SLOG(info, #{msg => "starting_intelligent_retained_message_restoration"}),
                        % 关键修复：等待EMQX消息系统完全就绪
                        wait_for_emqx_message_system_ready(),
                        ?SLOG(info, #{msg => "emqx_message_system_ready_starting_retained_restoration"}),
                        emqx_plugin_mongodb_retained:restore_retained_messages_from_mongodb(),
                        ?SLOG(info, #{msg => "retained_restoration_completed"});
                    _ ->
                        ?SLOG(debug, #{msg => "retained_persistence_disabled_skipping_restoration"})
                end
            catch
                E:R:S ->
                    ?SLOG(error, #{
                        msg => "retained_restoration_spawn_failed",
                        error => E,
                        reason => R,
                        stacktrace => S
                    })
            end
        end),

        % 2. 智能恢复遗嘱消息 - 等待EMQX完全启动并可以处理消息发布
        spawn(fun() ->
            try
                ?SLOG(info, #{msg => "will_restoration_spawn_started"}),

                % 修复：直接从配置中读取遗嘱消息设置，避免时机问题
                WillConfig = maps:get(will_persistence, Env, #{}),
                WillPersistenceEnabled = maps:get(enabled, WillConfig, false),
                ?SLOG(info, #{msg => "will_restoration_config_check", enabled => WillPersistenceEnabled, config => WillConfig}),

                case WillPersistenceEnabled of
                    true ->
                        ?SLOG(info, #{msg => "starting_intelligent_will_message_restoration"}),
                        % 关键修复：使用智能等待机制，确保EMQX完全启动并可以处理消息发布
                        % 不使用固定延迟，而是检测EMQX的消息发布能力
                        wait_for_emqx_message_system_ready(),
                        ?SLOG(info, #{msg => "emqx_message_system_ready_starting_will_restoration"}),
                        emqx_plugin_mongodb_will:handle_abnormal_shutdown_will_messages(),
                        ?SLOG(info, #{msg => "will_restoration_completed"});
                    _ ->
                        ?SLOG(info, #{msg => "will_persistence_disabled_skipping_restoration", enabled => WillPersistenceEnabled, config => WillConfig})
                end
            catch
                E:R:S ->
                    ?SLOG(error, #{
                        msg => "will_restoration_spawn_failed",
                        error => E,
                        reason => R,
                        stacktrace => S
                    })
            end
        end),

        % 3. 异步处理持久化会话 - 同时开始
        spawn(fun() ->
            wait_for_mongodb_ready(1000), % 统一等待最多1秒
            case maps:get(session_persistence, Env, #{}) of
                #{enabled := true} ->
                    ?SLOG(info, #{msg => "parallel_handling_persisted_sessions_after_restart"}),
                    catch emqx_plugin_mongodb_session:handle_persisted_sessions_after_restart();
                _ ->
                    ?SLOG(debug, #{msg => "session_persistence_disabled_skipping_handling"})
            end
        end),

        % 4. 智能恢复消息 - 等待EMQX消息系统就绪
        spawn(fun() ->
            try
                ?SLOG(info, #{msg => "message_restoration_spawn_started"}),
                case maps:get(message_persistence, Env, #{}) of
                    #{enabled := true} ->
                        ?SLOG(info, #{msg => "starting_intelligent_message_restoration"}),
                        % 关键修复：等待EMQX消息系统完全就绪
                        wait_for_emqx_message_system_ready(),
                        ?SLOG(info, #{msg => "emqx_message_system_ready_starting_message_restoration"}),
                        emqx_plugin_mongodb_message:restore_unacked_messages(),
                        ?SLOG(info, #{msg => "message_restoration_completed"});
                    _ ->
                        ?SLOG(debug, #{msg => "message_persistence_disabled_skipping_restoration"})
                end
            catch
                E:R:S ->
                    ?SLOG(error, #{
                        msg => "message_restoration_spawn_failed",
                        error => E,
                        reason => R,
                        stacktrace => S
                    })
            end
        end),

        % 5. 智能处理持久化订阅 - 等待EMQX消息系统就绪
        spawn(fun() ->
            try
                ?SLOG(info, #{msg => "subscription_restoration_spawn_started"}),
                case maps:get(subscription_persistence, Env, #{}) of
                    #{enabled := true} ->
                        ?SLOG(info, #{msg => "starting_intelligent_subscription_restoration"}),
                        % 关键修复：等待EMQX消息系统完全就绪
                        wait_for_emqx_message_system_ready(),
                        ?SLOG(info, #{msg => "emqx_message_system_ready_starting_subscription_restoration"}),
                        emqx_plugin_mongodb_subscription:handle_persisted_subscriptions_after_restart(),
                        ?SLOG(info, #{msg => "subscription_restoration_completed"});
                    _ ->
                        ?SLOG(debug, #{msg => "subscription_persistence_disabled_skipping_handling"})
                end
            catch
                E:R:S ->
                    ?SLOG(error, #{
                        msg => "subscription_restoration_spawn_failed",
                        error => E,
                        reason => R,
                        stacktrace => S
                    })
            end
        end),

        ?SLOG(info, #{msg => "data_restoration_triggered_successfully"}),
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_triggering_data_restoration",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 简化的MongoDB连接等待
%% 移除复杂的检查逻辑，使用简单的ping测试
-spec wait_for_mongodb_ready(integer()) -> ok.
wait_for_mongodb_ready(MaxWaitMs) ->
    wait_for_mongodb_ready(MaxWaitMs, 100). % 每100ms检查一次

wait_for_mongodb_ready(0, _CheckInterval) ->
    ?SLOG(warning, #{msg => "mongodb_ready_check_timeout"}),
    ok;
wait_for_mongodb_ready(MaxWaitMs, CheckInterval) when MaxWaitMs > 0 ->
    case simple_ping_mongodb() of
        true ->
            ?SLOG(info, #{msg => "mongodb_ready_check_passed", remaining_wait_ms => MaxWaitMs}),
            ok;
        false ->
            ?SLOG(debug, #{msg => "mongodb_not_ready_retrying", remaining_wait_ms => MaxWaitMs}),
            timer:sleep(CheckInterval),
            wait_for_mongodb_ready(MaxWaitMs - CheckInterval, CheckInterval)
    end.

%% @doc 简单的MongoDB ping测试
-spec simple_ping_mongodb() -> boolean().
simple_ping_mongodb() ->
    try
        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID, {call, ping}) of
            {ok, _} -> true;
            {async_return, {ok, _}} -> true;
            _ -> false
        end
    catch
        _:_ -> false
    end.

%% @doc 手动触发数据恢复操作
%% 这个函数用于测试和调试，可以手动触发数据恢复
%% 不依赖于连接事件，直接执行恢复逻辑
manual_trigger_data_restoration() ->
    ?SLOG(info, #{msg => "manual_data_restoration_triggered"}),

    %% 异步执行数据恢复，避免阻塞调用者
    spawn(fun() ->
        try
            %% 检查MongoDB连接是否可用
            case emqx_resource:health_check(?PLUGIN_MONGODB_RESOURCE_ID) of
                ok ->
                    ?SLOG(info, #{msg => "manual_trigger_mongodb_ready_starting_restoration"}),
                    trigger_data_restoration_on_connection();
                {ok, _} ->
                    ?SLOG(info, #{msg => "manual_trigger_mongodb_ready_starting_restoration"}),
                    trigger_data_restoration_on_connection();
                Error ->
                    ?SLOG(error, #{
                        msg => "manual_trigger_mongodb_not_ready",
                        health_check_result => Error
                    })
            end
        catch
            E:R:S ->
                ?SLOG(error, #{
                    msg => "error_in_manual_data_restoration_trigger",
                    error => E,
                    reason => R,
                    stacktrace => S
                })
        end
    end),

    ok.

%% @doc 智能启动时机检测 - 确保在最佳时机启动遗嘱消息恢复
%% 这是解决遗嘱消息BUG的关键函数
%%
%% 启动条件（必须全部满足）：
%% 1. EMQX完全启动并开始监听端口
%% 2. MongoDB连接成功并稳定
%% 3. EMQX消息发布系统完全就绪
%%
%% 设计原理（修正后）：
%% - 根据MQTT协议，遗嘱消息应该在异常断开时立即发布
%% - EMQX异常关闭时，所有客户端都被异常断开，遗嘱消息应该立即发布
%% - 问题不在于等待客户端重连，而在于确保EMQX能够正确处理消息发布
%% - 如果遗嘱消息是保留消息，客户端重连后仍能收到；如果不是保留消息，则按MQTT协议处理
-spec trigger_data_restoration_with_smart_timing() -> ok.
trigger_data_restoration_with_smart_timing() ->
    ?SLOG(info, #{
        msg => "starting_smart_timing_data_restoration",
        approach => "multi_condition_check_with_client_reconnection_window"
    }),

    try
        %% 第一步：验证EMQX是否完全启动并监听
        case check_emqx_fully_started() of
            true ->
                ?SLOG(info, #{msg => "emqx_fully_started_confirmed"});
            false ->
                ?SLOG(info, #{msg => "waiting_for_emqx_to_fully_start"}),
                wait_for_emqx_fully_started()
        end,

        %% 第二步：验证MongoDB连接是否稳定
        case check_mongodb_connection_stable() of
            true ->
                ?SLOG(info, #{msg => "mongodb_connection_stable_confirmed"});
            false ->
                ?SLOG(info, #{msg => "waiting_for_mongodb_connection_to_stabilize"}),
                wait_for_mongodb_stable()
        end,

        %% 第三步：确保EMQX消息系统完全就绪
        %% 修正：遗嘱消息应该立即发布，但需要确保EMQX能够正确处理消息发布
        case test_emqx_message_publish_capability() of
            true ->
                ?SLOG(info, #{msg => "emqx_message_publish_capability_confirmed"});
            false ->
                ?SLOG(info, #{msg => "waiting_for_emqx_message_publish_capability"}),
                wait_for_emqx_message_system_ready()
        end,

        %% 第四步：最终确认并启动数据恢复
        ?SLOG(info, #{
            msg => "all_conditions_met_starting_data_restoration",
            conditions => [
                "emqx_fully_started",
                "mongodb_connection_stable",
                "emqx_message_publish_capability_ready"
            ],
            note => "will_messages_will_be_published_immediately_as_per_mqtt_protocol"
        }),

        %% 启动数据恢复
        trigger_data_restoration_on_connection()

    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_in_smart_timing_data_restoration",
                error => E,
                reason => R,
                stacktrace => S
            })
    end,
    ok.

%% @doc 智能等待EMQX消息系统完全就绪
%% 这个函数确保EMQX的消息发布系统完全启动并可以处理消息发布
%% 特别是确保遗嘱消息可以被正确发布和路由
%% 修复：增加额外的延迟，确保客户端有时间重连和订阅
-spec wait_for_emqx_message_system_ready() -> ok.
wait_for_emqx_message_system_ready() ->
    ?SLOG(info, #{msg => "waiting_for_emqx_message_system_ready"}),

    % 第一阶段：等待EMQX消息系统基本就绪
    wait_for_emqx_message_system_ready(30, 1000), % 最多等待30秒，每秒检查一次

    % 第二阶段：额外等待，给客户端重连和订阅的时间
    % 这是关键修复：EMQX重启后，客户端需要时间重连并重新订阅
    ?SLOG(info, #{
        msg => "emqx_message_system_ready_waiting_for_client_reconnection_window",
        additional_wait_seconds => 5,
        reason => "allow_time_for_clients_to_reconnect_and_subscribe"
    }),
    timer:sleep(5000), % 等待5秒，给客户端重连时间

    ?SLOG(info, #{msg => "emqx_message_system_and_client_reconnection_window_ready"}),
    ok.

%% @doc 等待EMQX消息系统就绪的内部实现
-spec wait_for_emqx_message_system_ready(integer(), integer()) -> ok.
wait_for_emqx_message_system_ready(0, _Interval) ->
    ?SLOG(warning, #{
        msg => "emqx_message_system_ready_timeout",
        note => "proceeding_anyway_after_30_seconds"
    }),
    ok;
wait_for_emqx_message_system_ready(Retries, Interval) ->
    case test_emqx_message_publish_capability() of
        true ->
            ?SLOG(info, #{
                msg => "emqx_message_system_ready_confirmed",
                retries_left => Retries
            }),
            ok;
        false ->
            ?SLOG(debug, #{
                msg => "emqx_message_system_not_ready_waiting",
                retries_left => Retries,
                interval_ms => Interval
            }),
            timer:sleep(Interval),
            wait_for_emqx_message_system_ready(Retries - 1, Interval)
    end.

%% @doc 测试EMQX消息发布能力
%% 通过发布一个测试消息来验证EMQX的消息系统是否就绪
-spec test_emqx_message_publish_capability() -> boolean().
test_emqx_message_publish_capability() ->
    try
        % 创建一个测试消息，使用系统主题避免干扰正常业务
        TestTopic = <<"$SYS/mongodb_plugin/test/", (integer_to_binary(erlang:system_time(millisecond)))/binary>>,
        TestPayload = <<"test_message_system_ready">>,
        TestMessage = emqx_message:make(<<"$system_test">>, 0, TestTopic, TestPayload),

        % 尝试发布测试消息
        case emqx:publish(TestMessage) of
            [] ->
                % 没有订阅者，但消息发布成功（这是正常的）
                ?SLOG(debug, #{
                    msg => "test_message_published_successfully_no_subscribers",
                    topic => TestTopic
                }),
                true;
            [_|_] ->
                % 有订阅者，消息发布成功
                ?SLOG(debug, #{
                    msg => "test_message_published_successfully_with_subscribers",
                    topic => TestTopic
                }),
                true;
            {error, Reason} ->
                % 发布失败，消息系统可能还未就绪
                ?SLOG(debug, #{
                    msg => "test_message_publish_failed",
                    topic => TestTopic,
                    reason => Reason
                }),
                false;
            Other ->
                % 其他返回值，假设发布成功
                ?SLOG(debug, #{
                    msg => "test_message_publish_other_result",
                    topic => TestTopic,
                    result => Other
                }),
                true
        end
    catch
        E:R:S ->
            ?SLOG(debug, #{
                msg => "test_message_publish_exception",
                error => E,
                reason => R,
                stacktrace => S
            }),
            false
    end.

%% @doc 检查EMQX是否完全启动
%% 验证EMQX不仅启动了，而且开始监听MQTT端口
-spec check_emqx_fully_started() -> boolean().
check_emqx_fully_started() ->
    try
        %% 检查EMQX监听器是否启动
        case emqx_listeners:list() of
            [] ->
                ?SLOG(debug, #{msg => "no_listeners_found_emqx_not_fully_started"}),
                false;
            Listeners when is_list(Listeners) ->
                %% 检查是否有MQTT监听器在运行
                MqttListeners = [L || L <- Listeners, is_mqtt_listener_running(L)],
                case MqttListeners of
                    [] ->
                        ?SLOG(debug, #{msg => "no_mqtt_listeners_running"}),
                        false;
                    _ ->
                        ?SLOG(debug, #{
                            msg => "mqtt_listeners_confirmed_running",
                            count => length(MqttListeners)
                        }),
                        true
                end;
            _ ->
                false
        end
    catch
        _:_ ->
            %% 如果无法检查监听器，使用备用方法
            check_emqx_started_fallback()
    end.

%% @doc 检查是否为运行中的MQTT监听器
is_mqtt_listener_running({_Id, #{proto := mqtt, running := true}}) -> true;
is_mqtt_listener_running({_Id, #{proto := 'mqtt-tcp', running := true}}) -> true;
is_mqtt_listener_running({_Id, #{proto := 'mqtt-ssl', running := true}}) -> true;
is_mqtt_listener_running({_Id, #{proto := 'mqtt-ws', running := true}}) -> true;
is_mqtt_listener_running({_Id, #{proto := 'mqtt-wss', running := true}}) -> true;
is_mqtt_listener_running(_) -> false.

%% @doc EMQX启动检查的备用方法
check_emqx_started_fallback() ->
    try
        %% 尝试检查EMQX应用是否运行
        case application:which_applications() of
            Apps when is_list(Apps) ->
                EmqxRunning = lists:keymember(emqx, 1, Apps),
                ?SLOG(debug, #{
                    msg => "emqx_application_check_fallback",
                    emqx_running => EmqxRunning
                }),
                EmqxRunning;
            _ ->
                false
        end
    catch
        _:_ ->
            false
    end.

%% @doc 等待EMQX完全启动
-spec wait_for_emqx_fully_started() -> ok.
wait_for_emqx_fully_started() ->
    wait_for_emqx_fully_started(30, 2000). % 最多等待60秒，每2秒检查一次

wait_for_emqx_fully_started(0, _Interval) ->
    ?SLOG(warning, #{
        msg => "emqx_fully_started_timeout",
        note => "proceeding_anyway_after_timeout"
    }),
    ok;
wait_for_emqx_fully_started(Retries, Interval) ->
    case check_emqx_fully_started() of
        true ->
            ?SLOG(info, #{
                msg => "emqx_fully_started_confirmed",
                retries_left => Retries
            }),
            ok;
        false ->
            ?SLOG(debug, #{
                msg => "emqx_not_fully_started_waiting",
                retries_left => Retries,
                interval_ms => Interval
            }),
            timer:sleep(Interval),
            wait_for_emqx_fully_started(Retries - 1, Interval)
    end.

%% @doc 检查MongoDB连接是否稳定
-spec check_mongodb_connection_stable() -> boolean().
check_mongodb_connection_stable() ->
    try
        case emqx_resource:health_check(?PLUGIN_MONGODB_RESOURCE_ID) of
            ok ->
                %% 进行一次简单的ping测试确认连接稳定
                test_mongodb_ping();
            {ok, _} ->
                test_mongodb_ping();
            _ ->
                false
        end
    catch
        _:_ -> false
    end.

%% @doc 测试MongoDB ping
test_mongodb_ping() ->
    try
        case emqx_resource:simple_sync_query(?PLUGIN_MONGODB_RESOURCE_ID, {command, #{<<"ping">> => 1}}) of
            {ok, #{<<"ok">> := 1.0}} ->
                ?SLOG(debug, #{msg => "mongodb_ping_successful"}),
                true;
            _ ->
                ?SLOG(debug, #{msg => "mongodb_ping_failed"}),
                false
        end
    catch
        _:_ ->
            false
    end.

%% @doc 等待MongoDB连接稳定
-spec wait_for_mongodb_stable() -> ok.
wait_for_mongodb_stable() ->
    wait_for_mongodb_stable(15, 2000). % 最多等待30秒

wait_for_mongodb_stable(0, _Interval) ->
    ?SLOG(warning, #{
        msg => "mongodb_stable_timeout",
        note => "proceeding_anyway_after_timeout"
    }),
    ok;
wait_for_mongodb_stable(Retries, Interval) ->
    case check_mongodb_connection_stable() of
        true ->
            ?SLOG(info, #{
                msg => "mongodb_connection_stable_confirmed",
                retries_left => Retries
            }),
            ok;
        false ->
            ?SLOG(debug, #{
                msg => "mongodb_connection_not_stable_waiting",
                retries_left => Retries,
                interval_ms => Interval
            }),
            timer:sleep(Interval),
            wait_for_mongodb_stable(Retries - 1, Interval)
    end.


