%%%-------------------------------------------------------------------
%%% @doc MongoDB插件的消息处理流水线模块 - 企业级并行处理管道
%%% 这个模块实现了高性能的消息处理流水线，提供多阶段并行处理能力
%%%
%%% 功能概述：
%%% 1. 多阶段流水线处理 - 解析、转换、批处理、发送四个阶段
%%% 2. 并行工作进程池 - 每个阶段都有独立的工作进程池
%%% 3. 动态负载调整 - 根据系统负载动态调整工作进程数量
%%% 4. 性能监控统计 - 实时监控各阶段的处理时间和吞吐量
%%% 5. 队列缓冲管理 - 各阶段间使用队列缓冲，平衡处理速度
%%% 6. 容错和恢复 - 工作进程异常时自动重启和恢复
%%%
%%% 流水线架构：
%%% [消息输入] -> [解析阶段] -> [转换阶段] -> [批处理阶段] -> [发送阶段] -> [MongoDB]
%%%     |            |            |             |             |
%%%   队列缓冲    工作进程池    工作进程池    工作进程池    工作进程池
%%%
%%% Java等价概念：
%%% 类似于Spring Batch或Apache Kafka Streams的处理管道：
%%% @Component
%%% @Service
%%% public class MessageProcessingPipeline {
%%%     @Autowired private ExecutorService parseExecutor;
%%%     @Autowired private ExecutorService transformExecutor;
%%%     @Autowired private ExecutorService batchExecutor;
%%%     @Autowired private ExecutorService sendExecutor;
%%%
%%%     @Async
%%%     public CompletableFuture<Void> processMessage(Message message) {
%%%         return CompletableFuture
%%%             .supplyAsync(() -> parseStage(message), parseExecutor)
%%%             .thenApplyAsync(this::transformStage, transformExecutor)
%%%             .thenApplyAsync(this::batchStage, batchExecutor)
%%%             .thenApplyAsync(this::sendStage, sendExecutor);
%%%     }
%%% }
%%%
%%% 设计模式：
%%% - 管道模式（Pipeline Pattern）：多阶段顺序处理
%%% - 生产者-消费者模式：阶段间的队列缓冲
%%% - 工作池模式（Worker Pool Pattern）：并行工作进程
%%% - 观察者模式：性能监控和统计
%%% @end
%%%-------------------------------------------------------------------
-module(emqx_plugin_mongodb_pipeline).

%% 实现OTP gen_server行为
%% 类似于Java中实现Runnable或Callable接口
%% gen_server提供了标准的服务器进程模板
-behaviour(gen_server).

%% ============================================================================
%% 公共API函数 - 流水线管理和控制接口
%% 这些函数提供流水线的启动、停止和动态调整功能
%% 类似于Java中的Service层公共接口
%% ============================================================================
-export([
    start_link/0,       % 启动流水线服务器
                       % 功能：启动gen_server进程，初始化流水线
                       % Java等价：@PostConstruct public void startPipeline()

    adjust_workers/1,   % 动态调整工作进程数量
                       % 功能：根据负载比例调整各阶段的工作进程数量
                       % Java等价：public void adjustWorkers(double ratio)

    stop/0,            % 停止流水线服务器
                       % 功能：优雅关闭流水线，清理资源
                       % Java等价：@PreDestroy public void stopPipeline()

    integrate/0        % 集成到协调器
                       % 功能：将流水线模块注册到系统协调器
                       % Java等价：@PostConstruct public void integrate()
]).

%% ============================================================================
%% gen_server回调函数 - OTP标准服务器行为实现
%% 这些函数实现gen_server行为的标准回调
%% 类似于Java中的生命周期回调方法
%% ============================================================================
-export([
    init/1,             % 初始化回调
                       % 功能：初始化服务器状态，启动工作进程池
                       % Java等价：@PostConstruct public void init()

    handle_call/3,      % 同步调用处理
                       % 功能：处理同步请求，如状态查询、配置更新
                       % Java等价：public Response handleRequest(Request request)

    handle_cast/2,      % 异步消息处理
                       % 功能：处理异步消息，如工作进程状态更新
                       % Java等价：@EventListener public void handleEvent(Event event)

    handle_info/2,      % 系统消息处理
                       % 功能：处理系统消息，如进程监控、定时器
                       % Java等价：@Scheduled public void handleSystemEvent()

    terminate/2,        % 终止回调
                       % 功能：清理资源，保存状态
                       % Java等价：@PreDestroy public void cleanup()

    code_change/3       % 热代码升级回调
                       % 功能：支持运行时代码升级
                       % Java等价：类似于热部署机制
]).

%% ============================================================================
%% 内部工作函数 - 流水线各阶段的处理逻辑
%% 这些函数实现流水线各阶段的具体处理逻辑
%% 类似于Java中的private方法或工作线程的run方法
%% ============================================================================
-export([
    pipeline_worker/1,  % 通用流水线工作进程
                       % 功能：工作进程的主循环，处理队列中的任务
                       % Java等价：public void run() // Runnable接口实现

    stage_parse/1,      % 解析阶段处理
                       % 功能：解析MQTT消息，提取关键信息
                       % Java等价：public ParsedData parseStage(Message message)

    stage_transform/1,  % 转换阶段处理
                       % 功能：转换消息格式，准备数据库操作
                       % Java等价：public TransformedData transformStage(ParsedData data)

    stage_batch/1,      % 批处理阶段处理
                       % 功能：将消息组织成批次，优化数据库操作
                       % Java等价：public BatchData batchStage(TransformedData data)

    stage_send/1        % 发送阶段处理
                       % 功能：执行数据库操作，发送数据到MongoDB
                       % Java等价：public void sendStage(BatchData data)
]).

-include("emqx_plugin_mongodb.hrl").

%% ============================================================================
%% 数据结构定义 - 流水线状态和消息上下文
%% 这些记录定义了流水线的核心数据结构
%% 类似于Java中的实体类或数据传输对象（DTO）
%% ============================================================================

%% @doc 流水线状态记录
%% 这个记录保存流水线的完整运行状态，包括工作进程池和性能指标
%%
%% 功能说明：
%% 1. 管理各阶段的工作进程池
%% 2. 维护各阶段间的消息队列
%% 3. 收集和统计性能指标
%%
%% Java等价概念：
%% public class PipelineState {
%%     private List<Worker> parseWorkers;
%%     private List<Worker> transformWorkers;
%%     private List<Worker> batchWorkers;
%%     private List<Worker> sendWorkers;
%%     private Queue<Message> parseQueue;
%%     private Queue<Message> transformQueue;
%%     private Queue<Message> batchQueue;
%%     private Queue<Message> sendQueue;
%%     private PipelineMetrics metrics;
%% }
-record(pipeline_state, {
    parse_workers = [],         % 解析阶段工作进程列表
                               % 功能：存储解析阶段的所有工作进程PID
                               % Java等价：List<ParseWorker> parseWorkers

    transform_workers = [],     % 转换阶段工作进程列表
                               % 功能：存储转换阶段的所有工作进程PID
                               % Java等价：List<TransformWorker> transformWorkers

    batch_workers = [],         % 批处理阶段工作进程列表
                               % 功能：存储批处理阶段的所有工作进程PID
                               % Java等价：List<BatchWorker> batchWorkers

    send_workers = [],          % 发送阶段工作进程列表
                               % 功能：存储发送阶段的所有工作进程PID
                               % Java等价：List<SendWorker> sendWorkers

    parse_queue = queue:new(),  % 解析阶段消息队列
                               % 功能：缓冲待解析的消息
                               % Java等价：BlockingQueue<Message> parseQueue

    transform_queue = queue:new(), % 转换阶段消息队列
                                  % 功能：缓冲待转换的消息
                                  % Java等价：BlockingQueue<ParsedMessage> transformQueue

    batch_queue = queue:new(),  % 批处理阶段消息队列
                               % 功能：缓冲待批处理的消息
                               % Java等价：BlockingQueue<TransformedMessage> batchQueue

    send_queue = queue:new(),   % 发送阶段消息队列
                               % 功能：缓冲待发送的消息批次
                               % Java等价：BlockingQueue<BatchMessage> sendQueue

    metrics = #{                % 性能指标映射
                               % 功能：收集各阶段的性能统计数据
                               % Java等价：PipelineMetrics metrics
        parse_time => 0,        % 解析阶段总耗时（微秒）
        transform_time => 0,    % 转换阶段总耗时（微秒）
        batch_time => 0,        % 批处理阶段总耗时（微秒）
        send_time => 0,         % 发送阶段总耗时（微秒）
        total_time => 0,        % 总处理时间（微秒）
        messages_processed => 0 % 已处理消息总数
    }
}).

%% @doc 消息上下文记录
%% 这个记录跟踪单个消息在流水线中的完整处理过程
%%
%% 功能说明：
%% 1. 保存消息的原始数据和各阶段处理结果
%% 2. 记录各阶段的处理时间，用于性能分析
%% 3. 提供消息处理的完整上下文信息
%%
%% Java等价概念：
%% public class MessageContext {
%%     private Message originalMessage;
%%     private List<Query> queries;
%%     private Map<String, Object> parsedData;
%%     private Map<String, Object> transformedData;
%%     private String batchId;
%%     private long startTime;
%%     private long parseTime;
%%     private long transformTime;
%%     private long batchTime;
%%     private long sendTime;
%%     private long totalTime;
%% }
-record(message_context, {
    message :: #message{},      % 原始MQTT消息
                               % 功能：保存原始的MQTT消息对象
                               % Java等价：Message originalMessage

    querys :: list(),          % 查询列表
                               % 功能：存储消息相关的数据库查询
                               % Java等价：List<Query> queries

    parsed_data :: map(),      % 解析后的数据
                               % 功能：存储解析阶段的输出数据
                               % Java等价：Map<String, Object> parsedData

    transformed_data :: map(), % 转换后的数据
                               % 功能：存储转换阶段的输出数据
                               % Java等价：Map<String, Object> transformedData

    batch_id :: binary(),      % 批次ID
                               % 功能：标识消息所属的批次
                               % Java等价：String batchId

    start_time :: integer(),   % 开始处理时间戳（微秒）
                               % 功能：记录消息开始处理的时间
                               % Java等价：long startTime

    parse_time :: integer(),   % 解析阶段耗时（微秒）
                               % 功能：记录解析阶段的处理时间
                               % Java等价：long parseTime

    transform_time :: integer(), % 转换阶段耗时（微秒）
                                % 功能：记录转换阶段的处理时间
                                % Java等价：long transformTime

    batch_time :: integer(),   % 批处理阶段耗时（微秒）
                               % 功能：记录批处理阶段的处理时间
                               % Java等价：long batchTime

    send_time :: integer(),    % 发送阶段耗时（微秒）
                               % 功能：记录发送阶段的处理时间
                               % Java等价：long sendTime

    total_time :: integer()    % 总处理时间（微秒）
                               % 功能：记录消息的总处理时间
                               % Java等价：long totalTime
}).

%%%===================================================================
%%% API
%%%===================================================================

%% @doc 启动流水线处理服务器
start_link() ->
    gen_server:start_link({local, ?MODULE}, ?MODULE, [], []).

%% @doc 调整工作进程数量
adjust_workers(Ratio) ->
    % 验证比例在有效范围内
    ValidRatio = max(0.1, min(1.0, Ratio)),

    % 获取当前工作进程数量
    [{_, State}] = ets:lookup(?PIPELINE_REGISTRY, pipeline_state),

    % 调整各阶段工作进程数量
    adjust_stage_workers(parse, State#pipeline_state.parse_workers, ValidRatio),
    adjust_stage_workers(transform, State#pipeline_state.transform_workers, ValidRatio),
    adjust_stage_workers(batch, State#pipeline_state.batch_workers, ValidRatio),
    adjust_stage_workers(send, State#pipeline_state.send_workers, ValidRatio),

    ok.

%% @doc 调整特定阶段的工作进程数量
adjust_stage_workers(Stage, Workers, Ratio) ->
    % 计算目标工作进程数量
    CurrentCount = length(Workers),
    TargetCount = max(1, round(CurrentCount * Ratio)),

    % 如果需要减少工作进程
    case TargetCount < CurrentCount of
        true ->
            % 获取需要停止的工作进程
            {ToStop, ToKeep} = lists:split(CurrentCount - TargetCount, Workers),
            % 停止多余的工作进程
            [exit(Pid, normal) || Pid <- ToStop],
            % 更新工作进程列表
            ets:insert(?PIPELINE_REGISTRY, {Stage, ToKeep});
        false ->
            % 如果需要增加工作进程
            case TargetCount > CurrentCount of
                true ->
                    % 启动新的工作进程
                    NewWorkers = start_additional_workers(Stage, TargetCount - CurrentCount),
                    % 更新工作进程列表
                    ets:insert(?PIPELINE_REGISTRY, {Stage, Workers ++ NewWorkers});
                false ->
                    % 保持不变
                    ok
            end
    end.

%% @doc 启动额外的工作进程
start_additional_workers(Stage, Count) ->
    % 启动指定数量的新工作进程
    [spawn_link(?MODULE, get_stage_function(Stage), [Stage]) || _ <- lists:seq(1, Count)].

%% @doc 获取阶段对应的函数
get_stage_function(parse) -> stage_parse;
get_stage_function(transform) -> stage_transform;
get_stage_function(batch) -> stage_batch;
get_stage_function(send) -> stage_send.

%% @doc 集成到协调器
integrate() ->
    ?SLOG(info, #{msg => "integrating_pipeline_module"}),
    % 将此模块注册到协调器
    case erlang:function_exported(emqx_plugin_mongodb_coordinator, register_module, 2) of
        true ->
            emqx_plugin_mongodb_coordinator:register_module(?MODULE, #{
                priority => high,
                description => <<"Pipeline processing module">>,
                features => [parallel_processing, staged_execution, worker_pooling]
            });
        false ->
            ok
    end.

%% @doc 停止流水线处理服务器
stop() ->
    gen_server:call(?MODULE, stop).

%%%===================================================================
%%% gen_server callbacks
%%%===================================================================

%% @doc 初始化回调
init([]) ->
    % 创建ETS注册表
    ets:new(?PIPELINE_REGISTRY, [named_table, public, {read_concurrency, true}]),

    % 启动工作进程
    ParseWorkers = start_workers(?PARSE_WORKERS, parse),
    TransformWorkers = start_workers(?TRANSFORM_WORKERS, transform),
    BatchWorkers = start_workers(?BATCH_WORKERS, batch),
    SendWorkers = start_workers(?SEND_WORKERS, send),

    % 初始化状态
    State = #pipeline_state{
        parse_workers = ParseWorkers,
        transform_workers = TransformWorkers,
        batch_workers = BatchWorkers,
        send_workers = SendWorkers
    },

    % 启动性能监控
    erlang:send_after(60000, self(), collect_metrics),

    {ok, State}.

%% @doc 处理同步调用
handle_call(stop, _From, State) ->
    {stop, normal, ok, State};

handle_call(_Request, _From, State) ->
    {reply, {error, unknown_call}, State}.

%% @doc 处理异步调用
handle_cast({process, Context}, State) ->
    % 将消息添加到解析队列
    NewState = enqueue_message(Context, State),
    % 分发队列中的消息到工作进程
    DispatchedState = dispatch_messages(NewState),
    {noreply, DispatchedState};

handle_cast({stage_complete, parse, WorkerId, Context}, State) ->
    % 解析阶段完成，将消息移到转换队列
    NewState = dequeue_from_stage(parse, WorkerId, Context, State),
    {noreply, dispatch_messages(NewState)};

handle_cast({stage_complete, transform, WorkerId, Context}, State) ->
    % 转换阶段完成，将消息移到批处理队列
    NewState = dequeue_from_stage(transform, WorkerId, Context, State),
    {noreply, dispatch_messages(NewState)};

handle_cast({stage_complete, batch, WorkerId, Context}, State) ->
    % 批处理阶段完成，将消息移到发送队列
    NewState = dequeue_from_stage(batch, WorkerId, Context, State),
    {noreply, dispatch_messages(NewState)};

handle_cast({stage_complete, send, WorkerId, Context}, State) ->
    % 发送阶段完成，更新指标
    NewState = update_metrics(Context, State),
    % 标记工作进程为空闲
    FinalState = mark_worker_idle(send, WorkerId, NewState),
    {noreply, dispatch_messages(FinalState)};

handle_cast(_Msg, State) ->
    {noreply, State}.

%% @doc 处理消息
handle_info(collect_metrics, State) ->
    % 记录性能指标
    ?SLOG(info, #{
        msg => "pipeline_performance_metrics",
        metrics => State#pipeline_state.metrics
    }),

    % 重置计时器
    erlang:send_after(60000, self(), collect_metrics),

    {noreply, State};

handle_info(_Info, State) ->
    {noreply, State}.

%% @doc 终止回调
terminate(_Reason, State) ->
    % 停止所有工作进程
    stop_workers(State#pipeline_state.parse_workers),
    stop_workers(State#pipeline_state.transform_workers),
    stop_workers(State#pipeline_state.batch_workers),
    stop_workers(State#pipeline_state.send_workers),

    % 删除ETS表
    catch ets:delete(?PIPELINE_REGISTRY),

    ok.

%% @doc 代码更新回调
code_change(_OldVsn, State, _Extra) ->
    {ok, State}.

%%%===================================================================
%%% 内部函数
%%%===================================================================

%% @doc 启动工作进程
start_workers(Count, Type) ->
    [spawn_worker(Type) || _ <- lists:seq(1, Count)].

%% @doc 生成工作进程
spawn_worker(Type) ->
    WorkerId = erlang:unique_integer([positive]),
    Pid = erlang:spawn_link(?MODULE, pipeline_worker, [{self(), WorkerId, Type}]),
    ets:insert(?PIPELINE_REGISTRY, {{Type, WorkerId}, {Pid, idle}}),
    WorkerId.

%% @doc 停止工作进程
stop_workers(WorkerIds) ->
    [begin
         case ets:lookup(?PIPELINE_REGISTRY, {Type, Id}) of
             [{_, {Pid, _}}] ->
                 exit(Pid, normal),
                 ets:delete(?PIPELINE_REGISTRY, {Type, Id});
             _ ->
                 ok
         end
     end || Id <- WorkerIds, Type <- [parse, transform, batch, send]].

%% @doc 工作进程主循环
pipeline_worker({Parent, WorkerId, Type}) ->
    receive
        {process, Context} ->
            % 处理消息
            try
                % 调用相应的处理阶段
                NewContext = case Type of
                    parse -> stage_parse(Context);
                    transform -> stage_transform(Context);
                    batch -> stage_batch(Context);
                    send -> stage_send(Context)
                end,

                % 通知父进程阶段完成
                gen_server:cast(Parent, {stage_complete, Type, WorkerId, NewContext})
            catch
                C:E:S ->
                    ?SLOG(error, #{
                        msg => "pipeline_worker_error",
                        stage => Type,
                        worker_id => WorkerId,
                        error_class => C,
                        error => E,
                        stack => S
                    }),
                    % 即使出错也通知完成，以避免阻塞
                    gen_server:cast(Parent, {stage_complete, Type, WorkerId, Context})
            end,

            % 更新工作进程状态为空闲
            ets:insert(?PIPELINE_REGISTRY, {{Type, WorkerId}, {self(), idle}});

        stop ->
            exit(normal);

        _ ->
            ok
    end,
    % 继续循环
    pipeline_worker({Parent, WorkerId, Type}).

%% @doc 解析阶段
stage_parse(Context) ->
    % 记录开始时间
    StartTime = erlang:system_time(microsecond),

    % 从消息中提取数据
    Message = Context#message_context.message,
    ParsedData = #{
        topic => Message#message.topic,
        qos => Message#message.qos,
        from => Message#message.from,
        flags => Message#message.flags,
        headers => Message#message.headers,
        payload => Message#message.payload,
        timestamp => Message#message.timestamp
    },

    % 记录解析时间
    EndTime = erlang:system_time(microsecond),
    ParseTime = EndTime - StartTime,

    % 更新上下文
    Context#message_context{
        parsed_data = ParsedData,
        parse_time = ParseTime
    }.

%% @doc 转换阶段
stage_transform(Context) ->
    % 记录开始时间
    StartTime = erlang:system_time(microsecond),

    % 获取解析后的数据
    ParsedData = Context#message_context.parsed_data,

    % 转换数据为MongoDB文档格式
    TransformedData = #{
        <<"topic">> => maps:get(topic, ParsedData),
        <<"qos">> => maps:get(qos, ParsedData),
        <<"clientid">> =>
            case maps:get(from, ParsedData) of
                undefined -> null;
                {ClientId, _} -> ClientId
            end,
        <<"payload">> => maps:get(payload, ParsedData),
        <<"timestamp">> => maps:get(timestamp, ParsedData),
        <<"received_at">> => erlang:system_time(millisecond)
    },

    % 记录转换时间
    EndTime = erlang:system_time(microsecond),
    TransformTime = EndTime - StartTime,

    % 更新上下文
    Context#message_context{
        transformed_data = TransformedData,
        transform_time = TransformTime
    }.

%% @doc 批处理阶段
stage_batch(Context) ->
    % 记录开始时间
    StartTime = erlang:system_time(microsecond),

    % 生成批处理ID
    BatchId = generate_batch_id(),

    % 获取查询和转换后的数据
    Querys = Context#message_context.querys,
    TransformedData = Context#message_context.transformed_data,

    % 预分配批处理缓冲区
    BatchSize = get_batch_size(),

    % 将消息添加到批处理缓冲区
    [add_to_batch_buffer(Query, TransformedData, BatchId, BatchSize) || Query <- Querys],

    % 记录批处理时间
    EndTime = erlang:system_time(microsecond),
    BatchTime = EndTime - StartTime,

    % 更新上下文
    Context#message_context{
        batch_id = BatchId,
        batch_time = BatchTime
    }.

%% @doc 发送阶段
stage_send(Context) ->
    % 记录开始时间
    StartTime = erlang:system_time(microsecond),

    % 获取批处理ID
    BatchId = Context#message_context.batch_id,

    % 检查批处理是否准备好发送
    case check_batch_ready(BatchId) of
        true ->
            % 批处理准备好，发送到MongoDB
            send_batch(BatchId);
        false ->
            % 批处理未准备好，不执行操作
            ok
    end,

    % 记录发送时间
    EndTime = erlang:system_time(microsecond),
    SendTime = EndTime - StartTime,

    % 计算总处理时间
    TotalTime = EndTime - Context#message_context.start_time,

    % 更新上下文
    Context#message_context{
        send_time = SendTime,
        total_time = TotalTime
    }.

%% @doc 将消息添加到队列
enqueue_message(Context, State) ->
    % 添加到解析队列
    NewQueue = queue:in(Context, State#pipeline_state.parse_queue),
    State#pipeline_state{parse_queue = NewQueue}.

%% @doc 从阶段队列中移除消息，并添加到下一阶段队列
dequeue_from_stage(parse, WorkerId, Context, State) ->
    % 标记工作进程为空闲
    NewState = mark_worker_idle(parse, WorkerId, State),
    % 将消息添加到转换队列
    TransformQueue = queue:in(Context, NewState#pipeline_state.transform_queue),
    NewState#pipeline_state{transform_queue = TransformQueue};

dequeue_from_stage(transform, WorkerId, Context, State) ->
    % 标记工作进程为空闲
    NewState = mark_worker_idle(transform, WorkerId, State),
    % 将消息添加到批处理队列
    BatchQueue = queue:in(Context, NewState#pipeline_state.batch_queue),
    NewState#pipeline_state{batch_queue = BatchQueue};

dequeue_from_stage(batch, WorkerId, Context, State) ->
    % 标记工作进程为空闲
    NewState = mark_worker_idle(batch, WorkerId, State),
    % 将消息添加到发送队列
    SendQueue = queue:in(Context, NewState#pipeline_state.send_queue),
    NewState#pipeline_state{send_queue = SendQueue};

dequeue_from_stage(send, WorkerId, _Context, State) ->
    % 标记工作进程为空闲
    mark_worker_idle(send, WorkerId, State).

%% @doc 标记工作进程为空闲
mark_worker_idle(Type, WorkerId, State) ->
    case ets:lookup(?PIPELINE_REGISTRY, {Type, WorkerId}) of
        [{_, {Pid, _}}] ->
            ets:insert(?PIPELINE_REGISTRY, {{Type, WorkerId}, {Pid, idle}});
        _ ->
            ok
    end,
    State.

%% @doc 分发队列中的消息到工作进程
dispatch_messages(State) ->
    % 分发各阶段的消息
    State1 = dispatch_stage_messages(parse, State),
    State2 = dispatch_stage_messages(transform, State1),
    State3 = dispatch_stage_messages(batch, State2),
    dispatch_stage_messages(send, State3).

%% @doc 分发特定阶段的消息
dispatch_stage_messages(parse, State) ->
    dispatch_from_queue(
        parse,
        State#pipeline_state.parse_workers,
        State#pipeline_state.parse_queue,
        fun(Q) -> State#pipeline_state{parse_queue = Q} end
    );

dispatch_stage_messages(transform, State) ->
    dispatch_from_queue(
        transform,
        State#pipeline_state.transform_workers,
        State#pipeline_state.transform_queue,
        fun(Q) -> State#pipeline_state{transform_queue = Q} end
    );

dispatch_stage_messages(batch, State) ->
    dispatch_from_queue(
        batch,
        State#pipeline_state.batch_workers,
        State#pipeline_state.batch_queue,
        fun(Q) -> State#pipeline_state{batch_queue = Q} end
    );

dispatch_stage_messages(send, State) ->
    dispatch_from_queue(
        send,
        State#pipeline_state.send_workers,
        State#pipeline_state.send_queue,
        fun(Q) -> State#pipeline_state{send_queue = Q} end
    ).

%% @doc 从队列分发消息到空闲工作进程
dispatch_from_queue(Type, WorkerIds, Queue, UpdateFn) ->
    case queue:is_empty(Queue) of
        true ->
            % 队列为空，不执行操作
            UpdateFn(Queue);
        false ->
            % 查找空闲工作进程
            case find_idle_worker(Type, WorkerIds) of
                {ok, WorkerId, Pid} ->
                    % 获取队列中的下一个消息
                    {{value, Context}, NewQueue} = queue:out(Queue),

                    % 标记工作进程为忙碌
                    ets:insert(?PIPELINE_REGISTRY, {{Type, WorkerId}, {Pid, busy}}),

                    % 发送消息到工作进程
                    Pid ! {process, Context},

                    % 更新队列
                    UpdateFn(NewQueue);
                not_found ->
                    % 没有空闲工作进程，不执行操作
                    UpdateFn(Queue)
            end
    end.

%% @doc 查找空闲工作进程
find_idle_worker(Type, WorkerIds) ->
    find_idle_worker(Type, WorkerIds, not_found).

find_idle_worker(_Type, [], Result) ->
    Result;
find_idle_worker(Type, [WorkerId | Rest], _Result) ->
    case ets:lookup(?PIPELINE_REGISTRY, {Type, WorkerId}) of
        [{_, {Pid, idle}}] ->
            {ok, WorkerId, Pid};
        _ ->
            find_idle_worker(Type, Rest, not_found)
    end.

%% @doc 更新性能指标
update_metrics(Context, State) ->
    % 获取当前指标
    #{
        parse_time := ParseTimeTotal,
        transform_time := TransformTimeTotal,
        batch_time := BatchTimeTotal,
        send_time := SendTimeTotal,
        total_time := TotalTimeTotal,
        messages_processed := Count
    } = State#pipeline_state.metrics,

    % 安全获取时间值，处理可能的undefined值
    ParseTime = case Context#message_context.parse_time of
        undefined -> 0;
        PT when is_integer(PT) -> PT;
        _ -> 0
    end,

    TransformTime = case Context#message_context.transform_time of
        undefined -> 0;
        TT when is_integer(TT) -> TT;
        _ -> 0
    end,

    BatchTime = case Context#message_context.batch_time of
        undefined -> 0;
        BT when is_integer(BT) -> BT;
        _ -> 0
    end,

    SendTime = case Context#message_context.send_time of
        undefined -> 0;
        ST when is_integer(ST) -> ST;
        _ -> 0
    end,

    TotalTime = case Context#message_context.total_time of
        undefined -> 0;
        TOT when is_integer(TOT) -> TOT;
        _ -> 0
    end,

    % 更新指标
    NewMetrics = #{
        parse_time => ParseTimeTotal + ParseTime,
        transform_time => TransformTimeTotal + TransformTime,
        batch_time => BatchTimeTotal + BatchTime,
        send_time => SendTimeTotal + SendTime,
        total_time => TotalTimeTotal + TotalTime,
        messages_processed => Count + 1
    },

    % 更新状态
    State#pipeline_state{metrics = NewMetrics}.

%% @doc 生成批处理ID
generate_batch_id() ->
    Now = erlang:system_time(millisecond),
    Rand = erlang:phash2({self(), Now, make_ref()}),
    list_to_binary(io_lib:format("batch_~p_~p", [Now, Rand])).

%% @doc 获取配置的批处理大小
get_batch_size() ->
    % 从进程字典或模块默认值获取批处理大小
    get(max_batch_size, 1000).

%% @doc 将消息添加到批处理缓冲区
add_to_batch_buffer({Name, Collection}, Document, BatchId, BatchSize) ->
    % 使用ETS表作为批处理缓冲区
    BatchKey = {batch, Collection, BatchId},

    % 使用原子操作更新批处理缓冲区
    try
        % 尝试创建新批次
        ets:insert_new(?PIPELINE_REGISTRY, {BatchKey, {[Document], 1, BatchSize}})
    catch
        error:badarg ->
            % 批次已存在，更新它
            ets:update_counter(?PIPELINE_REGISTRY, BatchKey,
                               [{2, 1, BatchSize, BatchSize}])
    end.

%% @doc 检查批处理是否准备好发送
check_batch_ready(BatchId) ->
    % 查找所有与此批处理ID相关的条目
    MS = ets:fun2ms(fun({{batch, _, BId}, {_, Count, Max}}) when BId =:= BatchId ->
        {Count, Max}
    end),

    % 检查是否有任何批次已满
    case ets:select(?PIPELINE_REGISTRY, MS) of
        [] ->
            % 没有找到批次
            false;
        CountsAndMaxes ->
            % 检查是否有任何批次已满或超时
            lists:any(fun({Count, Max}) -> Count >= Max end, CountsAndMaxes)
    end.

%% @doc 发送批处理到MongoDB
send_batch(BatchId) ->
    % 查找所有与此批处理ID相关的条目
    MS = ets:fun2ms(fun({{batch, Collection, BId}, {Docs, _, _}} = Entry) when BId =:= BatchId ->
        {Collection, Docs, Entry}
    end),

    % 获取批次数据
    case ets:select(?PIPELINE_REGISTRY, MS) of
        [] ->
            % 没有找到批次
            ok;
        CollectionDocsEntries ->
            % 发送每个批次到MongoDB
            lists:foreach(
                fun({Collection, Docs, Element}) ->
                 % 使用MongoDB连接器发送批次
                 try
                     % 获取MongoDB连接
                     Pid = case persistent_term:get(?PLUGIN_MONGODB_RESOURCE_ID) of
                         undefined -> get(mongodb_connection_pid);
                            P when is_pid(P) -> P;
                            _ -> undefined
                     end,

                     % 发送批次
                     case is_pid(Pid) andalso is_process_alive(Pid) of
                         true ->
                                % 添加错误处理
                                try
                             % 使用零拷贝发送批次
                                    DocCount = length(Docs),
                                    % 确保文档列表不为空
                                    case DocCount > 0 of
                                        true ->
                                            emqx_plugin_mongodb_connector:zero_copy_batch_insert(Collection, Docs, DocCount);
                                        false ->
                                            ?SLOG(warning, #{
                                                msg => "empty_batch_skipped",
                                                batch_id => BatchId,
                                                collection => Collection
                                            })
                                    end
                                catch
                                    InnerC:InnerE:InnerS ->
                                        ?SLOG(error, #{
                                            msg => "batch_insert_failed",
                                            batch_id => BatchId,
                                            collection => Collection,
                                            docs_count => length(Docs),
                                            error_class => InnerC,
                                            error => InnerE,
                                            stack => InnerS
                                        })
                                end;
                         false ->
                                ?SLOG(error, #{
                                    msg => "mongodb_connection_not_available",
                                    batch_id => BatchId,
                                    collection => Collection
                                })
                     end
                 catch
                     C:E:S ->
                         ?SLOG(error, #{
                             msg => "send_batch_error",
                             batch_id => BatchId,
                             collection => Collection,
                             docs_count => length(Docs),
                             error_class => C,
                             error => E,
                             stack => S
                         })
                 end,

                    % 安全删除批次条目
                    try
                 ets:delete(?PIPELINE_REGISTRY, Element)
                    catch
                        DelErr:DelReason ->
                            ?SLOG(warning, #{
                                msg => "failed_to_delete_batch_entry",
                                batch_id => BatchId,
                                error => DelErr,
                                reason => DelReason
                            })
                    end
                end,
                CollectionDocsEntries
            )
    end.

%% @doc 从进程字典或默认值获取配置项
get(Key, Default) ->
    case erlang:get(Key) of
        undefined -> Default;
        Value -> Value
    end.
