%% @doc MongoDB插件的监督者模块。
%% Erlang/OTP中的模块（Module）类似于Java中的类（Class），它是代码组织的基本单元。
-module(emqx_plugin_mongodb_sup).

%% @behaviour supervisor
%% `-behaviour(supervisor).` 指令声明此模块实现 `supervisor` 行为（Behaviour）。
%% 行为类似于Java中的接口（Interface）或抽象类（Abstract Class），它定义了一组必须实现的回调函数。
%% `supervisor` 行为要求实现 `init/1` 回调函数。
-behaviour(supervisor).

%% @export([start_link/0]).
%% @export([init/1]).
%% `-export([...]).` 列出了此模块中可以被外部其他模块调用的函数。
%% `start_link/0` 用于启动监督者进程。
%% `init/1` 是 `supervisor` 行为的回调函数，用于初始化监督者。
-export([start_link/0]).
-export([init/1]).

%% @doc 启动监督者进程。
%% 此函数被调用以启动监督者。
%% `supervisor:start_link({local, ?MODULE}, ?MODULE, [])` 是OTP库提供的标准函数，用于启动一个新的监督者进程。
%% `{local, ?MODULE}` 表示将此监督者进程注册一个本地名称，名称与模块名相同 (`emqx_plugin_mongodb_sup`)。
%%   这样其他进程可以通过这个名称来引用此监督者，类似于Java中通过JNDI查找服务或Spring中通过bean ID获取bean实例。
%% `?MODULE` 作为第二个参数，指定了实现 `supervisor` 行为的回调模块（即本模块）。
%% `[]` 是传递给 `init/1` 函数的参数列表。
start_link() ->
    supervisor:start_link({local, ?MODULE}, ?MODULE, []).

%% @doc 初始化监督者。
%% 这是 `supervisor` 行为要求实现的回调函数，在 `supervisor:start_link/3` 调用时执行。
%% 它返回一个描述监督者如何管理其子进程的规范。
%% `Args` 参数是 `start_link` 传递过来的参数，这里是 `[]`。
init([]) ->
    %% `SupFlags` (监督标志) 定义了监督者的重启策略和子进程管理方式。
    %% `{one_for_all, 0, 1}` 是一个常见的重启策略：
    %%   - `one_for_all`: 如果一个子进程死掉，监督者会终止所有其他子进程，然后尝试重启所有子进程。
    %%     这适用于子进程之间有紧密依赖关系的情况。
    %%     其他策略还有 `one_for_one` (只重启死掉的子进程) 和 `rest_for_one` (重启死掉的子进程以及启动顺序在其后的所有子进程)。
    %%   - `0`: `MaxR` - 在 `MaxT` 时间内允许的最大重启次数。这里是0，表示不限制重启次数（或者说，这个参数在这里的语义是如果重启失败则放弃）。
    %%     更准确地说，如果子进程在 `MaxT` 秒内重启超过 `MaxR` 次，监督者会终止自己。
    %%   - `1`: `MaxT` - 时间窗口（秒）。
    %% `ChildSpecs` (子进程规范列表) 定义了此监督者要启动和管理的子进程。
    %%   这里是 `[]`，表示这个监督者目前不直接管理任何子进程。
    %%   通常，这里会包含一个或多个子进程的定义，例如 `gen_server`、其他 `supervisor` 或 `worker`。
    %%   每个子进程规范会指定如何启动子进程（模块、函数、参数）、重启策略（permanent, transient, temporary）等。
    %%   例如：`[{my_worker, {my_worker_module, start_link, []}, permanent, 5000, worker, [my_worker_module]}]`
    {ok, { {one_for_all, 0, 1}, []} }.
