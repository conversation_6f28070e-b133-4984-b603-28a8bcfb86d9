%% @doc EMQX MongoDB插件主模块 - 核心控制器
%%
%% 这个模块是EMQX MongoDB插件的主入口点和控制中心，类似于Java Spring Boot中的主应用类。
%%
%% 在Java中的等价概念：
%% @SpringBootApplication
%% @EnableConfigurationProperties
%% public class EmqxMongodbPluginApplication {
%%     @Autowired private ConfigurationManager configManager;
%%     @Autowired private List<PersistenceService> persistenceServices;
%%
%%     public void start() { ... }
%%     public void stop() { ... }
%% }
%%
%% 主要职责：
%% 1. 插件生命周期管理 - 类似于Spring的ApplicationLifecycle
%% 2. 配置文件读取和管理 - 类似于@ConfigurationProperties
%% 3. 各个持久化模块的协调 - 类似于@Service组件管理
%% 4. MQTT钩子函数注册 - 类似于事件监听器注册
%% 5. MongoDB连接资源管理 - 类似于数据源管理
%% @end

-module(emqx_plugin_mongodb).

%% 包含头文件 - 类似于Java的import语句
%% 在Java中相当于：
%% import com.emqx.core.*;
%% import com.emqx.hooks.*;
%% import static com.mongodb.plugin.Constants.*;
-include("emqx_plugin_mongodb.hrl").

%% 声明实现gen_server行为 - 类似于Java的implements接口
%% 在Java中相当于：
%% public class EmqxMongodbPlugin implements ApplicationLifecycle, ConfigurationManager
-behaviour(gen_server).

%% 常量定义 - 类似于Java中的public static final常量
%% 在Java中相当于：
%% public class Constants {
%%     public static final int DEFAULT_TIMEOUT_MS = 15000;
%%     public static final int MIN_RETRY_DELAY = 100;
%%     // ...
%% }

%% 默认超时时间(毫秒) - MongoDB操作的默认超时时间
%% 类似于Java的：public static final int DEFAULT_TIMEOUT_MS = 15000;
-define(DEFAULT_TIMEOUT_MS, 15000).

%% 重试机制相关常量 - 类似于Java的RetryPolicy配置
%% 最小重试延迟(毫秒) - 避免过于频繁的重试
%% 类似于Java的：public static final int MIN_RETRY_DELAY = 100;
-define(MIN_RETRY_DELAY, 100).

%% 最大重试延迟(毫秒) - 避免无限增长的延迟
%% 类似于Java的：public static final int MAX_RETRY_DELAY = 30000;
-define(MAX_RETRY_DELAY, 30000).

%% 重试延迟因子 - 指数退避算法的倍数因子
%% 类似于Java的：public static final double RETRY_FACTOR = 2.0;
-define(RETRY_FACTOR, 2).

%% 重试抖动因子(20%) - 避免雷群效应，添加随机性
%% 类似于Java的：public static final double RETRY_JITTER = 0.2;
-define(RETRY_JITTER, 0.2).

%% 批处理大小调整间隔(1分钟) - 动态调整批处理大小的时间间隔
%% 类似于Java的：public static final long BATCH_SIZE_ADJUST_INTERVAL = 60000L;
-define(BATCH_SIZE_ADJUST_INTERVAL, 60000).

%% 服务器名称 - gen_server的注册名称
%% 类似于Java的：public static final String SERVER_NAME = "EmqxMongodbPlugin";
-define(SERVER, ?MODULE).

%% 插件生命周期管理API - 类似于Java Spring的ApplicationLifecycle接口
%% 在Java中相当于：
%% public interface PluginLifecycle {
%%     void load();                    // 对应load/0
%%     void load(Config config);       // 对应load/1
%%     void unload();                  // 对应unload/0
%%     void reload();                  // 对应reload/0
%% }
%%
%% public interface ConfigurationManager {
%%     Config readConfig();            // 对应read_config/0
%%     void cleanConfig();             // 对应clean_config/0
%%     Config getConfig();             // 对应get_config/0
%% }
-export([
    load/0,         % 无参数加载插件 - 使用默认配置启动所有服务
    load/1,         % 带配置参数加载插件 - 使用指定配置启动服务
    unload/0,       % 卸载插件 - 停止所有服务并清理资源
    reload/0,       % 重新加载插件配置 - 热更新配置而不重启服务
    read_config/0,  % 读取插件配置文件 - 从hocon文件解析配置
    clean_config/0, % 清理配置缓存 - 清除内存中的配置数据
    get_config/0    % 获取当前配置 - 返回当前生效的配置对象
]).

%% MQTT消息钩子函数 - 类似于Java的事件监听器
%% 在Java中相当于：
%% @EventListener
%% public void onMessagePublish(MqttMessage message) { ... }
-export([
    on_message_publish/1    % MQTT消息发布钩子 - 当消息发布时被调用
]).

%% 事件消息构造函数 - 类似于Java的消息构造器
%% 在Java中相当于：
%% public class EventMessageBuilder {
%%     public EventMessage buildEventMessage(Object data) { ... }
%% }
-export([
    eventmsg_publish/1      % 构造事件消息 - 将数据转换为MongoDB存储格式
]).

% @doc 导出内部辅助函数 (供测试和内部使用)
% 这些函数可能未直接在当前模块中调用，但可能被其他模块使用或用于测试
-export([
    match_topic/2,
    check_connection_alive/1,
    init_mongo_type/1,
    ensure_binary/1,
    with_timeout/2,
    adaptive_retry/2,
    adaptive_retry/4
]).

% 导出gen_server回调函数
-export([
    start_link/0,
    init/1,
    handle_call/3,
    handle_cast/2,
    handle_info/2,
    terminate/2,
    code_change/3
]).

%% @doc 插件加载函数 (无参数版本) - 类似于Java的@PostConstruct方法
%%
%% 这是插件的主要初始化入口点，类似于Java Spring Boot中的：
%% @PostConstruct
%% public void initialize() {
%%     initializeCache();
%%     startServices();
%%     initializePersistenceModules();
%% }
%%
%% @return ok | {error, Reason}
load() ->
    %% 初始化ETS缓存表 - 类似于Java的ConcurrentHashMap初始化
    %% ETS (Erlang Term Storage) 是Erlang的内存数据库，类似于Java的：
    %% private static final ConcurrentHashMap<String, Object> cache = new ConcurrentHashMap<>();
    %%
    %% 检查缓存表是否已存在，避免重复创建
    case ets:info(?PLUGIN_MONGODB_TAB) of
        undefined ->
            %% 创建新的ETS表 - 相当于初始化缓存
            %% 在Java中相当于：
            %% if (cache == null) {
            %%     cache = new ConcurrentHashMap<String, Object>();
            %% }
            %%
            %% ETS表选项说明：
            %% - named_table: 可通过名称访问，类似于静态变量
            %% - public: 所有进程可访问，类似于public static
            %% - set: 键唯一的集合，类似于HashMap
            %% - {keypos, 1}: 使用元组第一个元素作为键
            %% - {read_concurrency, true}: 优化并发读取性能
            ets:new(?PLUGIN_MONGODB_TAB, [
                named_table, public, set, {keypos, 1}, {read_concurrency, true}
            ]);
        %% 表已存在，跳过创建 - 类似于单例模式的检查
        _ ->
            ok
    end,

    %% 初始化主题匹配缓存 - 类似于Java的缓存预热
    %% 相当于Java的：
    %% @PostConstruct
    %% private void initTopicMatchCache() { ... }
    init_topic_match_cache(),

    %% 启动批处理优化进程 - 类似于Java的后台服务启动
    %% 相当于Java的：
    %% @Async
    %% public void startBatchProcessor() { ... }
    %% 返回{ok, Pid}，Pid是进程标识符，类似于Java的Thread ID
    {ok, _} = start_link(),

    %% 初始化各个持久化模块 - 类似于Java的@Service组件初始化
    %% 相当于Java的：
    %% @Autowired private SessionPersistenceService sessionService;
    %% @Autowired private SubscriptionPersistenceService subscriptionService;
    %% // 在@PostConstruct中调用：
    %% sessionService.initialize();
    %% subscriptionService.initialize();

    %% 初始化会话持久化模块 - 管理MQTT客户端会话
    emqx_plugin_mongodb_session:init(),

    %% 初始化订阅持久化模块 - 管理MQTT主题订阅关系
    emqx_plugin_mongodb_subscription:init(),

    %% 初始化消息持久化模块 - 管理未确认的QoS 1/2消息
    emqx_plugin_mongodb_message:init(),

    %% 初始化遗嘱消息持久化模块 - 管理客户端遗嘱消息
    emqx_plugin_mongodb_will:init(),

    % 初始化保留消息持久化模块
    emqx_plugin_mongodb_retained:init(),

    % 调用 `load/1` 函数，参数是读取到的配置。
    load(read_config()).

%% @doc 启动gen_server进程
start_link() ->
    gen_server:start_link({local, ?SERVER}, ?MODULE, [], []).

%% @doc gen_server初始化回调
init([]) ->
    % 初始化批处理大小调整器
    init_batch_size_adjuster(),
    {ok, #{}}.

%% @doc 处理同步调用
handle_call(_Request, _From, State) ->
    {reply, ok, State}.

%% @doc 处理异步调用
handle_cast(_Msg, State) ->
    {noreply, State}.

%% @doc 终止回调
terminate(_Reason, _State) ->
    ok.

%% @doc 代码更新回调
code_change(_OldVsn, State, _Extra) ->
    {ok, State}.

% @doc 根据配置加载插件 (带参数版本)。
% 此函数接收解析后的配置 `#{connection := Connection, topics := Topics}`。
% `#{key := Value}` 是Erlang中的map语法。
% `Connection` 是MongoDB的连接配置，`Topics` 是需要处理的MQTT主题列表。
load(#{connection := Connection, topics := Topics} = Config) ->
    % 再次确保ETS表已创建，这是一种防御性编程。
    case ets:info(?PLUGIN_MONGODB_TAB) of
        undefined ->
            ets:new(?PLUGIN_MONGODB_TAB, [
                named_table, public, set, {keypos, 1}, {read_concurrency, true}
            ]);
        _ ->
            ok
    end,

    % 提取缓存配置
    CacheConfig = maps:get(cache, Connection, #{}),
    CacheEnabled = maps:get(enable, CacheConfig, true),

    % 如果缓存启用，初始化主题匹配缓存
    case CacheEnabled of
        true ->
            % 获取缓存大小和TTL
            TopicMatchSize = maps:get(topic_match_size, CacheConfig, 1000),
            TopicMatchTTL = maps:get(topic_match_ttl, CacheConfig, 60000),

            % 更新宏定义的值
            put(?TOPIC_MATCH_CACHE_SIZE, TopicMatchSize),
            put(?TOPIC_MATCH_CACHE_TTL, TopicMatchTTL),

            init_topic_match_cache();
        false ->
            ok
    end,

    % 启动MongoDB资源。EMQX中的资源 (Resource) 是一种可管理外部连接或服务的抽象。
    case start_resource(Connection) of
        % `_` 表示我们不关心成功时的具体返回值 (通常是资源ID)
        {ok, _} ->
            % 资源启动成功后，解析主题配置并存入ETS表。
            topic_parse(Topics),
            % 注册钩子函数。当 `message.publish` 事件发生时，调用本模块的 `on_message_publish` 函数。
            % `?MODULE` 是一个预处理器宏，代表当前模块名 (`emqx_plugin_mongodb`)。
            % `[]` 是传递给 `on_message_publish` 的额外参数 (这里没有)。
            % 这类似于Java中的事件监听器注册，例如 `eventBus.register(this)` 或 `button.addActionListener(...)`。
            hook('message.publish', {?MODULE, on_message_publish, []}),

            % 注册消息确认钩子 - 用于删除已确认的QoS 1/2消息
            hook('message.acked', {emqx_plugin_mongodb_message, on_message_acked, []}),

            % 加载会话持久化模块
            emqx_plugin_mongodb_session:load(Config),

            % 加载订阅持久化模块
            emqx_plugin_mongodb_subscription:load(Config),

            % 加载消息持久化模块
            emqx_plugin_mongodb_message:load(Config),

            % 加载遗嘱消息持久化模块
            emqx_plugin_mongodb_will:load(),

            % 加载Inflight消息持久化模块
            emqx_plugin_mongodb_inflight:load(Config),

            {ok, Config};
        {error, Reason} ->
            % `?SLOG` 是EMQX提供的日志宏，用于记录日志。
            % `error`: 日志级别。
            % `#{msg => "...", reason => Reason}`: 日志内容，是一个map。
            ?SLOG(error, #{msg => "failed_to_start_mongodb_resource", reason => Reason}),
            % 返回错误，插件加载失败。
            {error, Reason}
    end;
% `load/1` 的另一个子句，用于处理配置格式不匹配的情况。
load(_) ->
    {error, "config_error"}.

% @doc 消息发布回调函数。
% 当MQTT消息发布时，EMQX会调用此函数。
% `Message` 是一个 `#message{}` 记录，包含了消息的各种属性。
% `#message{topic = <<"$SYS/", _/binary>>}`: 这是一个模式匹配。
%   如果消息的主题 (topic) 是以 `"$SYS/"` 开头的二进制串 (binary)，则匹配此子句。
%   `<<...>>` 表示二进制数据。
%   `_binary` 表示匹配任意二进制后缀。
%   `$SYS` 主题通常用于EMQX的系统消息，插件一般不处理这些消息。
on_message_publish(Message = #message{topic = <<"$SYS/", _/binary>>}) ->
    % 直接返回原始消息，不进行处理
    {ok, Message};
% 处理非系统主题消息
on_message_publish(Message) ->
    % 提取主题
    Topic = extract_topic(Message),

    % 再次检查是否为系统消息（使用EMQX内置函数）
    case emqx_message:is_sys(Message) of
        true ->
            % 系统消息，直接返回，不进行处理
            {ok, Message};
        false ->
            % 检查是否为遗嘱消息并记录
            spawn(fun() ->
                try
                    case emqx_plugin_mongodb_will:is_will_message(Message) of
                        true ->
                            % 这是遗嘱消息，记录发布历史
                            ClientId = emqx_message:from(Message),
                            ClientInfo = #{clientid => ClientId},
                            emqx_plugin_mongodb_will:record_will_message_published(ClientInfo, Message);
                        false ->
                            % 不是遗嘱消息，继续正常处理
                            ok
                    end
                catch
                    _:_ -> ok  % 忽略遗嘱消息处理错误，不影响主流程
                end
            end),
            % 非系统消息，进行正常处理
            % 检查主题匹配
            case select(Message) of
                {true, Querys} when Querys =/= [] ->
                    % 主题匹配，尝试进行背压检查
                    try
                        % 确定消息优先级
                        Priority = determine_message_priority(Topic),
                        case emqx_plugin_mongodb_backpressure:check_pressure(Topic, Priority) of
                            {reject, _Actions} ->
                                % 背压拒绝，记录日志
                                ?SLOG(warning, #{
                                    msg => "message_rejected_due_to_backpressure",
                                    topic => Topic,
                                    priority => Priority
                                }),
                                % 不处理消息，但允许继续传递
                                {ok, Message};
                            {allow, Actions} ->
                                % 背压允许，处理消息
                                % 获取批处理参数
                                {BatchSize, _BatchTimeout} = get_batch_params(Topic, Actions),

                                % 使用背压调整的批处理大小
                                adjust_batch_size(BatchSize),

                                % 记录处理开始时间
                                StartTime = erlang:system_time(millisecond),

                                % 处理消息 (添加错误处理)
                                Result = try
                                    query(?MODULE:eventmsg_publish(Message), Querys)
                                catch
                                    Class:Reason:Stacktrace ->
                                        ?SLOG(error, #{
                                            msg => "query_execution_failed",
                                            error => Class,
                                            reason => Reason,
                                            stacktrace => Stacktrace,
                                            topic => Topic
                                        }),
                                        {error, {query_error, Reason}}
                                end,

                                % 计算处理时间
                                ProcessingTime = erlang:system_time(millisecond) - StartTime,

                                % 更新背压指标（安全处理）
                                try
                                    QueueLength = get_current_queue_length(),
                                    % 根据查询结果确定成功/失败
                                    IsSuccess = case Result of
                                        {error, _} -> false;
                                        _ -> true
                                    end,
                                    emqx_plugin_mongodb_backpressure:update_metrics(QueueLength, ProcessingTime, IsSuccess)
                                catch
                                    _:_ -> ok
                                end,

                                % 返回处理结果
                                {ok, Message}
                        end
                    catch
                        E:R:S ->
                            % 背压检查失败，记录错误但不阻止消息处理
                            ?SLOG(warning, #{
                                msg => "backpressure_check_failed",
                                error => E,
                                reason => R,
                                stacktrace => S,
                                topic => Topic
                            }),
                            % 继续处理消息
                            query(?MODULE:eventmsg_publish(Message), Querys),
                            {ok, Message}
                    end;
                false ->
                    % 消息不匹配任何已配置的主题过滤器，不做处理。
                    {ok, Message}
            end
    end.

% 获取当前队列长度
get_current_queue_length() ->
    % 尝试从批处理模块获取队列长度
    try
        case erlang:function_exported(emqx_plugin_mongodb_adaptive_batch, get_queue_length, 0) of
            true ->
                emqx_plugin_mongodb_adaptive_batch:get_queue_length();
            false ->
                % 如果函数不存在，使用进程邮箱长度作为替代指标
                {_, Len} = erlang:process_info(self(), message_queue_len),
                Len
        end
    catch
        _:_ ->
            % 出错时返回默认值
            0
    end.

% 调整批处理大小
adjust_batch_size(BatchSize) ->
    % 尝试调整批处理大小
    try
        case erlang:function_exported(emqx_plugin_mongodb_adaptive_batch, set_batch_size, 1) of
            true ->
                emqx_plugin_mongodb_adaptive_batch:set_batch_size(BatchSize);
            false ->
                % 如果函数不存在，使用进程字典存储
                put(batch_size, BatchSize)
        end
    catch
        _:_ ->
            % 出错时不做任何处理
            ok
    end.

% 获取批处理参数
get_batch_params(Topic, Actions) ->
    % 从背压动作中获取批处理大小调整因子
    BatchSizeFactor = case Actions of
        Actions when is_map(Actions) ->
            % Actions 是一个 map，需要从中提取 batch_size
            extract_batch_size_from_actions(Actions);
        Actions when is_list(Actions) ->
            % Actions 是一个 proplist
            proplists:get_value(batch_size, Actions, 1.0);
        _ ->
            1.0
    end,

    % 从自适应批处理模块获取基础批处理参数
    {BaseBatchSize, BatchTimeout} = case erlang:function_exported(emqx_plugin_mongodb_adaptive_batch, get_batch_params, 1) of
        true ->
            emqx_plugin_mongodb_adaptive_batch:get_batch_params(Topic);
        false ->
            % 默认值
            {?DEFAULT_BATCH_SIZE, ?DEFAULT_BATCH_TIMEOUT}
    end,

    % 应用背压调整因子
    AdjustedBatchSize = erlang:round(BaseBatchSize * BatchSizeFactor),

    % 确保批处理大小在合理范围内
    FinalBatchSize = max(?MIN_BATCH_SIZE, min(?MAX_BATCH_SIZE, AdjustedBatchSize)),

    {FinalBatchSize, BatchTimeout}.

% 从复杂的 Actions map 中提取 batch_size
extract_batch_size_from_actions(Actions) when is_map(Actions) ->
    % Actions 的结构类似：
    % #{critical => [...], high => [...], mild => [...], moderate => [...]}
    % 每个级别包含多个 map，其中可能有 #{batch_size => Value}

    % 尝试从各个级别中提取 batch_size
    Levels = [critical, high, moderate, mild],
    extract_batch_size_from_levels(Actions, Levels, 1.0).

% 从各个级别中提取 batch_size
extract_batch_size_from_levels(_Actions, [], DefaultValue) ->
    DefaultValue;
extract_batch_size_from_levels(Actions, [Level | RestLevels], DefaultValue) ->
    case maps:get(Level, Actions, []) of
        LevelActions when is_list(LevelActions) ->
            case extract_batch_size_from_level_actions(LevelActions) of
                undefined ->
                    extract_batch_size_from_levels(Actions, RestLevels, DefaultValue);
                BatchSize ->
                    BatchSize
            end;
        _ ->
            extract_batch_size_from_levels(Actions, RestLevels, DefaultValue)
    end.

% 从单个级别的 actions 列表中提取 batch_size
extract_batch_size_from_level_actions([]) ->
    undefined;
extract_batch_size_from_level_actions([Action | RestActions]) when is_map(Action) ->
    case maps:get(batch_size, Action, undefined) of
        undefined ->
            extract_batch_size_from_level_actions(RestActions);
        BatchSize ->
            BatchSize
    end;
extract_batch_size_from_level_actions([_ | RestActions]) ->
    extract_batch_size_from_level_actions(RestActions).

% 确定消息优先级
determine_message_priority(Topic) ->
    % 根据主题确定优先级
    % 可以基于主题前缀、正则匹配等规则
    case is_high_priority_topic(Topic) of
        true ->
            ?BACKPRESSURE_PRIORITY_HIGH;
        false ->
            case is_medium_priority_topic(Topic) of
                true ->
                    ?BACKPRESSURE_PRIORITY_MEDIUM;
                false ->
                    ?BACKPRESSURE_PRIORITY_LOW
            end
    end.

% 判断是否为高优先级主题
is_high_priority_topic(Topic) ->
    % 示例规则：以"critical/"开头的主题为高优先级
    binary:match(Topic, <<"critical/">>) =/= nomatch.

% 判断是否为中优先级主题
is_medium_priority_topic(Topic) ->
    % 示例规则：以"important/"开头的主题为中优先级
    binary:match(Topic, <<"important/">>) =/= nomatch.

% @doc 插件卸载函数。
% 当EMQX卸载此插件时调用。
unload() ->
    ?SLOG(info, #{msg => "unloading_mongodb_plugin"}),
    % 取消注册钩子函数
    unhook('message.publish', {?MODULE, on_message_publish}),

    % 取消注册消息确认钩子
    unhook('message.acked', {emqx_plugin_mongodb_message, on_message_acked}),

    % 卸载会话持久化模块
    emqx_plugin_mongodb_session:unload(),

    % 卸载订阅持久化模块
    emqx_plugin_mongodb_subscription:unload(),

    % 卸载消息持久化模块
    emqx_plugin_mongodb_message:unload(),

    % 卸载遗嘱消息持久化模块
    emqx_plugin_mongodb_will:unload(),

    % 卸载Inflight消息持久化模块
    emqx_plugin_mongodb_inflight:unload(),

    % 停止MongoDB连接管理器（确保资源完全清理）
    stop_connection_manager(),

    % 清理ETS表
    cleanup_ets_tables(),

    % 清理应用环境变量
    cleanup_application_env(),

    % 安全地卸载资源
    unload_resource(3),
    ok.

%% @doc 停止MongoDB连接管理器
%% 确保所有MongoDB连接和相关资源被正确清理
stop_connection_manager() ->
    try
        ?SLOG(info, #{msg => "stopping_mongodb_connection_manager"}),

        % 停止连接管理器进程
        case whereis(emqx_plugin_mongodb_connection) of
            undefined ->
                ?SLOG(debug, #{msg => "mongodb_connection_manager_not_running"});
            Pid when is_pid(Pid) ->
                case is_process_alive(Pid) of
                    true ->
                        ?SLOG(info, #{msg => "stopping_mongodb_connection_manager_process", pid => Pid}),
                        gen_server:stop(Pid, normal, 5000),
                        ?SLOG(info, #{msg => "mongodb_connection_manager_stopped"});
                    false ->
                        ?SLOG(debug, #{msg => "mongodb_connection_manager_already_stopped"})
                end
        end,

        % 额外的连接清理
        cleanup_remaining_connections(),

        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_stopping_mongodb_connection_manager",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 清理剩余的MongoDB连接
cleanup_remaining_connections() ->
    try
        % 清理可能残留的MongoDB连接进程
        case emqx_plugin_mongodb_api:get_connection() of
            {ok, Pid} when is_pid(Pid) ->
                case is_process_alive(Pid) of
                    true ->
                        ?SLOG(info, #{msg => "cleaning_up_remaining_mongodb_connection", pid => Pid}),
                        emqx_plugin_mongodb_api:disconnect_safe(Pid);
                    false ->
                        ok
                end;
            _ ->
                ok
        end
    catch
        _:_ ->
            % 忽略清理过程中的错误
            ok
    end.

%% @doc 卸载资源
%% @spec unload_resource(Retries :: non_neg_integer()) -> ok | {error, Reason :: term()}
unload_resource(0) ->
    ?SLOG(error, #{msg => "failed_to_unload_resource_after_retries"}),
    {error, max_retries_reached};
unload_resource(Retries) ->
    try
        % 先停止资源
        case emqx_resource:stop(?PLUGIN_MONGODB_RESOURCE_ID) of
            ok ->
                ?SLOG(info, #{msg => "resource_stopped_successfully"});
            {error, not_found} ->
                ?SLOG(info, #{msg => "resource_not_found_consider_already_removed"});
            {error, Reason} ->
                ?SLOG(warning, #{
                    msg => "failed_to_stop_resource",
                    reason => Reason,
                    retries_left => Retries
                })
        end,

        % 移除资源 - 使用更安全的方式
        try
            case emqx_resource:remove_local(?PLUGIN_MONGODB_RESOURCE_ID) of
                ok ->
                    ?SLOG(info, #{msg => "resource_removed_successfully"}),
                    ok;
                {error, not_found} ->
                    ?SLOG(info, #{msg => "resource_not_found_when_removing"}),
                    ok;
                {error, Reason2} ->
                    ?SLOG(warning, #{
                        msg => "failed_to_remove_resource",
                        reason => Reason2,
                        retries_left => Retries
                    }),
                    timer:sleep(1000),
                    unload_resource(Retries - 1)
            end
        catch
            error:function_clause ->
                % 处理函数参数不匹配的情况
                ?SLOG(warning, #{
                    msg => "resource_remove_function_clause_error_trying_alternative",
                    retries_left => Retries
                }),
                % 尝试直接删除资源而不使用remove_local
                try
                    emqx_resource:stop(?PLUGIN_MONGODB_RESOURCE_ID),
                    ?SLOG(info, #{msg => "resource_stopped_as_alternative"}),
                    ok
                catch
                    _:_ ->
                        ?SLOG(info, #{msg => "resource_cleanup_completed_with_fallback"}),
                        ok
                end;
            E2:R2 ->
                ?SLOG(error, #{
                    msg => "unexpected_error_removing_resource",
                    error => E2,
                    reason => R2,
                    retries_left => Retries
                }),
                timer:sleep(1000),
                unload_resource(Retries - 1)
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_unloading_resource",
                error => E,
                reason => R,
                stacktrace => S,
                retries_left => Retries
            }),
            timer:sleep(1000),
            unload_resource(Retries - 1)
    end.

%% @doc 清理ETS表
cleanup_ets_tables() ->
    try
        % 清理主配置表
        case ets:info(?PLUGIN_MONGODB_TAB) of
            undefined -> ok;
            _ ->
                ets:delete_all_objects(?PLUGIN_MONGODB_TAB),
                ?SLOG(info, #{msg => "ets_table_cleaned", table => ?PLUGIN_MONGODB_TAB})
        end,

        % 清理主题匹配缓存表
        case ets:info(?TOPIC_MATCH_CACHE_TAB) of
            undefined -> ok;
            _ ->
                ets:delete_all_objects(?TOPIC_MATCH_CACHE_TAB),
                ?SLOG(info, #{msg => "ets_table_cleaned", table => ?TOPIC_MATCH_CACHE_TAB})
        end,

        % 清理最近访问表
        case ets:info(?TOPIC_MATCH_CACHE_RECENT_TAB) of
            undefined -> ok;
            _ ->
                ets:delete_all_objects(?TOPIC_MATCH_CACHE_RECENT_TAB),
                ?SLOG(info, #{msg => "ets_table_cleaned", table => ?TOPIC_MATCH_CACHE_RECENT_TAB})
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_cleaning_ets_tables",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 清理应用环境变量
cleanup_application_env() ->
    % 使用异步方式清理环境变量，避免阻塞插件卸载过程
    spawn(fun() ->
        try
            % 清理各模块的环境变量，使用超时保护
            EnvVars = [
                session_persistence_enabled,
                subscription_persistence_enabled,
                message_persistence_enabled,
                will_persistence_enabled,
                retained_persistence_enabled,
                packet_id_persistence_enabled,
                config
            ],

            % 逐个清理环境变量，每个都有独立的超时保护
            lists:foreach(fun(EnvVar) ->
                try
                    % 使用带超时的调用
                    case catch gen_server:call(application_controller,
                                             {unset_env, emqx_plugin_mongodb, EnvVar, []},
                                             2000) of
                        ok ->
                            ?SLOG(debug, #{msg => "env_var_unset", var => EnvVar});
                        {'EXIT', {timeout, _}} ->
                            ?SLOG(warning, #{msg => "env_var_unset_timeout", var => EnvVar});
                        Other ->
                            ?SLOG(debug, #{msg => "env_var_unset_result", var => EnvVar, result => Other})
                    end
                catch
                    _:_ ->
                        ?SLOG(debug, #{msg => "env_var_unset_failed", var => EnvVar})
                end
            end, EnvVars),

            ?SLOG(info, #{msg => "application_environment_cleanup_completed"})
        catch
            E:R:S ->
                ?SLOG(error, #{
                    msg => "error_cleaning_application_env",
                    error => E,
                    reason => R,
                    stacktrace => S
                })
        end
    end),

    % 立即返回，不等待清理完成
    ?SLOG(info, #{msg => "application_environment_cleanup_started_async"}),
    ok.

% @doc 重新加载插件配置。
% 当通过 `emqx_ctl` 等工具触发插件重载时调用。
reload() ->
    ?SLOG(info, #{msg => "reloading_mongodb_plugin"}),
    Config = read_config(),
    % 安全地卸载资源
    unload_resource(3),
    % 等待确保资源完全卸载
    timer:sleep(1000),
    % 重新加载资源
    load(Config).

% @doc 清理配置文件
% 提供一个单独的函数用于手动清理配置文件
clean_config() ->
    ConfigFile = mongodb_config_file(),
    % 备份现有配置
    BackupFile = iolist_to_binary([ConfigFile, ".", integer_to_list(erlang:system_time(second)), ".bak"]),

    % 检查配置文件是否存在
    case filelib:is_regular(ConfigFile) of
        true ->
            % 创建备份
            case file:copy(ConfigFile, BackupFile) of
                {ok, _} ->
                    ?SLOG(info, #{
                        msg => "mongodb_plugin_config_backup_created",
                        config_file => ConfigFile,
                        backup_file => BackupFile
                    });
                {error, BackupError} ->
                    ?SLOG(warning, #{
                        msg => "failed_to_backup_mongodb_plugin_config",
                        config_file => ConfigFile,
                        reason => BackupError
                    })
            end,

            % 删除配置文件
            case file:delete(ConfigFile) of
                ok ->
                    ?SLOG(info, #{
                        msg => "mongodb_plugin_config_file_deleted",
                        config_file => ConfigFile
                    }),
                    ok;
                {error, DeleteError} ->
                    ?SLOG(error, #{
                        msg => "failed_to_delete_mongodb_plugin_config",
                        config_file => ConfigFile,
                        reason => DeleteError
                    }),
                    {error, DeleteError}
            end;
        false ->
            ?SLOG(info, #{
                msg => "mongodb_plugin_config_file_not_found",
                config_file => ConfigFile
            }),
            {error, config_file_not_found}
    end.

% @doc 读取配置文件。
% 返回解析后的配置map，或者 `{error, Reason}`。
read_config() ->
    % `hocon:load/1` 用于加载和解析HOCON格式的配置文件。
    % HOCON (Human-Optimized Config Object Notation) 是一种对JSON超集的配置文件格式。
    ConfigFile = mongodb_config_file(),
    case filelib:is_regular(ConfigFile) of
        true ->
            case hocon:load(ConfigFile) of
                % 文件成功加载和解析
                {ok, RawConf} ->
                    % `emqx_config:check_config/2` 使用预定义的schema (`emqx_plugin_mongodb_schema`) 来验证配置结构和类型。
                    % `emqx_plugin_mongodb_schema` 模块定义了配置的期望格式。
                    case emqx_config:check_config(emqx_plugin_mongodb_schema, RawConf) of
                        % `{_, #{plugin_mongodb := Conf}}` 模式匹配，提取插件相关的配置部分。
                        % `_` 忽略schema检查的详细结果，我们只关心是否成功并提取配置。
                        {_, #{plugin_mongodb := Conf}} ->
                            % 修复：移除频繁的配置加载日志，避免日志膨胀
                            % 配置加载是正常操作，不需要每次都记录详细日志
                            % 返回提取的配置
                            Conf;
                        % schema验证失败或配置结构错误
                        Error ->
                            ?SLOG(error, #{
                                msg => "bad_hocon_config_structure_or_schema_validation_failed",
                                file => ConfigFile,
                                error_details => Error
                            }),
                            {error, {bad_hocon_config, Error}}
                    end;
                % HOCON文件加载或解析失败
                {error, Error} ->
                    ?SLOG(error, #{
                        msg => "failed_to_load_hocon_file",
                        file => ConfigFile,
                        reason => Error
                    }),
                    {error, {failed_to_load_hocon, Error}}
            end;
        false ->
            % 配置文件不存在，创建默认配置
            ?SLOG(warning, #{
                msg => "mongodb_plugin_config_file_not_found_creating_default",
                file => ConfigFile
            }),
            % 在这里可以添加创建默认配置的逻辑
            % 现在简单返回错误
            {error, config_file_not_found}
    end.

% @doc 获取当前插件配置。
% 返回解析后的配置map，如果配置不存在则返回默认配置。
get_config() ->
    case read_config() of
        {error, _} ->
            % 如果读取配置失败，返回默认配置
            #{
                session_persistence => #{enabled => false},
                subscription_persistence => #{enabled => false},
                message_persistence => #{enabled => false},
                will_persistence => #{enabled => false},
                topics => []
            };
        Config when is_map(Config) ->
            Config;
        _ ->
            % 其他情况也返回默认配置
            #{
                session_persistence => #{enabled => false},
                subscription_persistence => #{enabled => false},
                message_persistence => #{enabled => false},
                will_persistence => #{enabled => false},
                topics => []
            }
    end.

% @doc 获取配置文件路径。
% 优先使用环境变量 `EMQX_PLUGIN_MONGODB_CONF` 指定的路径，否则使用默认路径。
mongodb_config_file() ->
    Env = os:getenv("EMQX_PLUGIN_MONGODB_CONF"),
    case Env of
        % `os:getenv` 在未设置时返回 `false`
        false -> "etc/emqx_plugin_mongodb.hocon";
        % 空字符串也视为默认
        "" -> "etc/emqx_plugin_mongodb.hocon";
        % 使用环境变量指定的路径
        Path -> Path
    end.

% @doc 启动MongoDB资源。
% `Connection` 是包含MongoDB连接参数的map。
% `#{health_check := #{interval := HealthCheckInterval}}` 从Connection map中提取健康检查间隔。
start_resource(Connection = #{health_check := #{interval := HealthCheckInterval}}) ->
    % 添加更多日志以跟踪启动流程
    ?SLOG(info, #{msg => "starting_mongodb_resource", health_check_interval => HealthCheckInterval}),

    % 资源ID
    ResId = ?PLUGIN_MONGODB_RESOURCE_ID,
    % 为资源创建度量指标 (metrics)
    _ = emqx_resource:create_metrics(ResId),

    % 使用超时保护创建资源
    try
        % 直接传递Connection，让connector模块处理工作选项
        % 关键修复：设置start_after_created => true确保资源创建后自动启动
        Result = emqx_resource:create_local(
            ResId,
            ?PLUGIN_MONGODB_RESOURCE_GROUP,
            emqx_plugin_mongodb_connector,
            Connection,
            #{
                health_check_interval => HealthCheckInterval,
                start_after_created => true,
                start_timeout => 30000  % 30秒启动超时
            }
        ),
        % 根据创建结果进行处理
        ?SLOG(info, #{msg => "mongodb_resource_creation_result", result => Result}),
        case start_resource_if_enabled(Result) of
            {ok, ResId} ->
                % 关键修复：显式启动资源，因为simple_async_internal_buffer模式不会自动启动
                ?SLOG(info, #{msg => "explicitly_starting_mongodb_resource", resource_id => ResId}),
                case emqx_resource:start(ResId) of
                    ok ->
                        ?SLOG(info, #{msg => "mongodb_resource_started_successfully", resource_id => ResId}),
                        {ok, ResId};
                    {error, StartError} ->
                        ?SLOG(error, #{msg => "failed_to_start_mongodb_resource", resource_id => ResId, error => StartError}),
                        {error, {resource_start_failed, StartError}}
                end;
            Error ->
                Error
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_creating_mongodb_resource",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {resource_creation_error, {E, R}}}
    end;

% 支持旧版本配置结构
start_resource(Connection = #{health_check_interval := HealthCheckInterval}) ->
    % 添加更多日志以跟踪启动流程
    ?SLOG(info, #{msg => "starting_mongodb_resource_legacy_config", health_check_interval => HealthCheckInterval}),

    % 资源ID
    ResId = ?PLUGIN_MONGODB_RESOURCE_ID,
    % 为资源创建度量指标 (metrics)
    _ = emqx_resource:create_metrics(ResId),

    % 使用超时保护创建资源
    try
        % 直接传递Connection，让connector模块处理工作选项
        % 关键修复：设置start_after_created => true确保资源创建后自动启动
        Result = emqx_resource:create_local(
            ResId,
            ?PLUGIN_MONGODB_RESOURCE_GROUP,
            emqx_plugin_mongodb_connector,
            Connection,
            #{
                health_check_interval => HealthCheckInterval,
                start_after_created => true,
                start_timeout => 30000  % 30秒启动超时
            }
        ),
        % 根据创建结果进行处理
        ?SLOG(info, #{msg => "mongodb_resource_creation_result_legacy", result => Result}),
        case start_resource_if_enabled(Result) of
            {ok, ResId} ->
                % 关键修复：显式启动资源，因为simple_async_internal_buffer模式不会自动启动
                ?SLOG(info, #{msg => "explicitly_starting_mongodb_resource_legacy", resource_id => ResId}),
                case emqx_resource:start(ResId) of
                    ok ->
                        ?SLOG(info, #{msg => "mongodb_resource_started_successfully_legacy", resource_id => ResId}),
                        {ok, ResId};
                    {error, StartError} ->
                        ?SLOG(error, #{msg => "failed_to_start_mongodb_resource_legacy", resource_id => ResId, error => StartError}),
                        {error, {resource_start_failed, StartError}}
                end;
            Error ->
                Error
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_creating_mongodb_resource_legacy",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {resource_creation_error, {E, R}}}
    end;

% 添加通用处理函数子句以适应其他配置结构
start_resource(Connection) ->
    % 从health_check配置或默认值中提取健康检查间隔
    HealthCheckInterval =
        case maps:get(health_check, Connection, undefined) of
            HC when is_map(HC) -> maps:get(interval, HC, 15000);
            _ -> 15000 % 默认15秒
        end,

    % 添加更多日志以跟踪启动流程
    ?SLOG(info, #{msg => "starting_mongodb_resource_generic_config", health_check_interval => HealthCheckInterval}),

    % 资源ID
    ResId = ?PLUGIN_MONGODB_RESOURCE_ID,
    % 为资源创建度量指标 (metrics)
    _ = emqx_resource:create_metrics(ResId),

    % 使用超时保护创建资源
    try
        % 直接传递Connection，让connector模块处理工作选项
        % 关键修复：设置start_after_created => true确保资源创建后自动启动
        Result = emqx_resource:create_local(
            ResId,
            ?PLUGIN_MONGODB_RESOURCE_GROUP,
            emqx_plugin_mongodb_connector,
            Connection,
            #{
                health_check_interval => HealthCheckInterval,
                start_after_created => true,
                start_timeout => 30000  % 30秒启动超时
            }
        ),
        % 根据创建结果进行处理
        ?SLOG(info, #{msg => "mongodb_resource_creation_result_generic", result => Result}),
        case start_resource_if_enabled(Result) of
            {ok, ResId} ->
                % 关键修复：显式启动资源，因为simple_async_internal_buffer模式不会自动启动
                ?SLOG(info, #{msg => "explicitly_starting_mongodb_resource_generic", resource_id => ResId}),
                case emqx_resource:start(ResId) of
                    ok ->
                        ?SLOG(info, #{msg => "mongodb_resource_started_successfully_generic", resource_id => ResId}),
                        {ok, ResId};
                    {error, StartError} ->
                        ?SLOG(error, #{msg => "failed_to_start_mongodb_resource_generic", resource_id => ResId, error => StartError}),
                        {error, {resource_start_failed, StartError}}
                end;
            Error ->
                Error
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_creating_mongodb_resource_generic",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {resource_creation_error, {E, R}}}
    end.

% @doc 根据启动结果处理资源。
% 这个函数有多个子句，通过模式匹配处理 `emqx_resource:create_local` 可能的返回值。
% 成功创建且无错误。

% 明确匹配 error := undefined
start_resource_if_enabled({ok, #{id := ResId, error := undefined}}) ->
    {ok, ResId};
% 资源已创建，但处于错误状态。
start_resource_if_enabled({ok, #{id := ResId, error := Error}}) when Error =/= undefined ->
    ?SLOG(error, #{
        msg => "mongodb_resource_created_with_error_state",
        error => Error,
        resource_id => ResId
    }),
    % 尝试停止已创建但有错误的资源
    emqx_resource:stop(ResId),
    {error, {resource_creation_error_state, Error}};
% `emqx_resource:create_local` 直接返回错误。
start_resource_if_enabled({error, Reason}) ->
    ?SLOG(error, #{
        msg => "failed_to_create_mongodb_resource_emqx_resource_create_local_failed",
        error => Reason,
        % ResId 可能未绑定
        resource_id => ?PLUGIN_MONGODB_RESOURCE_ID
    }),
    {error, {resource_creation_failed, Reason}};
% 兜底条款，处理未预期的返回值。
start_resource_if_enabled(Other) ->
    ?SLOG(error, #{
        msg => "unexpected_result_from_emqx_resource_create_local",
        result => Other,
        resource_id => ?PLUGIN_MONGODB_RESOURCE_ID
    }),
    {error, {unexpected_resource_creation_result, Other}}.

% @doc 构造要发布到MongoDB的事件消息。
% `Message` 是从 `on_message_publish/1` 传入的EMQX消息记录 (`#message{}`).
% 通过模式匹配从 `Message` 记录中提取字段。
% `#record{field1 = Var1, field2 = Var2}` 语法用于访问记录字段。
eventmsg_publish(
    Message = #message{
        % 消息ID
        id = Id,
        % 客户端ID (通常是MQTT ClientId)
        from = ClientId,
        % 服务质量等级
        qos = QoS,
        % 消息标志 (例如 retain, dup)
        flags = Flags,
        % MQTT主题
        topic = Topic,
        % 消息负载 (通常是二进制)
        payload = Payload,
        % 消息时间戳 (EMQX接收到的时间)
        timestamp = Timestamp
    }
) ->
    % 尝试解析JSON负载，如果失败则保留原始二进制
    ParsedPayload = try_parse_json_payload(Payload),

    % 调用 `with_basic_columns/2` 来添加一些通用字段。
    with_basic_columns(
        % 事件名称 (原子)
        'message.publish',
        % 构造一个map作为消息主体
        #{
            % 将消息ID转换为十六进制字符串
            id => emqx_guid:to_hexstr(Id),
            clientid => ClientId,
            % 从消息头获取用户名
            username => emqx_message:get_header(username, Message, undefined),
            % 使用解析后的负载
            payload => ParsedPayload,
            % 添加格式标识
            payload_format => get_payload_format(ParsedPayload),
            % 获取客户端IP和端口
            peerhost => ntoa(emqx_message:get_header(peerhost, Message, undefined)),
            topic => Topic,
            qos => QoS,
            % 格式化标志为可读形式
            flags => format_flags(Flags),
            % 获取并处理MQTT 5.0属性
            pub_props => printable_maps(emqx_message:get_header(properties, Message, #{})),
            % EMQX接收到消息的时间
            publish_received_at => Timestamp
        }
    );

% 支持EMQX 5.x中的消息格式 - 使用元组格式
eventmsg_publish({message, Id, QoS, ClientId, Flags, Headers, Topic, Payload, Timestamp, _Extra}) ->
    % 尝试解析JSON负载
    ParsedPayload = try_parse_json_payload(Payload),

    % 构造事件消息
    with_basic_columns(
        'message.publish',
        #{
            id => case is_binary(Id) of
                     true -> emqx_guid:to_hexstr(Id);
                     false -> list_to_binary(io_lib:format("~p", [Id]))
                 end,
            clientid => ClientId,
            username => maps:get(username, Headers, undefined),
            payload => ParsedPayload,
            payload_format => get_payload_format(ParsedPayload),
            peerhost => ntoa(maps:get(peerhost, Headers, undefined)),
            topic => Topic,
            qos => QoS,
            flags => format_flags(Flags),
            pub_props => printable_maps(maps:get(properties, Headers, #{})),
            publish_received_at => Timestamp
        }
    );

% 支持其他可能的消息格式 - 作为通用回退
eventmsg_publish(Message) ->
    % 提取必要的字段，尽可能使用通用方法
    Topic = extract_topic(Message),

    % 尝试从不同的消息格式中提取信息
    {Id, ClientId, QoS, Payload, Timestamp} = extract_message_fields(Message),

    % 尝试解析JSON负载
    ParsedPayload = try_parse_json_payload(Payload),

    % 构造基本事件消息
    with_basic_columns(
        'message.publish',
        #{
            id => Id,
            clientid => ClientId,
            topic => Topic,
            payload => ParsedPayload,
            payload_format => get_payload_format(ParsedPayload),
            qos => QoS,
            publish_received_at => Timestamp
        }
    ).

% 从不同格式的消息中提取关键字段
extract_message_fields(#message{id = Id, from = ClientId, qos = QoS, payload = Payload, timestamp = Timestamp}) ->
    {emqx_guid:to_hexstr(Id), ClientId, QoS, Payload, Timestamp};
extract_message_fields({message, Id, QoS, ClientId, _Flags, _Headers, _Topic, Payload, Timestamp, _Extra}) ->
    IdBin = case is_binary(Id) of
              true -> emqx_guid:to_hexstr(Id);
              false -> list_to_binary(io_lib:format("~p", [Id]))
            end,
    {IdBin, ClientId, QoS, Payload, Timestamp};
extract_message_fields(Message) when is_map(Message) ->
    % 从map中提取字段，提供默认值
    Id = maps:get(id, Message, <<"unknown_id">>),
    ClientId = maps:get(clientid, Message, <<"unknown_client">>),
    QoS = maps:get(qos, Message, 0),
    Payload = maps:get(payload, Message, <<"">>),
    Timestamp = maps:get(timestamp, Message, erlang:system_time(millisecond)),
    {Id, ClientId, QoS, Payload, Timestamp};
extract_message_fields(_) ->
    % 最后的回退，提供默认值
    {<<"unknown_id">>, <<"unknown_client">>, 0, <<"">>, erlang:system_time(millisecond)}.

% @doc 处理JSON负载
% 直接保存原始JSON字符串，不进行解析，确保MongoDB接收到的是原始格式
try_parse_json_payload(Payload) when is_binary(Payload) ->
    % 只需记录日志，不对payload进行处理
    ?SLOG(debug, #{
        msg => "keeping_original_payload",
        payload_size => byte_size(Payload),
        payload_sample => case byte_size(Payload) > 100 of
            true -> binary:part(Payload, 0, 100);
            false -> Payload
        end
    }),
    % 直接返回原始payload
    Payload;
try_parse_json_payload(Payload) ->
    % 其他类型也保持原样
    Payload.

% @doc 获取负载格式标识
get_payload_format(Payload) when is_map(Payload) -> json;
get_payload_format(Payload) when is_binary(Payload) -> binary;
get_payload_format(_) -> other.

% @doc 格式化MQTT标志为可读形式
format_flags(Flags) when is_map(Flags) ->
    maps:fold(
        fun(K, V, Acc) ->
            Acc#{atom_to_binary(K, utf8) => V}
        end,
        #{},
        Flags
    );
format_flags(Flags) ->
    Flags.

% @doc 执行MongoDB查询。
% `EvtMsg`: 构造好的事件消息 (map)。
% `Querys`: 从 `select/1` 返回的匹配规则列表，每个元素是 `{Name, Collection}`。
query(EvtMsg, Querys) ->
    % 添加更详细的调试日志
    ?SLOG(debug, #{
        msg => "mongodb_query_started",
        querys => Querys,
        evt_msg_summary => maps:get(id, EvtMsg, <<>>),
        evt_msg_keys => maps:keys(EvtMsg)
    }),

    % 使用自适应重试机制
    adaptive_retry(
        fun() ->
            % 使用异步查询
            ?SLOG(debug, #{
                msg => "mongodb_before_query",
                resource_id => ?PLUGIN_MONGODB_RESOURCE_ID,
                query_type => {Querys, EvtMsg}
            }),
            Result = emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID, {Querys, EvtMsg}),

            % 添加调试日志
            ?SLOG(debug, #{
                msg => "mongodb_query_result",
                result => Result,
                querys => Querys
            }),

            query_ret(Result, EvtMsg, Querys)
        end,
        3
    ).

% @doc 处理查询结果。
% `emqx_resource:query` 对于异步查询，通常会立即返回 `{async_return, ok}` 或错误。
% 实际的数据库操作结果由 `emqx_plugin_mongodb_connector` 模块内部处理。

% 资源返回 {ok, ok} (同步查询成功，但这里是异步，所以此匹配可能不常用)
query_ret({ok, ok}, _, _) ->
    ok;
% 异步查询请求已成功提交给资源进程。
query_ret({async_return, ok}, _, _) ->
    ok;
% 资源直接返回 ok (某些情况下的简化返回)
query_ret(ok, _, _) ->
    ok;
% 资源返回 {ok, OtherThanOk} (同步查询但结果非预期)
query_ret({ok, Result}, EvtMsg, Querys) when Result =/= ok ->
    % 改为warning，因为资源本身是ok的，但操作可能有问题
    ?SLOG(
        warning,
        #{
            msg => "mongodb_resource_query_returned_non_ok_result",
            result => Result,
            % 只记录ID避免日志过大
            evt_msg_summary => maps:get(id, EvtMsg, <<"unknown_id">>),
            querys_count => length(Querys)
        }
    ),
    % 仍然返回ok，让消息流程继续，错误由connector内部处理
    ok;
% `emqx_resource:query` 直接返回错误 (例如资源不存在或忙)
query_ret({error, Reason} = Error, EvtMsg, Querys) ->
    ?SLOG(
        error,
        #{
            msg => "failed_to_query_mongodb_resource_emqx_resource_query_failed",
            error_reason => Reason,
            % 保留原始ret供调试
            ret => Error,
            evt_msg_summary => maps:get(id, EvtMsg, <<"unknown_id">>),
            querys_count => length(Querys)
        }
    ),
    % 返回错误以便重试
    {error, Reason};
% 其他未知返回
query_ret(Ret, EvtMsg, Querys) ->
    ?SLOG(
        error,
        #{
            msg => "unexpected_return_from_mongodb_resource_query",
            ret => Ret,
            evt_msg_summary => maps:get(id, EvtMsg, <<"unknown_id">>),
            querys_count => length(Querys)
        }
    ),
    % 返回错误以便重试
    {error, unexpected_return}.

% @doc 为事件消息添加基础列信息。
% `EventName`: 事件类型，例如 `'message.publish'`。
% `Columns`: 包含核心事件数据的map。

% `when is_map(Columns)` 是一个卫语句，确保Columns是map
with_basic_columns(EventName, Columns) when is_map(Columns) ->
    % 在现有 `Columns` map 的基础上添加新字段。

    % Erlang map更新语法
    Columns#{
        event => EventName,
        % 当前系统时间戳 (毫秒)。MongoDB通常用UTCDateTime，确保时间戳格式兼容。
        timestamp => erlang:system_time(millisecond),
        % 当前Erlang节点名
        node => node()
    }.

% @doc IP地址元组转换为字符串表示。
% Erlang网络库通常以 `{A,B,C,D}` 元组表示IPv4地址。
ntoa(undefined) -> undefined;
ntoa({IpAddr, Port}) -> iolist_to_binary([inet:ntoa(IpAddr), ":", integer_to_list(Port)]);
ntoa(IpAddr) -> iolist_to_binary(inet:ntoa(IpAddr)).

% @doc 转换消息属性 (MQTT 5.0 Properties) 为可打印/存储的格式。
% MQTT 5.0 属性是一个map或proplist。
printable_maps(undefined) ->
    % 如果没有属性，返回空map
    #{};
printable_maps(Headers) ->
    % `maps:fold/3` 遍历map中的每个键值对。
    % 类似于Java中的 `map.entrySet().stream().reduce(...)` 或 `forEach`。
    maps:fold(
        % 定义一个匿名函数 (fun) 来处理每个键值对
        fun
            % (Key, Value, AccumulatorIn) -> AccumulatorOut
            % 特殊处理 `peerhost`, `peername`, `sockname` 字段，将其IP元组转换为字符串。
            (K, V0, AccIn) when K =:= peerhost; K =:= peername; K =:= sockname ->
                AccIn#{K => ntoa(V0)};
            % 特殊处理 'User-Property' (用户属性)。MQTT 5.0 用户属性是一个键值对列表。
            ('User-Property', V0, AccIn) when is_list(V0) ->
                AccIn#{
                    % 将列表转换为map存储
                    'User-Property' => maps:from_list(V0),
                    % 同时保留原始的键值对列表形式，方便某些查询
                    'User-Property-Pairs' => [
                        #{
                            key => Key,
                            value => Value
                        }
                     || % 列表推导 (List Comprehension)
                        {Key, Value} <- V0
                    ]
                };
            % 注意：当前实现会丢弃值为元组的属性 (除了上面特殊处理的)。
            % 如果需要保留，应转换为字符串或其他可序列化格式。
            (_K, V, AccIn) when is_tuple(V) ->
                % 或者转换为字符串: AccIn#{ K => io_lib:format("~p", [V])}
                AccIn;
            % 其他类型的键值对直接保留。
            (K, V, AccIn) ->
                AccIn#{K => V}
        end,
        % 初始累加器，确保 'User-Property' 键总是存在，即使没有用户属性
        #{'User-Property' => #{}},
        % 要遍历的原始Headers map
        Headers
    ).

% @doc 解析主题配置列表并存入ETS表。
% `Topics` 是从配置文件读取的包含主题过滤器规则的列表。
% 每个规则是一个map，例如 `#{filter := "sensor/+/data", name := "temp_sensor", collection := "sensor_data"}`。

% 基本情况：列表为空，完成解析
topic_parse([]) ->
    ok;
topic_parse([#{filter := Filter, name := Name, collection := Collection} | T]) ->
    % ETS表已在load/0或load/1中创建，此处不再重复检查和创建。
    % 将规则名称转换为字符串 (如果它是原子的话)。
    NameStr =
        case is_atom(Name) of
            true -> atom_to_list(Name);
            % 假定已经是字符串或二进制 (binary)
            false -> Name
        end,

    % 确保Collection是二进制类型
    CollectionBin =
        case is_binary(Collection) of
            true -> Collection;
            false ->
                case is_list(Collection) of
                    true ->
                        % 处理可能带引号的字符串
                        CollStr = string:trim(Collection, both, "\""),
                        list_to_binary(CollStr);
                    false ->
                        case is_atom(Collection) of
                            true -> atom_to_binary(Collection, utf8);
                            false ->
                                % 处理其他可能的类型，如字符串中的字符串
                                try
                                    CollStr = if
                                        is_list(Collection) -> Collection;
                                        true -> io_lib:format("~p", [Collection])
                                    end,
                                    % 处理可能带引号的字符串
                                    TrimmedStr = string:trim(CollStr, both, "\""),
                                    list_to_binary(TrimmedStr)
                                catch
                                    _:_ -> <<"unknown_collection">>
                                end
                        end
                end
        end,

    % 添加调试日志，帮助排查问题
    ?SLOG(debug, #{
        msg => "topic_parse_collection",
        original_collection => Collection,
        parsed_collection => CollectionBin,
        filter => Filter,
        name => NameStr
    }),

    % 构造要插入ETS的元组。
    Item = {NameStr, Filter, CollectionBin},
    % 将规则插入到名为 `?PLUGIN_MONGODB_TAB` 的ETS表中。
    ets:insert(?PLUGIN_MONGODB_TAB, Item),
    % 递归处理列表的剩余部分 `T`。
    topic_parse(T);
% 处理列表中可能存在的无效项 (不是期望的map格式)。
topic_parse([InvalidItem | T]) ->
    ?SLOG(warning, #{msg => "invalid_item_in_topics_config_skipping", item => InvalidItem}),
    topic_parse(T).

% 在emqx_plugin_mongodb.erl中优化select函数
select(Message) ->
    % 使用带缓存的实现
    select_with_cache(Message).

% 初始化缓存
init_topic_match_cache() ->
    % 主缓存表
    case ets:info(?TOPIC_MATCH_CACHE_TAB) of
        undefined ->
            ets:new(?TOPIC_MATCH_CACHE_TAB, [
                named_table, public, set, {read_concurrency, true}
            ]);
        _ -> ok
    end,

    % 最近访问表，用于LRU策略
    case ets:info(?TOPIC_MATCH_CACHE_RECENT_TAB) of
        undefined ->
            ets:new(?TOPIC_MATCH_CACHE_RECENT_TAB, [
                named_table, public, ordered_set, {write_concurrency, true}
            ]);
        _ -> ok
    end.

% 缓存主题匹配结果
select_with_cache(Message) ->
    % 提取主题，支持不同的消息格式
    Topic = extract_topic(Message),

    % 首先检查是否为系统消息
    case emqx_message:is_sys(Message) of
        true ->
            % 系统消息不处理
            false;
        false ->
            % 确保缓存表已初始化
            case ets:info(?TOPIC_MATCH_CACHE_TAB) of
                undefined ->
                    init_topic_match_cache(),
                    % 缓存未初始化，直接使用内部实现
                    select_internal(Message);
                _ ->
                    % 检查缓存
                    case ets:lookup(?TOPIC_MATCH_CACHE_TAB, Topic) of
                        [{_, Result, Timestamp}] ->
                            % 检查TTL
                            Now = erlang:system_time(millisecond),
                            if
                                Now - Timestamp < ?TOPIC_MATCH_CACHE_TTL ->
                                    % 更新主题访问时间用于LRU管理
                                    update_topic_access(Topic),
                                    Result;
                                true ->
                                    % 缓存过期，重新计算并更新
                                    Result = select_internal(Message),
                                    ets:insert(?TOPIC_MATCH_CACHE_TAB, {Topic, Result, Now}),
                                    % 更新主题访问时间
                                    update_topic_access(Topic),
                                    Result
                            end;
                        [] ->
                            % 缓存未命中，计算并缓存结果
                            Result = select_internal(Message),
                            Now = erlang:system_time(millisecond),
                            ets:insert(?TOPIC_MATCH_CACHE_TAB, {Topic, Result, Now}),
                            % 更新主题访问时间
                            update_topic_access(Topic),
                            % 维护缓存大小
                            check_and_clean_cache(),
                            Result
                    end
            end
    end.

% 提取消息主题，支持不同的消息格式
extract_topic(#message{topic = Topic}) when is_binary(Topic) ->
    Topic;
extract_topic({message, _Id, _QoS, _From, _Flags, _Headers, Topic, _Payload, _Timestamp, _Extra}) when is_binary(Topic) ->
    Topic;
extract_topic(Message) when is_map(Message) ->
    maps:get(topic, Message, <<"">>);
extract_topic(_) ->
    <<"">>.

% @doc 自适应重试机制
adaptive_retry(Fun, MaxRetries) ->
    adaptive_retry(Fun, MaxRetries, ?MIN_RETRY_DELAY, ?MAX_RETRY_DELAY).

adaptive_retry(Fun, MaxRetries, MinDelay, MaxDelay) ->
    adaptive_retry_internal(Fun, MaxRetries, MinDelay, MaxDelay, 1).

adaptive_retry_internal(_Fun, 0, _MinDelay, _MaxDelay, _Attempt) ->
    {error, max_retries_exceeded};
adaptive_retry_internal(Fun, MaxRetries, MinDelay, MaxDelay, Attempt) ->
    Result =
        try
            Fun()
        catch
            E:R:_ -> {error, {E, R}}
        end,

    case Result of
        ok -> ok;
        {ok, Value} -> {ok, Value};
        {error, retryable} ->
            % 可重试错误，进行指数退避延迟
            Delay = calculate_delay(MinDelay, MaxDelay, Attempt),
            timer:sleep(Delay),
            adaptive_retry_internal(Fun, MaxRetries - 1, MinDelay, MaxDelay, Attempt + 1);
        {error, _} = Error ->
            % 不可重试的错误，直接返回
            Error
    end.

% 计算指数退避延迟（带抖动）
calculate_delay(MinDelay, MaxDelay, Attempt) ->
    % 基础指数延迟
    BaseDelay = MinDelay * math:pow(?RETRY_FACTOR, Attempt - 1),
    % 添加抖动
    Jitter = BaseDelay * ?RETRY_JITTER * (rand:uniform() * 2 - 1),
    % 应用抖动并限制在最大值内
    Delay = round(min(MaxDelay, BaseDelay + Jitter)),
    max(MinDelay, Delay).

% 注意：系统消息检查现在使用EMQX内置的emqx_message:is_sys/1函数
% 该函数检查消息标志中的sys标记和$SYS主题前缀

% @doc 主题匹配规则。
% `Filter` 是从配置中读取的主题过滤器字符串，例如 `"foo/bar"`, `"foo/+/baz"`, `"foo/#"`。
% EMQX配置指明过滤器不能以 `#` 或 `+` 开头，这里做了检查。

% 主题过滤器以 `#` 开头，视为无效 (根据插件的特定逻辑)
match_topic(_, <<"#", _/binary>>) ->
    false;
% 主题过滤器以 `+` 开头，视为无效
match_topic(_, <<"+", _/binary>>) ->
    false;
% 不处理系统消息。
match_topic(Message, Filter) ->
    % 首先检查是否为系统消息
    case emqx_message:is_sys(Message) of
        true ->
            false;  % 系统消息不匹配任何过滤器
        false ->
            % 安全获取主题，防止错误格式的消息导致崩溃
            Topic =
                try
                    extract_topic(Message)
                catch
                    _:_ ->
                        % 提取主题失败，返回一个不会匹配任何规则的特殊主题
                        <<"$INVALID_TOPIC">>
                end,

            % 使用try-catch保护主题匹配操作
            try
                emqx_topic:match(Topic, Filter)
            catch
                _:_ ->
                    % 匹配过程中发生错误，默认不匹配
                    false
            end
    end.

% @doc 检查MongoDB连接是否活跃
check_connection_alive(Pid) ->
    % 首先检查进程是否存活
    case is_process_alive(Pid) of
        false ->
            % 进程已经死亡，直接返回false
            false;
        true ->
            % 进程存活，尝试ping操作
    try
        case
            with_timeout(
                fun() ->
                    % 使用标准的 MongoDB 命令格式
                    mc_worker_api:command(Pid, #{<<"ping">> => 1})
                end,
                5000
            )
        of
            {ok, #{<<"ok">> := 1.0}} ->
                true;
            {ok, OtherResult} ->
                ?SLOG(warning, #{
                    msg => "mongo_connection_unexpected_ping_response",
                    result => OtherResult
                }),
                false;
            {error, timeout} ->
                ?SLOG(warning, #{msg => "mongo_connection_timeout"}),
                false;
            {error, Reason} ->
                ?SLOG(warning, #{
                    msg => "mongo_connection_get_status_error",
                    reason => Reason
                }),
                false
        end
    catch
        Class:Error:Stack ->
            ?SLOG(warning, #{
                msg => "mongo_connection_get_status_exception",
                class => Class,
                error => Error,
                stack => Stack
            }),
            false
            end
    end.

% @doc 添加带超时的函数执行辅助函数
with_timeout(Fun, Timeout) when is_function(Fun, 0), is_integer(Timeout), Timeout > 0 ->
    % 使用父进程ID
    Parent = self(),
    % 启动子进程并监控
    {Pid, Ref} = spawn_monitor(
        fun() ->
            try
                % 执行函数并发送结果
                Result = Fun(),
                Parent ! {self(), result, Result}
            catch
                Class:Error:Stack ->
                    Parent ! {self(), error, {Class, Error, Stack}}
            end
        end
    ),

    % 设置定时器
    TimerRef = erlang:start_timer(Timeout, self(), {timeout, Pid}),

    % 等待结果、超时或错误
    receive
        % 收到结果
        {Pid, result, Result} ->
            % 清理
            erlang:cancel_timer(TimerRef),
            _ = receive {timeout, TimerRef, _} -> ok after 0 -> ok end,
            erlang:demonitor(Ref, [flush]),
            {ok, Result};

        % 收到错误
        {Pid, error, {Class, Error, _Stack}} ->
            % 清理
            erlang:cancel_timer(TimerRef),
            _ = receive {timeout, TimerRef, _} -> ok after 0 -> ok end,
            erlang:demonitor(Ref, [flush]),
            {error, {Class, Error}};

        % 超时
        {timeout, TimerRef, {timeout, Pid}} ->
            % 终止子进程
            exit(Pid, kill),
            erlang:demonitor(Ref, [flush]),
            {error, timeout};

        % 子进程异常退出
        {'DOWN', Ref, process, Pid, Reason} when Reason =/= normal ->
            % 清理
            erlang:cancel_timer(TimerRef),
            _ = receive {timeout, TimerRef, _} -> ok after 0 -> ok end,
            {error, Reason}
    after Timeout + 100 ->
        % 备用超时机制，以防主超时机制失效
        exit(Pid, kill),
        erlang:demonitor(Ref, [flush]),
        {error, timeout}
    end.

% 单独定义ensure_binary函数
ensure_binary(Name) when is_binary(Name) ->
    Name;
ensure_binary(Name) when is_list(Name) ->
    list_to_binary(Name);
ensure_binary(Name) when is_atom(Name) ->
    atom_to_binary(Name, utf8);
ensure_binary(_) ->
    <<"rs">>.

% MongoDB类型初始化
init_mongo_type(#{mongo_type := rs, replica_set_name := Name}) ->
    % 确保副本集名称是二进制类型
    {rs, ensure_binary(Name)};
init_mongo_type(#{mongo_type := single}) ->
    single;
init_mongo_type(#{mongo_type := sharded}) ->
    sharded.

% @doc 更新主题访问时间
update_topic_access(Topic) ->
    % 确保缓存表已初始化
    case ets:info(?TOPIC_MATCH_CACHE_RECENT_TAB) of
        undefined ->
            init_topic_match_cache();
        _ -> ok
    end,

    % 使用try-catch保护ETS操作
    try
        Now = erlang:system_time(millisecond),
        ets:insert(?TOPIC_MATCH_CACHE_RECENT_TAB, {Topic, Now})
    catch
        _:_ ->
            % 插入操作失败，忽略错误
            ok
    end.

% @doc 检查并清理缓存
check_and_clean_cache() ->
    % 确保缓存表已初始化
    case ets:info(?TOPIC_MATCH_CACHE_TAB) of
        undefined ->
            init_topic_match_cache(),
            ok;
        _ ->
            % 使用try-catch保护ETS操作
            try
                Size = ets:info(?TOPIC_MATCH_CACHE_TAB, size),
                MaxSize = case get(?TOPIC_MATCH_CACHE_SIZE) of
                    undefined -> ?TOPIC_MATCH_CACHE_SIZE;
                    Value when is_integer(Value), Value > 0 -> Value;
                    _ -> ?TOPIC_MATCH_CACHE_SIZE % 回退到默认值
                end,
                if
                    Size > MaxSize ->
                        % 缓存过大，删除最久未访问的条目
                        delete_least_recently_used(Size - MaxSize);
                    true -> ok
                end
            catch
                _:_ ->
                    % ETS操作失败，尝试重新初始化缓存
                    init_topic_match_cache(),
                    ok
            end
    end.

% @doc 删除最久未访问的缓存条目
delete_least_recently_used(Count) ->
    % 确保缓存表已初始化
    case ets:info(?TOPIC_MATCH_CACHE_RECENT_TAB) of
        undefined ->
            init_topic_match_cache(),
            [];
        _ ->
            % 使用try-catch保护ETS操作
            try
                % 按时间戳排序获取最久未访问的主题
                % 使用手动创建的匹配规范，而不是ets:fun2ms
                MS = [{{'$1', '_'}, [], ['$1']}],
                Topics = lists:sublist(ets:select(?TOPIC_MATCH_CACHE_RECENT_TAB, MS), Count),

                % 从两个表中删除这些主题
                [begin
                     try
                         ets:delete(?TOPIC_MATCH_CACHE_TAB, Topic),
                         ets:delete(?TOPIC_MATCH_CACHE_RECENT_TAB, Topic)
                     catch
                         _:_ -> ok % 忽略单个删除操作的错误
                     end
                 end || Topic <- Topics]
            catch
                _:_ ->
                    % ETS操作失败，忽略错误
                    []
            end
    end.

% 选择的内部实现，不使用缓存，在select_with_cache中调用
select_internal(Message) ->
    % 提取主题
    Topic = extract_topic(Message),

    % 添加调试日志
    ?SLOG(debug, #{
        msg => "select_internal_called",
        topic => Topic,
        message_type => case is_map(Message) of
            true -> map;
            false ->
                case is_tuple(Message) andalso tuple_size(Message) > 0 andalso element(1, Message) =:= message of
                    true -> message_record;
                    false -> other
                end
        end,
        message_keys => case is_map(Message) of
            true -> maps:keys(Message);
            false -> []
        end
    }),

    % 预先检查系统消息
    case emqx_message:is_sys(Message) of
        true ->
            % 系统消息不处理
            false;
        false ->
            try
                % 使用ets:match_object获取所有规则，然后过滤
                AllRules = ets:match_object(?PLUGIN_MONGODB_TAB, {'_', '_', '_'}),

                % 添加调试日志
                ?SLOG(debug, #{
                    msg => "select_internal_all_rules",
                    rules_count => length(AllRules),
                    first_few_rules => lists:sublist(AllRules, 3)
                }),

                % 使用二分查找优化匹配过程
                MatchingRules = find_matching_rules(Topic, AllRules),

                % 添加调试日志
                ?SLOG(debug, #{
                    msg => "select_internal_matching_rules",
                    matching_rules => MatchingRules
                }),

                case MatchingRules of
                    [] -> false;
                    _ -> {true, MatchingRules}
                end
            catch
                E:R:S ->
                    ?SLOG(error, #{
                        msg => "error_in_select_internal",
                        error => E,
                        reason => R,
                        stack => S,
                        topic => Topic
                    }),
                    false
            end
    end.

% 使用二分查找优化匹配过程
find_matching_rules(Topic, Rules) ->
    % 添加调试日志
    ?SLOG(debug, #{
        msg => "find_matching_rules_called",
        topic => Topic,
        rules_count => length(Rules)
    }),

    % 首先按主题过滤器长度排序，通常更具体的过滤器更长
    SortedRules = lists:sort(
        fun({_, FilterA, _}, {_, FilterB, _}) ->
            byte_size(FilterA) >= byte_size(FilterB)
        end,
        Rules
    ),

    % 然后尝试匹配
    MatchingRules = lists:filtermap(
        fun({Name, Filter, Collection}) ->
            % 确保Filter是二进制类型
            BinFilter = case is_binary(Filter) of
                true -> Filter;
                false ->
                    case is_list(Filter) of
                        true -> list_to_binary(Filter);
                        false ->
                            case is_atom(Filter) of
                                true -> atom_to_binary(Filter, utf8);
                                false -> term_to_binary(Filter)
                            end
                    end
            end,

            % 添加调试日志
            ?SLOG(debug, #{
                msg => "matching_topic",
                topic => Topic,
                filter => BinFilter,
                name => Name,
                collection => Collection
            }),

            % 尝试匹配
            try
                case match_topic_optimized(Topic, BinFilter) of
                    true ->
                        ?SLOG(debug, #{
                            msg => "topic_matched",
                            topic => Topic,
                            filter => BinFilter
                        }),
                        {true, {Name, Collection}};
                    false -> false
                end
            catch
                E:R:S ->
                    ?SLOG(error, #{
                        msg => "error_matching_topic",
                        error => E,
                        reason => R,
                        stack => S,
                        topic => Topic,
                        filter => BinFilter
                    }),
                    false
            end
        end,
        SortedRules
    ),

    % 添加调试日志
    ?SLOG(debug, #{
        msg => "find_matching_rules_result",
        topic => Topic,
        matching_rules_count => length(MatchingRules),
        matching_rules => MatchingRules
    }),

    MatchingRules.

% @doc 简化的主题匹配实现
simple_topic_match(Topic, Filter) ->
    TopicParts = binary:split(Topic, <<"/">>, [global]),
    FilterParts = binary:split(Filter, <<"/">>, [global]),
    match_parts(TopicParts, FilterParts).

% 匹配主题部分
match_parts([], []) ->
    true;
match_parts(_, [<<"#">>]) ->
    % # 匹配剩余所有部分
    true;
match_parts([_|TopicRest], [<<"+">>|FilterRest]) ->
    % + 匹配单个部分
    match_parts(TopicRest, FilterRest);
match_parts([TopicPart|TopicRest], [FilterPart|FilterRest]) when TopicPart =:= FilterPart ->
    % 精确匹配
    match_parts(TopicRest, FilterRest);
match_parts(_, _) ->
    % 不匹配
    false.

% @doc 优化的主题匹配判断
% 提前检查常见的通配符条件
match_topic_optimized(_, <<"#">>) ->
    % 单独的 # 匹配所有主题
    true;
match_topic_optimized(Topic, Filter) when is_binary(Topic), is_binary(Filter) ->
    % 检查是否包含通配符，如果包含则使用完整的匹配逻辑
    case binary:match(Filter, [<<"#">>, <<"+">>]) of
        nomatch ->
            % 没有通配符，直接比较
            Topic =:= Filter;
        _ ->
            % 包含通配符，使用 EMQX 的主题匹配函数
            try
                emqx_topic:match(Topic, Filter)
            catch
                _:_ ->
                    % 如果 emqx_topic:match 不可用，使用简化匹配
                    simple_topic_match(Topic, Filter)
            end
    end;
match_topic_optimized(Topic, Filter) ->
    % 处理非二进制类型的主题或过滤器
    ?SLOG(warning, #{
        msg => "non_binary_topic_or_filter",
        topic_type => case is_binary(Topic) of true -> binary; false -> other end,
        filter_type => case is_binary(Filter) of true -> binary; false -> other end
    }),
    false.

% 简单的主题匹配实现
match_topic_simple(Topic, Filter) ->
    % 如果过滤器是 "#"，匹配所有
    case Filter of
        <<"#">> -> true;
        _ -> do_match_topic(binary:split(Topic, <<"/">>, [global]), binary:split(Filter, <<"/">>, [global]))
    end.

% 递归匹配主题层级
do_match_topic([], [<<"#">>]) -> true;
do_match_topic([], []) -> true;
do_match_topic([_H|_T], [<<"#">>]) -> true;
do_match_topic([H|T], [<<"+">>|T2]) -> do_match_topic(T, T2);
do_match_topic([H|T], [H|T2]) -> do_match_topic(T, T2);
do_match_topic(_, _) -> false.

% @doc 初始化批处理调整器
init_batch_size_adjuster() ->
    % 使用独立进程处理批处理调整，避免进程依赖问题
    spawn(fun() -> batch_size_adjuster_loop() end),
    ets:insert(?PLUGIN_MONGODB_TAB, {batch_metrics, #{
        success_count => 0,
        error_count => 0,
        avg_latency => 0,
        current_batch_size => 1000
    }}).

% @doc 批处理调整器循环
batch_size_adjuster_loop() ->
    timer:sleep(?BATCH_SIZE_ADJUST_INTERVAL),
    % 执行批处理大小调整
    spawn(fun() -> adjust_batch_size() end),
    % 继续循环
    batch_size_adjuster_loop().

% @doc 调整批处理大小
adjust_batch_size() ->
    try
        % 获取当前批处理指标
        case ets:lookup(?PLUGIN_MONGODB_TAB, batch_metrics) of
            [{_, Metrics}] ->
                % 根据指标调整批处理大小
                adjust_batch_size_based_on_metrics(Metrics);
            [] ->
                % 没有指标，使用默认值
                ok
        end
    catch
        _:_ ->
            % 调整失败，忽略错误
            ok
    end.

% @doc 根据指标调整批处理大小
adjust_batch_size_based_on_metrics(Metrics) ->
    SuccessCount = maps:get(success_count, Metrics, 0),
    ErrorCount = maps:get(error_count, Metrics, 0),
    CurrentSize = maps:get(current_batch_size, Metrics, 1000),

    % 简单的调整策略
    NewSize = if
        ErrorCount > SuccessCount * 0.1 ->
            % 错误率过高，减少批处理大小
            max(100, CurrentSize - 100);
        SuccessCount > 0, ErrorCount =:= 0 ->
            % 没有错误，增加批处理大小
            min(5000, CurrentSize + 100);
        true ->
            % 保持当前大小
            CurrentSize
    end,

    % 更新批处理大小
    ets:insert(?PLUGIN_MONGODB_TAB, {batch_metrics, Metrics#{current_batch_size => NewSize}}).

% 批处理大小调整逻辑
handle_info(adjust_batch_size, State) ->
    [{_, Metrics}] = ets:lookup(?PLUGIN_MONGODB_TAB, batch_metrics),
    #{
        success_count := SuccessCount,
        error_count := ErrorCount,
        avg_latency := AvgLatency,
        current_batch_size := CurrentSize
    } = Metrics,

    % 根据成功率和延迟调整批处理大小
    NewSize = if
        ErrorCount > SuccessCount * 0.1 ->
            % 错误率高，减小批处理大小
            max(?MIN_BATCH_SIZE, CurrentSize div 2);
        AvgLatency > 200 ->
            % 延迟高，稍微减小批处理大小
            max(?MIN_BATCH_SIZE, CurrentSize * 3 div 4);
        AvgLatency < 50 andalso SuccessCount > 100 ->
            % 延迟低且吞吐量高，增加批处理大小
            min(?MAX_BATCH_SIZE, CurrentSize * 5 div 4);
        true ->
            % 保持当前大小
            CurrentSize
    end,

    % 更新批处理大小并重置指标
    ets:insert(?PLUGIN_MONGODB_TAB, {batch_metrics, #{
        success_count => 0,
        error_count => 0,
        avg_latency => 0,
        current_batch_size => NewSize
    }}),

    % 设置下一次调整时间
    erlang:send_after(?BATCH_SIZE_ADJUST_INTERVAL, self(), adjust_batch_size),
    {noreply, State}.

% 等待批处理服务器进程退出的辅助函数
wait_for_batch_server_exit(Pid, Timeout) ->
    wait_for_batch_server_exit(Pid, Timeout, 100).

wait_for_batch_server_exit(_Pid, 0, _Interval) ->
    timeout;
wait_for_batch_server_exit(Pid, Timeout, Interval) ->
    case is_process_alive(Pid) of
        true ->
            timer:sleep(Interval),
            wait_for_batch_server_exit(Pid, Timeout - Interval, Interval);
        false ->
            ok
    end.

% @doc 检查MongoDB资源是否已启动
is_resource_started() ->
    case emqx_resource:get_instance(?PLUGIN_MONGODB_RESOURCE_ID) of
        {ok, _} -> true;
        {error, not_found} -> false;
        {error, _} -> false
    end.

% @doc 添加钩子 (辅助函数)。
% `HookPoint`: 钩子点名称，例如 `'message.publish'`。
% `MFA`: 模块-函数-参数元组 `{Module, Function, Args}`。
% `?HP_HIGHEST`: 钩子优先级，表示此钩子函数应尽可能早地执行。
hook(HookPoint, MFA) ->
    emqx_hooks:add(HookPoint, MFA, _Property = ?HP_HIGHEST).

% @doc 删除钩子 (辅助函数)。
unhook(HookPoint, MFA) ->
    emqx_hooks:del(HookPoint, MFA).
