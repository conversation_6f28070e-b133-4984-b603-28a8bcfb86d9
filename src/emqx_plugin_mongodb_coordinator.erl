%%%-------------------------------------------------------------------
%%% @doc
%%% MongoDB插件的模块协调器
%%% 这个模块是整个MongoDB插件系统的核心协调器，负责管理和协调所有功能模块
%%%
%%% 功能概述：
%%% 1. 模块注册和生命周期管理 - 统一管理所有功能模块的注册和状态
%%% 2. 事件传播和通信 - 实现模块间的事件通信和状态同步
%%% 3. 共享上下文管理 - 提供全局共享的上下文信息存储
%%% 4. 系统状态监控 - 监控整个插件系统的健康状态
%%% 5. 性能优化协调 - 根据系统状态动态优化各模块的行为
%%%
%%% 架构设计：
%%% - 采用中央协调器模式，所有模块通过协调器进行通信
%%% - 使用ETS表进行高性能的状态存储和查询
%%% - 实现事件驱动的模块间协作机制
%%% - 提供系统级的监控和优化能力
%%%
%%% Java等价概念：
%%% 类似于Spring Framework的ApplicationContext
%%% 或者微服务架构中的服务注册中心（如Eureka、Consul）
%%% 或者事件总线系统（如Guava EventBus、Spring Events）
%%% 或者企业集成模式中的消息路由器（Message Router）
%%%
%%% 设计模式：
%%% - 中介者模式（Mediator Pattern）：协调器作为中介者管理模块间通信
%%% - 观察者模式（Observer Pattern）：事件通知和状态变更传播
%%% - 注册表模式（Registry Pattern）：模块注册和查找
%%% - 上下文模式（Context Pattern）：共享上下文信息管理
%%% @end
%%%-------------------------------------------------------------------
-module(emqx_plugin_mongodb_coordinator).

%% 实现gen_server行为，提供状态管理和并发控制
%% 类似于Java中实现Runnable接口或继承Thread类
-behaviour(gen_server).

%% API - 公共接口函数
%% 这些函数提供协调器的主要功能，类似于Java中的public方法
-export([
    start_link/0,           % 启动协调器服务，类似于Java的服务启动方法
    register_module/2,      % 注册模块到协调器，类似于服务注册
    notify_event/3,         % 通知事件，类似于事件发布
    get_system_status/0,    % 获取系统状态，类似于健康检查接口
    get_module_status/1,    % 获取模块状态，类似于单个服务状态查询
    update_shared_context/2,% 更新共享上下文，类似于全局配置更新
    get_shared_context/1,   % 获取共享上下文，类似于全局配置查询
    stop/0,                 % 停止协调器，类似于服务关闭
    integrate/0             % 集成到系统中，类似于模块初始化
]).

%% gen_server callbacks
-export([
    init/1,
    handle_call/3,
    handle_cast/2,
    handle_info/2,
    terminate/2,
    code_change/3
]).

%% 内部导出函数
-export([
    system_monitor/0,
    propagate_event/3
]).

-include("emqx_plugin_mongodb.hrl").

%% 定义ETS表名 - 用于高性能的内存存储和查询
%% ETS (Erlang Term Storage) 类似于Java中的ConcurrentHashMap
%% 但性能更高，支持原子操作和并发访问
-define(COORDINATOR_REGISTRY, emqx_plugin_mongodb_coordinator_registry).  % 协调器注册表
-define(SHARED_CONTEXT_TAB, emqx_plugin_mongodb_shared_context).          % 共享上下文表
-define(MODULE_STATUS_TAB, emqx_plugin_mongodb_module_status).            % 模块状态表
-define(EVENT_HISTORY_TAB, emqx_plugin_mongodb_event_history).            % 事件历史表

%% 事件类型定义 - 定义系统中的各种事件类型
%% 类似于Java中的枚举类型或常量定义
%% 这些事件用于模块间的通信和状态同步
-define(EVENT_BACKPRESSURE, backpressure).    % 背压事件：系统负载过高时触发
-define(EVENT_CIRCUIT_BREAKER, circuit_breaker). % 熔断器事件：服务故障时触发
-define(EVENT_CONNECTION, connection).         % 连接事件：数据库连接状态变化
-define(EVENT_BATCH, batch).                  % 批处理事件：批处理参数调整
-define(EVENT_RESOURCE, resource).            % 资源事件：系统资源使用变化
-define(EVENT_ERROR, error).                  % 错误事件：系统错误和异常
-define(EVENT_PIPELINE, pipeline).            % 管道事件：并行处理管道状态变化
-define(EVENT_ZERO_COPY, zero_copy).          % 零拷贝事件：内存优化相关事件

%% 系统状态记录定义
%% 这个记录结构用于存储整个系统的状态信息
%% 类似于Java中的状态对象或DTO (Data Transfer Object)
%%
%% 在Java中相当于：
%% public class SystemStatus {
%%     private Map<String, Object> modules;
%%     private List<Event> events;
%%     private Map<String, Object> sharedContext;
%%     private long lastUpdate;
%% }
-record(system_status, {
    modules = #{},         % 模块状态映射：存储所有注册模块的状态信息
                          % 类似于Map<String, ModuleStatus>
    events = [],          % 最近事件列表：存储最近发生的系统事件
                          % 类似于List<Event>，用于事件历史和调试
    shared_context = #{}, % 共享上下文：存储模块间共享的配置和状态
                          % 类似于ApplicationContext或全局配置Map
    last_update = 0       % 上次更新时间：记录状态最后更新的时间戳
                          % 类似于long lastModified，用于缓存失效和同步
}).

%%%===================================================================
%%% API - 公共接口函数
%%% 这些函数提供协调器的主要功能，供其他模块调用
%%%===================================================================

%% @doc 启动模块协调器
%% 这个函数启动协调器的gen_server进程，开始提供协调服务
%%
%% 功能说明：
%% 1. 创建一个本地注册的gen_server进程
%% 2. 初始化协调器的内部状态和ETS表
%% 3. 开始监听模块注册和事件通知
%% 4. 启动系统监控循环
%%
%% 返回值：
%% - {ok, Pid}: 启动成功，返回进程ID
%% - {error, Reason}: 启动失败，返回错误原因
%%
%% Java等价概念：
%% 类似于Spring ApplicationContext的启动
%% 或者微服务注册中心的启动（如Eureka Server）
%%
%% 示例：
%% {ok, Pid} = emqx_plugin_mongodb_coordinator:start_link()
start_link() ->
    %% 启动gen_server进程
    %% {local, ?MODULE} 表示本地注册，进程名为模块名
    %% ?MODULE 是当前模块名的宏
    %% [] 是传递给init/1回调的参数列表
    %% 在Java中相当于：
    %% @Service
    %% @PostConstruct
    %% public void startCoordinator() { ... }
    gen_server:start_link({local, ?MODULE}, ?MODULE, [], []).

%% @doc 注册模块到协调器
%% 这个函数允许功能模块向协调器注册自己，以便参与系统协调
%%
%% 功能说明：
%% 1. 验证和标准化模块信息
%% 2. 添加注册时间戳和模块标识
%% 3. 异步发送注册请求到协调器
%% 4. 立即返回，不阻塞调用者
%%
%% 参数说明：
%% - Module: 模块名（原子类型），用于标识模块
%% - Info: 模块信息（映射类型），包含模块的元数据
%%   格式示例：#{
%%     priority => high,
%%     description => <<"Circuit breaker module">>,
%%     features => [failure_detection, auto_recovery]
%%   }
%%
%% 返回值：
%% - ok: 注册请求已发送（异步操作）
%%
%% Java等价概念：
%% 类似于Spring的@Component注解或服务注册
%% 或者微服务的服务注册（如Eureka Client注册）
%%
%% 示例：
%% emqx_plugin_mongodb_coordinator:register_module(
%%     emqx_plugin_mongodb_circuit_breaker,
%%     #{priority => critical, description => <<"Circuit breaker">>}
%% )
register_module(Module, Info) ->
    %% 确保描述信息是映射类型
    %% 如果传入的不是映射，则使用空映射作为默认值
    %% 这是一种防御性编程，确保数据类型的一致性
    %% 在Java中相当于：
    %% Map<String, Object> moduleInfo = (info instanceof Map) ?
    %%     (Map<String, Object>) info : new HashMap<>();
    ModuleInfo = case is_map(Info) of
        true -> Info;
        false -> #{}
    end,

    %% 添加默认字段，增强模块信息
    %% 添加注册时间戳和模块标识，便于追踪和管理
    %% 在Java中相当于：
    %% moduleInfo.put("registration_time", System.currentTimeMillis());
    %% moduleInfo.put("module", module);
    EnhancedInfo = ModuleInfo#{
        registration_time => erlang:system_time(millisecond),  % 注册时间戳
        module => Module                                       % 模块标识
    },

    %% 将模块信息异步发送到协调器
    %% 使用gen_server:cast进行异步调用，不阻塞调用者
    %% 在Java中相当于：
    %% @Async
    %% public void registerModule(String module, Map<String, Object> info) {
    %%     coordinatorService.registerModule(module, info);
    %% }
    gen_server:cast(?MODULE, {register_module, Module, EnhancedInfo}),
    ok.

%% @doc 通知事件
%% 这个函数允许模块向协调器发送事件通知，触发系统级的协调响应
%%
%% 功能说明：
%% 1. 接收模块发送的事件通知
%% 2. 异步传递事件到协调器进行处理
%% 3. 触发相关的系统优化和状态调整
%% 4. 记录事件历史用于分析和调试
%%
%% 参数说明：
%% - ModuleName: 发送事件的模块名
%% - EventType: 事件类型（如backpressure、circuit_breaker等）
%% - EventData: 事件数据，包含事件的详细信息
%%   格式示例：#{
%%     severity => high,
%%     message => <<"High memory usage detected">>,
%%     metrics => #{cpu => 0.8, memory => 0.9}
%%   }
%%
%% 返回值：
%% - ok: 事件通知已发送（异步操作）
%%
%% Java等价概念：
%% 类似于Spring Events的事件发布
%% 或者Guava EventBus的事件发送
%% 或者观察者模式的通知机制
%%
%% 示例：
%% emqx_plugin_mongodb_coordinator:notify_event(
%%     emqx_plugin_mongodb_circuit_breaker,
%%     circuit_breaker,
%%     #{state => open, reason => <<"High error rate">>}
%% )
notify_event(ModuleName, EventType, EventData) ->
    %% 异步发送事件通知到协调器
    %% 使用gen_server:cast确保不阻塞事件发送者
    %% 在Java中相当于：
    %% @EventListener
    %% @Async
    %% public void handleEvent(String module, String eventType, Object eventData) {
    %%     eventBus.post(new SystemEvent(module, eventType, eventData));
    %% }
    gen_server:cast(?MODULE, {notify_event, ModuleName, EventType, EventData}).

%% @doc 获取系统状态
%% 这个函数返回整个插件系统的当前状态信息
%%
%% 功能说明：
%% 1. 收集所有注册模块的状态信息
%% 2. 汇总最近的系统事件
%% 3. 提供系统健康度评估
%% 4. 返回共享上下文信息
%%
%% 返回值：
%% - {ok, SystemStatus}: 成功返回系统状态
%% - {error, Reason}: 获取状态失败
%%
%% SystemStatus格式：
%% #{
%%   modules => #{module_name => module_status, ...},
%%   events => [recent_events...],
%%   shared_context => #{key => value, ...},
%%   last_update => timestamp,
%%   system_health => normal | degraded | critical
%% }
%%
%% Java等价概念：
%% 类似于Spring Actuator的健康检查端点
%% 或者微服务的状态查询接口
%% 或者系统监控的状态聚合
%%
%% 示例：
%% {ok, Status} = emqx_plugin_mongodb_coordinator:get_system_status(),
%% SystemHealth = maps:get(system_health, Status)
get_system_status() ->
    %% 同步调用协调器获取系统状态
    %% 使用gen_server:call进行同步调用，等待返回结果
    %% 在Java中相当于：
    %% @GetMapping("/actuator/health")
    %% public ResponseEntity<SystemStatus> getSystemStatus() {
    %%     return ResponseEntity.ok(coordinatorService.getSystemStatus());
    %% }
    gen_server:call(?MODULE, get_system_status).

%% @doc 获取模块状态
%% 这个函数返回指定模块的当前状态信息
%%
%% 功能说明：
%% 1. 查询指定模块的注册信息和运行状态
%% 2. 返回模块的健康状态和性能指标
%% 3. 提供模块级别的监控和调试信息
%% 4. 支持模块状态的实时查询
%%
%% 参数说明：
%% - ModuleName: 要查询的模块名（原子类型）
%%
%% 返回值：
%% - {ok, ModuleStatus}: 成功返回模块状态
%% - {error, not_found}: 模块未注册
%% - {error, Reason}: 其他错误
%%
%% ModuleStatus格式：
%% #{
%%   module => module_name,
%%   status => running | stopped | error,
%%   registration_time => timestamp,
%%   last_activity => timestamp,
%%   metrics => #{...}
%% }
%%
%% Java等价概念：
%% 类似于Spring Actuator的单个服务健康检查
%% 或者微服务的服务状态查询
%%
%% 示例：
%% {ok, Status} = get_module_status(emqx_plugin_mongodb_circuit_breaker)
get_module_status(ModuleName) ->
    %% 同步调用协调器获取指定模块的状态
    %% 在Java中相当于：
    %% @GetMapping("/actuator/health/{serviceName}")
    %% public ResponseEntity<ServiceStatus> getServiceStatus(@PathVariable String serviceName)
    gen_server:call(?MODULE, {get_module_status, ModuleName}).

%% @doc 更新共享上下文
%% 这个函数更新系统级的共享上下文信息，供所有模块使用
%%
%% 功能说明：
%% 1. 更新全局共享的配置或状态信息
%% 2. 触发相关模块的上下文同步
%% 3. 记录上下文变更历史
%% 4. 异步操作，不阻塞调用者
%%
%% 参数说明：
%% - Key: 上下文键名（原子或二进制类型）
%% - Value: 上下文值（任意类型）
%%
%% 返回值：
%% - ok: 更新请求已发送（异步操作）
%%
%% Java等价概念：
%% 类似于Spring的ApplicationContext属性更新
%% 或者分布式配置中心的配置更新
%% 或者Redis的全局缓存更新
%%
%% 示例：
%% update_shared_context(system_load_threshold, 0.8),
%% update_shared_context(maintenance_mode, false)
update_shared_context(Key, Value) ->
    %% 异步发送上下文更新请求到协调器
    %% 在Java中相当于：
    %% @Async
    %% public void updateGlobalContext(String key, Object value) {
    %%     applicationContext.setAttribute(key, value);
    %%     notifyContextChange(key, value);
    %% }
    gen_server:cast(?MODULE, {update_context, Key, Value}).

%% @doc 获取共享上下文
%% 这个函数获取系统级的共享上下文信息
%%
%% 功能说明：
%% 1. 查询全局共享的配置或状态信息
%% 2. 提供模块间的信息共享机制
%% 3. 支持默认值和类型转换
%% 4. 高性能的上下文查询
%%
%% 参数说明：
%% - Key: 要查询的上下文键名
%%
%% 返回值：
%% - {ok, Value}: 成功返回上下文值
%% - {error, not_found}: 键不存在
%% - {error, Reason}: 其他错误
%%
%% Java等价概念：
%% 类似于Spring的ApplicationContext属性查询
%% 或者分布式配置中心的配置查询
%% 或者Redis的全局缓存查询
%%
%% 示例：
%% {ok, Threshold} = get_shared_context(system_load_threshold),
%% {ok, MaintenanceMode} = get_shared_context(maintenance_mode)
get_shared_context(Key) ->
    %% 同步调用协调器获取共享上下文
    %% 在Java中相当于：
    %% @Cacheable("globalContext")
    %% public Object getGlobalContext(String key) {
    %%     return applicationContext.getAttribute(key);
    %% }
    gen_server:call(?MODULE, {get_context, Key}).

%% @doc 停止模块协调器
%% 这个函数优雅地停止协调器服务，清理所有资源
%%
%% 功能说明：
%% 1. 通知所有注册模块协调器即将停止
%% 2. 保存当前状态和上下文信息
%% 3. 清理ETS表和内存资源
%% 4. 优雅地终止协调器进程
%%
%% 返回值：
%% - ok: 停止成功
%% - {error, Reason}: 停止失败
%%
%% Java等价概念：
%% 类似于Spring ApplicationContext的关闭
%% 或者微服务的优雅停机
%% 或者@PreDestroy注解的清理方法
%%
%% 示例：
%% ok = emqx_plugin_mongodb_coordinator:stop()
stop() ->
    %% 同步调用协调器执行停止操作
    %% 在Java中相当于：
    %% @PreDestroy
    %% public void shutdown() {
    %%     // 清理资源，保存状态
    %%     applicationContext.close();
    %% }
    gen_server:call(?MODULE, stop).

%% @doc 集成到协调器
%% 这个函数用于协调器模块自身的集成，实际上是一个空操作
%%
%% 功能说明：
%% 1. 协调器模块本身不需要向自己注册
%% 2. 记录集成日志，表明协调器已经就绪
%% 3. 提供统一的集成接口，保持API一致性
%% 4. 立即返回成功状态
%%
%% 返回值：
%% - ok: 集成成功（实际上是空操作）
%%
%% Java等价概念：
%% 类似于Spring的自引用或循环依赖处理
%% 或者单例模式的自我初始化
%%
%% 设计说明：
%% 这个函数存在的原因是为了保持API的一致性
%% 所有模块都有integrate/0函数，协调器也不例外
%% 但协调器不需要向自己注册，所以这是一个空操作
integrate() ->
    %% 记录协调器模块已经集成的信息
    %% 这主要是为了日志记录和API一致性
    ?SLOG(info, #{msg => "coordinator_module_already_integrated"}),
    ok.

%%%===================================================================
%%% gen_server callbacks - gen_server行为回调函数
%%% 这些函数实现gen_server行为的标准回调，类似于Java中的接口实现
%%%===================================================================

%% @doc 初始化回调函数
%% 这个函数在协调器启动时被调用，负责初始化协调器的状态和资源
%%
%% 功能说明：
%% 1. 创建高性能的ETS表用于数据存储
%% 2. 初始化协调器的内部状态
%% 3. 启动系统监控和定期检查机制
%% 4. 设置协调器的运行环境
%%
%% 参数说明：
%% - []: 空参数列表，协调器不需要初始化参数
%%
%% 返回值：
%% - {ok, State}: 初始化成功，返回初始状态
%% - {stop, Reason}: 初始化失败，停止进程
%%
%% Java等价概念：
%% 类似于Spring的@PostConstruct注解方法
%% 或者Java构造函数中的初始化逻辑
%% 或者Servlet的init()方法
%%
%% ETS表设计说明：
%% - COORDINATOR_REGISTRY: 协调器注册表，存储系统状态
%% - SHARED_CONTEXT_TAB: 共享上下文表，存储全局配置
%% - MODULE_STATUS_TAB: 模块状态表，存储各模块状态
%% - EVENT_HISTORY_TAB: 事件历史表，存储系统事件记录
init([]) ->
    %% 创建ETS表用于高性能数据存储
    %% ETS (Erlang Term Storage) 类似于Java中的ConcurrentHashMap
    %% 但性能更高，支持原子操作和高并发访问

    %% 创建协调器注册表
    %% named_table: 使用宏名作为表名，便于全局访问
    %% public: 允许其他进程读取，类似于public访问权限
    %% {read_concurrency, true}: 优化并发读取性能
    %% 在Java中相当于：
    %% ConcurrentHashMap<String, Object> coordinatorRegistry = new ConcurrentHashMap<>();
    ets:new(?COORDINATOR_REGISTRY, [named_table, public, {read_concurrency, true}]),

    %% 创建共享上下文表
    %% 用于存储模块间共享的配置和状态信息
    %% 在Java中相当于：
    %% ConcurrentHashMap<String, Object> sharedContext = new ConcurrentHashMap<>();
    ets:new(?SHARED_CONTEXT_TAB, [named_table, public, {read_concurrency, true}]),

    %% 创建模块状态表
    %% {write_concurrency, true}: 优化并发写入性能
    %% 在Java中相当于：
    %% ConcurrentHashMap<String, ModuleStatus> moduleStatusMap = new ConcurrentHashMap<>();
    ets:new(?MODULE_STATUS_TAB, [named_table, public, {write_concurrency, true}]),

    %% 创建事件历史表
    %% ordered_set: 有序集合，按键排序存储
    %% 用于存储系统事件的时间序列数据
    %% 在Java中相当于：
    %% ConcurrentSkipListMap<Long, Event> eventHistory = new ConcurrentSkipListMap<>();
    ets:new(?EVENT_HISTORY_TAB, [named_table, public, ordered_set, {write_concurrency, true}]),

    %% 初始化系统状态记录
    %% 创建协调器的初始状态，包含空的模块列表和事件列表
    %% 在Java中相当于：
    %% SystemStatus initialState = new SystemStatus();
    %% initialState.setLastUpdate(System.currentTimeMillis());
    State = #system_status{
        last_update = erlang:system_time(millisecond)  % 设置初始化时间戳
    },

    %% 将初始状态存储到ETS表中
    %% 这样其他进程也可以访问系统状态
    %% 在Java中相当于：
    %% coordinatorRegistry.put("system_status", initialState);
    ets:insert(?COORDINATOR_REGISTRY, {system_status, State}),

    %% 启动系统监控进程
    %% spawn_link创建一个链接的子进程，如果子进程崩溃，父进程也会收到通知
    %% 类似于Java中启动一个守护线程进行后台监控
    %% 在Java中相当于：
    %% Thread monitorThread = new Thread(this::systemMonitor);
    %% monitorThread.setDaemon(true);
    %% monitorThread.start();
    spawn_link(?MODULE, system_monitor, []),

    %% 启动定期状态检查
    %% erlang:send_after在指定时间后向自己发送消息
    %% 类似于Java中的ScheduledExecutorService
    %% 在Java中相当于：
    %% ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    %% scheduler.scheduleAtFixedRate(this::checkSystemStatus, 5, 5, TimeUnit.SECONDS);
    erlang:send_after(5000, self(), check_system_status),

    %% 返回初始化成功和初始状态
    {ok, State}.

%% @doc 处理同步调用 - 模块注册
%% 这个函数处理模块向协调器的注册请求
%%
%% 功能说明：
%% 1. 将新模块添加到系统模块列表中
%% 2. 更新ETS表中的模块状态信息
%% 3. 更新系统状态和时间戳
%% 4. 同步返回注册结果
%%
%% 参数说明：
%% - {register_module, ModuleName, InitialStatus}: 注册请求消息
%% - _From: 调用者进程信息（未使用）
%% - State: 当前协调器状态
%%
%% Java等价概念：
%% 类似于Spring的Bean注册或服务注册中心的服务注册
handle_call({register_module, ModuleName, InitialStatus}, _From, State) ->
    %% 将新模块添加到模块映射中
    %% 在Java中相当于：
    %% moduleRegistry.put(moduleName, initialStatus);
    NewModules = maps:put(ModuleName, InitialStatus, State#system_status.modules),

    %% 更新ETS表中的模块状态
    %% 这样其他进程也可以查询模块状态
    %% 在Java中相当于：
    %% moduleStatusCache.put(moduleName, initialStatus);
    ets:insert(?MODULE_STATUS_TAB, {ModuleName, InitialStatus}),

    %% 更新协调器的内部状态
    %% 包含新的模块列表和更新时间戳
    %% 在Java中相当于：
    %% systemStatus.setModules(newModules);
    %% systemStatus.setLastUpdate(System.currentTimeMillis());
    NewState = State#system_status{
        modules = NewModules,
        last_update = erlang:system_time(millisecond)
    },

    %% 将更新后的系统状态保存到注册表
    %% 确保状态变更被持久化
    %% 在Java中相当于：
    %% coordinatorRegistry.put("system_status", newState);
    ets:insert(?COORDINATOR_REGISTRY, {system_status, NewState}),

    %% 返回注册成功响应和新状态
    {reply, ok, NewState};

%% @doc 处理同步调用 - 获取系统状态
%% 这个函数返回整个系统的当前状态信息
%%
%% 功能说明：
%% 1. 直接返回当前的系统状态
%% 2. 包含所有模块状态和系统事件
%% 3. 提供系统健康度的实时快照
%%
%% Java等价概念：
%% 类似于Spring Actuator的健康检查端点
handle_call(get_system_status, _From, State) ->
    %% 直接返回当前系统状态
    %% 在Java中相当于：
    %% @GetMapping("/actuator/health")
    %% public SystemStatus getSystemStatus() { return this.systemStatus; }
    {reply, {ok, State}, State};

%% @doc 处理同步调用 - 获取模块状态
%% 这个函数返回指定模块的状态信息
%%
%% 功能说明：
%% 1. 在模块映射中查找指定模块
%% 2. 返回模块的详细状态信息
%% 3. 如果模块不存在，返回错误信息
%%
%% Java等价概念：
%% 类似于服务注册中心的单个服务查询
handle_call({get_module_status, ModuleName}, _From, State) ->
    %% 在模块映射中查找指定模块
    %% maps:find类似于Java中的Map.get()
    %% 在Java中相当于：
    %% Optional<ModuleStatus> status = moduleRegistry.get(moduleName);
    case maps:find(ModuleName, State#system_status.modules) of
        {ok, Status} ->
            %% 模块存在，返回状态信息
            {reply, {ok, Status}, State};
        error ->
            %% 模块不存在，返回错误
            {reply, {error, module_not_found}, State}
    end;

%% @doc 处理同步调用 - 获取共享上下文
%% 这个函数返回指定键的共享上下文值
%%
%% 功能说明：
%% 1. 在共享上下文中查找指定键
%% 2. 返回对应的值
%% 3. 如果键不存在，返回错误信息
%%
%% Java等价概念：
%% 类似于ApplicationContext的属性查询
handle_call({get_context, Key}, _From, State) ->
    %% 在共享上下文中查找指定键
    %% 在Java中相当于：
    %% Object value = applicationContext.getAttribute(key);
    case maps:find(Key, State#system_status.shared_context) of
        {ok, Value} ->
            %% 键存在，返回值
            {reply, {ok, Value}, State};
        error ->
            %% 键不存在，返回错误
            {reply, {error, key_not_found}, State}
    end;

%% @doc 处理同步调用 - 停止协调器
%% 这个函数优雅地停止协调器服务
%%
%% 功能说明：
%% 1. 接收停止请求
%% 2. 返回成功响应
%% 3. 触发gen_server的正常终止流程
%%
%% Java等价概念：
%% 类似于Spring的@PreDestroy或优雅停机
handle_call(stop, _From, State) ->
    %% 返回停止成功并触发正常终止
    %% {stop, normal, ok, State} 表示：
    %% - stop: 停止gen_server
    %% - normal: 正常终止原因
    %% - ok: 返回给调用者的响应
    %% - State: 当前状态
    {stop, normal, ok, State};

%% @doc 处理同步调用 - 未知请求
%% 这个函数处理所有未识别的同步调用请求
%%
%% 功能说明：
%% 1. 捕获所有未定义的调用
%% 2. 返回统一的错误响应
%% 3. 保持系统的健壮性
%%
%% Java等价概念：
%% 类似于Spring MVC的默认错误处理器
handle_call(_Request, _From, State) ->
    %% 返回未知调用错误
    %% 这是一种防御性编程，处理意外的调用
    {reply, {error, unknown_call}, State}.

%% @doc 处理异步调用 - 事件通知
%% 这个函数处理来自各个模块的事件通知，实现系统级的事件协调
%%
%% 功能说明：
%% 1. 接收模块发送的事件通知
%% 2. 记录事件到历史表中
%% 3. 更新系统状态和事件列表
%% 4. 触发相关的系统优化响应
%%
%% 参数说明：
%% - {notify_event, ModuleName, EventType, EventData}: 事件通知消息
%% - State: 当前协调器状态
%%
%% 事件处理流程：
%% 1. 构造标准化的事件记录
%% 2. 存储到事件历史表
%% 3. 更新内存中的事件列表
%% 4. 触发事件响应处理
%%
%% Java等价概念：
%% 类似于Spring Events的事件处理
%% 或者观察者模式的通知处理
%% 或者消息队列的事件消费
handle_cast({notify_event, ModuleName, EventType, EventData}, State) ->
    %% 构造标准化的事件记录
    %% 包含事件的完整上下文信息
    %% 在Java中相当于：
    %% Event event = new Event.Builder()
    %%     .module(moduleName)
    %%     .type(eventType)
    %%     .data(eventData)
    %%     .timestamp(System.currentTimeMillis())
    %%     .build();
    Event = #{
        module => ModuleName,                           % 事件来源模块
        type => EventType,                             % 事件类型
        data => EventData,                             % 事件数据
        timestamp => erlang:system_time(millisecond)   % 事件时间戳
    },

    %% 生成唯一的事件ID并添加到事件历史表
    %% erlang:unique_integer([positive]) 生成唯一的正整数ID
    %% 在Java中相当于：
    %% long eventId = idGenerator.nextId();
    %% eventHistoryRepository.save(eventId, event);
    EventId = erlang:unique_integer([positive]),
    ets:insert(?EVENT_HISTORY_TAB, {EventId, Event}),

    %% 更新系统状态中的事件列表
    %% 保留最近的100个事件，使用lists:sublist限制列表长度
    %% 在Java中相当于：
    %% List<Event> newEvents = new ArrayList<>();
    %% newEvents.add(event);
    %% newEvents.addAll(currentEvents.subList(0, Math.min(99, currentEvents.size())));
    NewEvents = [Event | lists:sublist(State#system_status.events, 99)],
    NewState = State#system_status{
        events = NewEvents,
        last_update = erlang:system_time(millisecond)
    },

    %% 将更新后的系统状态保存到注册表
    %% 确保状态变更对其他进程可见
    %% 在Java中相当于：
    %% coordinatorRegistry.put("system_status", newState);
    ets:insert(?COORDINATOR_REGISTRY, {system_status, NewState}),

    % 传播事件到其他模块
    spawn_link(?MODULE, propagate_event, [ModuleName, EventType, EventData]),

    {noreply, NewState};

handle_cast({update_context, Key, Value}, State) ->
    % 更新共享上下文
    NewContext = maps:put(Key, Value, State#system_status.shared_context),

    % 更新ETS表
    ets:insert(?SHARED_CONTEXT_TAB, {Key, Value}),

    % 更新状态
    NewState = State#system_status{
        shared_context = NewContext,
        last_update = erlang:system_time(millisecond)
    },

    % 更新注册表
    ets:insert(?COORDINATOR_REGISTRY, {system_status, NewState}),

    {noreply, NewState};

handle_cast(_Msg, State) ->
    {noreply, State}.

%% @doc 处理消息
handle_info(check_system_status, State) ->
    % 检查系统状态
    check_and_optimize_system(),

    % 重置定时器
    erlang:send_after(5000, self(), check_system_status),

    {noreply, State};

handle_info(_Info, State) ->
    {noreply, State}.

%% @doc 终止回调
terminate(_Reason, _State) ->
    % 删除ETS表
    catch ets:delete(?COORDINATOR_REGISTRY),
    catch ets:delete(?SHARED_CONTEXT_TAB),
    catch ets:delete(?MODULE_STATUS_TAB),
    catch ets:delete(?EVENT_HISTORY_TAB),

    ok.

%% @doc 代码更新回调
code_change(_OldVsn, State, _Extra) ->
    {ok, State}.

%%%===================================================================
%%% 内部函数
%%%===================================================================

%% @doc 系统监控进程
system_monitor() ->
    try
        % 检查ETS表是否存在，如果不存在说明插件正在停止
        case ets:info(?COORDINATOR_REGISTRY) of
            undefined ->
                % ETS表不存在，停止监控循环
                ?SLOG(info, #{
                    msg => "coordinator_registry_table_not_exists_stopping_system_monitor",
                    table => ?COORDINATOR_REGISTRY
                }),
                ok;
            _ ->
                % ETS表存在，继续监控流程
                try
                    % 获取系统负载
                    SystemLoad = get_system_load(),

                    % 获取内存使用情况
                    MemoryUsage = get_memory_usage(),

                    % 获取连接状态
                    ConnectionStatus = get_connection_status(),

                    % 更新共享上下文
                    update_shared_context(system_load, SystemLoad),
                    update_shared_context(memory_usage, MemoryUsage),
                    update_shared_context(connection_status, ConnectionStatus),

                    % 检查系统状态并优化
                    check_and_optimize_system(),

                    % 定期执行
                    timer:sleep(5000),
                    system_monitor()
                catch
                    error:badarg ->
                        % ETS表在操作过程中被删除，停止监控循环
                        ?SLOG(info, #{
                            msg => "coordinator_registry_table_deleted_during_monitoring_stopping",
                            table => ?COORDINATOR_REGISTRY
                        }),
                        ok;
                    E:R:S ->
                        % 出错时记录日志
                        ?SLOG(error, #{
                            msg => "system_monitor_error",
                            error => E,
                            reason => R,
                            stacktrace => S
                        }),

                        % 检查是否应该继续监控
                        case ets:info(?COORDINATOR_REGISTRY) of
                            undefined ->
                                % ETS表已被删除，停止监控
                                ok;
                            _ ->
                                % ETS表仍存在，使用默认值更新上下文并继续
                                try
                                    update_shared_context(system_load, 0.5),
                                    update_shared_context(memory_usage, 0.5),
                                    update_shared_context(connection_status, normal)
                                catch
                                    _:_ -> ok  % 忽略更新错误
                                end,
                                % 等待后继续监控
                                timer:sleep(5000),
                                system_monitor()
                        end
                end
        end
    catch
        E2:R2:S2 ->
            ?SLOG(error, #{
                msg => "critical_error_in_system_monitor",
                error => E2,
                reason => R2,
                stacktrace => S2
            }),
            % 停止监控循环
            ok
    end.

%% @doc 传播事件到其他模块
propagate_event(SourceModule, EventType, EventData) ->
    % 获取所有注册模块
    ModuleList = ets:tab2list(?MODULE_STATUS_TAB),

    % 对每个模块处理事件
    lists:foreach(
        fun({ModuleName, _Status}) ->
            % 不要传播给源模块
            case ModuleName =/= SourceModule of
                true ->
                    % 根据事件类型和模块选择处理方式
                    handle_event_for_module(ModuleName, EventType, EventData);
                false ->
                    ok
            end
        end,
        ModuleList
    ).

%% @doc 为特定模块处理事件
handle_event_for_module(ModuleName, EventType, EventData) ->
    case {ModuleName, EventType} of
        % 背压事件处理
        {emqx_plugin_mongodb_pipeline, ?EVENT_BACKPRESSURE} ->
            % 通知流水线处理背压事件
            adjust_pipeline_for_backpressure(EventData);

        % 熔断器事件处理
        {emqx_plugin_mongodb_connection, ?EVENT_CIRCUIT_BREAKER} ->
            % 通知连接管理器熔断器状态变化
            adjust_connections_for_circuit_breaker(EventData);

        % 批处理事件处理
        {emqx_plugin_mongodb_zero_copy, ?EVENT_BATCH} ->
            % 通知零拷贝模块批处理参数变化
            adjust_zero_copy_for_batch(EventData);

        % 资源事件处理
        {emqx_plugin_mongodb_backpressure, ?EVENT_RESOURCE} ->
            % 通知背压模块资源状态变化
            adjust_backpressure_for_resource(EventData);

        % 错误事件处理
        {emqx_plugin_mongodb_circuit_breaker, ?EVENT_ERROR} ->
            % 通知熔断器错误事件
            adjust_circuit_breaker_for_error(EventData);

        % 默认情况
        _ ->
            % 对于其他模块和事件组合，不做特殊处理
            ok
    end.

%% @doc 检查和优化系统
check_and_optimize_system() ->
    try
        % 检查ETS表是否存在
        case ets:info(?COORDINATOR_REGISTRY) of
            undefined ->
                % ETS表不存在，可能插件正在停止
                ?SLOG(debug, #{
                    msg => "coordinator_registry_table_not_exists_skipping_optimization",
                    table => ?COORDINATOR_REGISTRY
                }),
                ok;
            _ ->
                % ETS表存在，继续优化流程
                case ets:lookup(?COORDINATOR_REGISTRY, system_status) of
                    [{_, State}] ->
                        % 获取共享上下文
                        SharedContext = State#system_status.shared_context,

                        % 获取系统负载
                        SystemLoad = maps:get(system_load, SharedContext, 0.0),

                        % 获取内存使用率
                        MemoryUsage = maps:get(memory_usage, SharedContext, 0.0),

                        % 获取连接状态
                        ConnectionStatus = maps:get(connection_status, SharedContext, normal),

                        % 根据系统状态进行优化
                        optimize_for_system_state(SystemLoad, MemoryUsage, ConnectionStatus);
                    [] ->
                        % 系统状态记录不存在
                        ?SLOG(debug, #{
                            msg => "system_status_record_not_found_skipping_optimization",
                            table => ?COORDINATOR_REGISTRY,
                            key => system_status
                        }),
                        ok
                end
        end
    catch
        error:badarg ->
            % ETS表在操作过程中被删除
            ?SLOG(debug, #{
                msg => "coordinator_registry_table_deleted_during_operation",
                table => ?COORDINATOR_REGISTRY
            }),
            ok;
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_in_check_and_optimize_system",
                error => E,
                reason => R,
                stacktrace => S
            }),
            ok
    end.

%% @doc 根据系统状态进行优化
optimize_for_system_state(SystemLoad, MemoryUsage, ConnectionStatus) ->
    % 负载高时优化
    case SystemLoad > 0.8 of
        true ->
            % 减少批处理大小
            adjust_batch_size_for_high_load(),
            % 增加批处理超时
            adjust_batch_timeout_for_high_load(),
            % 启用更积极的背压
            enable_aggressive_backpressure();
        false ->
            ok
    end,

    % 内存使用率高时优化
    case MemoryUsage > 0.85 of
        true ->
            % 减少缓存大小
            reduce_cache_size(),
            % 启用内存保护模式
            enable_memory_protection_mode();
        false ->
            ok
    end,

    % 连接状态异常时优化
    case ConnectionStatus of
        degraded ->
            % 启用连接保护模式
            enable_connection_protection_mode();
        critical ->
            % 启用紧急模式
            enable_emergency_mode();
        _ ->
            ok
    end.

%% @doc 获取系统负载
get_system_load() ->
    % 使用recon监控模块获取系统负载
    try
        emqx_plugin_mongodb_monitor:get_system_load()
    catch
        _:_ ->
            ?SLOG(warning, #{msg => "recon_system_load_failed", action => "using_default_load"}),
            0.5  % 使用默认值
    end.

%% @doc 获取内存使用率
get_memory_usage() ->
    % 使用recon监控模块获取内存使用率
    try
        emqx_plugin_mongodb_monitor:get_memory_usage()
    catch
        _:_ ->
            ?SLOG(warning, #{msg => "recon_memory_usage_failed", action => "using_default_memory_usage"}),
            0.5  % 使用默认值
    end.

%% @doc 获取连接状态
get_connection_status() ->
    % 从连接管理模块获取状态
    try
        case emqx_plugin_mongodb_connection:get_overall_health() of
            {ok, Health} when Health > 0.8 -> normal;
            {ok, Health} when Health > 0.5 -> degraded;
            {ok, _} -> critical;
            _ -> normal
        end
    catch
        _:_ -> normal
    end.

%% @doc 为高负载调整批处理大小
adjust_batch_size_for_high_load() ->
    % 获取当前批处理配置
    try
        % 减少批处理大小
        emqx_plugin_mongodb_adaptive_batch:adjust_batch_size(0.7)
    catch
        _:_ -> ok
    end.

%% @doc 为高负载调整批处理超时
adjust_batch_timeout_for_high_load() ->
    % 获取当前批处理配置
    try
        % 增加批处理超时
        emqx_plugin_mongodb_adaptive_batch:adjust_batch_timeout(1.5)
    catch
        _:_ -> ok
    end.

%% @doc 启用更积极的背压
enable_aggressive_backpressure() ->
    % 调整背压阈值
    try
        emqx_plugin_mongodb_backpressure:set_pressure_threshold(queue_length, #{
            ?BACKPRESSURE_LEVEL_MILD => 500,
            ?BACKPRESSURE_LEVEL_MODERATE => 1500,
            ?BACKPRESSURE_LEVEL_HIGH => 3000,
            ?BACKPRESSURE_LEVEL_CRITICAL => 5000
        })
    catch
        _:_ -> ok
    end.

%% @doc 减少缓存大小
reduce_cache_size() ->
    % 清理主题匹配缓存
    try
        ets:delete_all_objects(?TOPIC_MATCH_CACHE_TAB),
        ets:delete_all_objects(?TOPIC_MATCH_CACHE_RECENT_TAB)
    catch
        _:_ -> ok
    end.

%% @doc 启用内存保护模式
enable_memory_protection_mode() ->
    % 更新共享上下文
    update_shared_context(memory_protection, true),

    % 调整零拷贝模块
    try
        % 这里假设零拷贝模块有一个函数可以启用内存保护模式
        % 实际实现时需要添加此函数
        emqx_plugin_mongodb_zero_copy:enable_memory_protection()
    catch
        _:_ -> ok
    end.

%% @doc 启用连接保护模式
enable_connection_protection_mode() ->
    % 更新共享上下文
    update_shared_context(connection_protection, true),

    % 调整连接管理
    try
        % 减少最大连接数
        emqx_plugin_mongodb_connection:limit_connections(0.7)
    catch
        _:_ -> ok
    end.

%% @doc 启用紧急模式
enable_emergency_mode() ->
    % 更新共享上下文
    update_shared_context(emergency_mode, true),

    % 通知所有模块
    notify_event(?MODULE, emergency_mode, #{
        reason => "系统处于紧急状态",
        timestamp => erlang:system_time(millisecond)
    }).

%% @doc 为背压调整流水线
adjust_pipeline_for_backpressure(EventData) ->
    % 获取背压级别
    Level = maps:get(level, EventData, ?BACKPRESSURE_LEVEL_NORMAL),

    % 根据背压级别调整流水线
    case Level of
        ?BACKPRESSURE_LEVEL_NORMAL ->
            % 正常状态，不需要调整
            ok;
        ?BACKPRESSURE_LEVEL_MILD ->
            % 轻微背压，减少工作进程数量
            try emqx_plugin_mongodb_pipeline:adjust_workers(0.9) catch _:_ -> ok end;
        ?BACKPRESSURE_LEVEL_MODERATE ->
            % 中等背压，进一步减少工作进程
            try emqx_plugin_mongodb_pipeline:adjust_workers(0.7) catch _:_ -> ok end;
        ?BACKPRESSURE_LEVEL_HIGH ->
            % 高背压，大幅减少工作进程
            try emqx_plugin_mongodb_pipeline:adjust_workers(0.5) catch _:_ -> ok end;
        ?BACKPRESSURE_LEVEL_CRITICAL ->
            % 临界背压，最小化工作进程
            try emqx_plugin_mongodb_pipeline:adjust_workers(0.3) catch _:_ -> ok end
    end.

%% @doc 为熔断器调整连接
adjust_connections_for_circuit_breaker(EventData) ->
    % 获取熔断器状态
    State = maps:get(state, EventData, closed),

    % 根据熔断器状态调整连接
    case State of
        closed ->
            % 熔断器关闭，正常操作
            ok;
        half_open ->
            % 熔断器半开，限制连接
            try emqx_plugin_mongodb_connection:limit_connections(0.5) catch _:_ -> ok end;
        open ->
            % 熔断器开启，最小化连接
            try emqx_plugin_mongodb_connection:limit_connections(0.2) catch _:_ -> ok end
    end.

%% @doc 为批处理调整零拷贝
adjust_zero_copy_for_batch(EventData) ->
    % 获取批处理参数
    BatchSize = maps:get(batch_size, EventData, ?DEFAULT_BATCH_SIZE),

    % 调整零拷贝缓冲区大小
    try
        % 这里假设零拷贝模块有一个函数可以调整缓冲区大小
        % 实际实现时需要添加此函数
        emqx_plugin_mongodb_zero_copy:adjust_buffer_size(BatchSize)
    catch
        _:_ -> ok
    end.

%% @doc 为资源调整背压
adjust_backpressure_for_resource(EventData) ->
    % 获取资源状态
    MemoryUsage = maps:get(memory_usage, EventData, 0.0),
    CpuUsage = maps:get(cpu_usage, EventData, 0.0),

    % 根据资源状态调整背压阈值
    try
        % 内存使用率高时，降低队列长度阈值
        case MemoryUsage > 0.8 of
            true ->
                emqx_plugin_mongodb_backpressure:set_pressure_threshold(queue_length, #{
                    ?BACKPRESSURE_LEVEL_MILD => 500,
                    ?BACKPRESSURE_LEVEL_MODERATE => 1000,
                    ?BACKPRESSURE_LEVEL_HIGH => 2000,
                    ?BACKPRESSURE_LEVEL_CRITICAL => 3000
                });
            false ->
                ok
        end,

        % CPU使用率高时，提高处理时间阈值
        case CpuUsage > 0.8 of
            true ->
                emqx_plugin_mongodb_backpressure:set_pressure_threshold(processing_time, #{
                    ?BACKPRESSURE_LEVEL_MILD => 150,
                    ?BACKPRESSURE_LEVEL_MODERATE => 400,
                    ?BACKPRESSURE_LEVEL_HIGH => 700,
                    ?BACKPRESSURE_LEVEL_CRITICAL => 1500
                });
            false ->
                ok
        end
    catch
        _:_ -> ok
    end.

%% @doc 为错误调整熔断器
adjust_circuit_breaker_for_error(EventData) ->
    % 获取错误类型
    ErrorType = maps:get(type, EventData, ?ERROR_TYPE_UNKNOWN),

    % 根据错误类型调整熔断器阈值
    try
        case ErrorType of
            ?ERROR_TYPE_CONNECTION ->
                % 连接错误，降低阈值
                emqx_plugin_mongodb_circuit_breaker:update_config(global, #{
                    failure_threshold => 3,
                    reset_timeout => 60000
                });
            ?ERROR_TYPE_TIMEOUT ->
                % 超时错误，增加重置超时
                emqx_plugin_mongodb_circuit_breaker:update_config(global, #{
                    reset_timeout => 120000
                });
            _ ->
                ok
        end
    catch
        _:_ -> ok
    end.
