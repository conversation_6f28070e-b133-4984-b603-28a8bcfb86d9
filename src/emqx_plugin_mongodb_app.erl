% MongoDB插件应用模块
% @module emqx_plugin_mongodb_app
% 这是一个Erlang模块声明，类似于Java中的 `package com.example; public class EmqxPluginMongodbApp { ... }`
-module(emqx_plugin_mongodb_app).

% 实现OTP应用行为
% @behaviour application
% 这声明了该模块实现OTP `application` 行为，类似于Java中实现一个特定的接口，如 `Runnable` 或 `Callable`。
% `application` 行为定义了OTP应用程序的启动和停止回调。
-behaviour(application).

% 声明为EMQX插件
% @emqx_plugin ?MODULE
% 这是一个EMQX特定的宏或属性，用于将此模块注册为EMQX插件。
% `?MODULE` 是一个预定义宏，代表当前模块名 `emqx_plugin_mongodb_app`。
-emqx_plugin(?MODULE).

% 引入EMQX日志头文件和插件自定义头文件
% 类似于Java中的 `import com.example.Logger;` 和 `import static com.example.Constants.*;`
-include("emqx_plugin_mongodb.hrl").     % 引入此插件自定义的记录和宏定义

% 导出start/2和stop/1函数
% @export [start/2, stop/1]
% 声明模块的公共API，类似于Java中 `public` 方法。
% `start/2` 表示名为 `start` 的函数，它接受2个参数。
% `stop/1` 表示名为 `stop` 的函数，它接受1个参数。
% 这些是 `application` 行为必须实现的回调函数。
-export([
    start/2,
    stop/1
]).

% 导出配置文件管理函数
-export([
    delete_config_file/0,
    uninstall/0
]).

% 导出gen_server回调函数
-export([
    handle_info/2
]).

% 导出辅助函数
-export([
    verify_mongodb_resource_available/0,
    parse_delay_config/2
]).

% 启动应用
% @spec start(_StartType :: atom(), _StartArgs :: term()) -> {ok, SupPid :: pid()} | {error, Reason :: term()}
% _StartType - 启动类型 (例如 normal, takeover, failover)
% _StartArgs - 启动参数 (通常在 .app 文件中配置)
% @return {ok, SupervisorPid} 如果成功，其中 SupervisorPid 是顶级监督者的进程ID。
% @return {error, Reason} 如果失败。
% 类似于Java Spring Boot应用的 `main` 方法或 `ApplicationRunner` 的 `run` 方法，是应用的入口点。
start(_StartType, _StartArgs) ->
    % 使用EMQX的日志宏记录一条info级别的日志。
    % `#{msg => "..."}` 是一个Erlang的map数据结构，类似于Java的 `Map<String, Object>`。
    ?SLOG(info, #{msg => "starting_mongodb_plugin"}),

    % 尝试启动os_mon应用程序，用于系统监控
    case application:ensure_all_started(os_mon) of
        {ok, _} ->
            ?SLOG(info, #{msg => "os_mon_application_started_successfully"});
        {error, OsMonReason} ->
            ?SLOG(warning, #{msg => "failed_to_start_os_mon_application", reason => OsMonReason})
    end,

    % 复制配置文件到etc目录（如果需要）
    copy_config_to_etc(),

    % 读取配置
    Env = emqx_plugin_mongodb:read_config(),

    % 启动监督树
    % `emqx_plugin_mongodb_sup:start_link()` 会启动此插件的顶级监督者。
    % 监督者 (Supervisor) 是OTP的核心组件，用于监控子进程并在它们失败时重启它们，实现容错。
    % 类似于Java中使用ExecutorService管理线程池，但OTP监督者功能更强大，包含重启策略等。
    % 类似于Java中使用ExecutorService管理线程池，但OTP监督者功能更强大，包含重启策略等。
    case emqx_plugin_mongodb_sup:start_link() of
        {ok, Sup} -> % 如果监督者成功启动，Sup是监督者的进程ID (Pid)
            handle_supervisor_started(Sup); % 调用辅助函数处理后续逻辑
        {error, Reason} -> % 如果启动失败
            ?SLOG(error, #{msg => "failed_to_start_mongodb_supervisor", reason => Reason}),
            {error, Reason} % 返回错误
    end.

%% @doc 处理监督树启动成功后的逻辑
%% 这个函数在监督者成功启动后执行，负责初始化插件的核心功能
%%
%% 功能说明：
%% 1. 加载MongoDB插件的核心逻辑和配置
%% 2. 注册命令行接口和记录启动日志
%% 3. 异步启动各个功能模块（连接、持久化等）
%% 4. 处理启动过程中的错误和异常
%%
%% 参数说明：
%% - Sup: 监督者进程ID，类似于Java中的ThreadPoolExecutor实例
%%
%% 返回值：
%% - {ok, Sup}: 启动成功，返回监督者进程ID
%% - {error, Reason}: 启动失败，返回错误原因
%%
%% Java等价概念：
%% 类似于Spring Boot中的@PostConstruct方法或ApplicationRunner.run()
%% 在主要组件启动后执行初始化逻辑
handle_supervisor_started(Sup) ->
    %% 加载MongoDB插件的核心逻辑，包括读取配置、初始化连接池等
    %% emqx_plugin_mongodb:load/1类似于Java中的配置加载和Bean初始化
    %% 在Java中相当于：
    %% @PostConstruct
    %% public void initialize() {
    %%     Configuration config = configLoader.readConfig();
    %%     mongoService.load(config);
    %% }
    case emqx_plugin_mongodb:load(emqx_plugin_mongodb:read_config()) of
        {ok, _} -> % 加载成功，_表示不关心具体的返回值
            %% 注册命令行接口并记录启动日志
            %% 类似于Java中注册MBean或REST端点
            register_cli_and_log(),

            %% 获取配置信息，用于后续的模块启动
            %% 类似于Java中的@Value注入或@ConfigurationProperties
            Env = emqx_plugin_mongodb:read_config(),

            % 使用spawn_link异步启动各个模块，避免阻塞启动过程
            spawn_link(fun() -> start_modules_async(Env) end),

            % 立即返回成功，不等待所有资源初始化完成
            {ok, Sup}; % 返回成功和监督者Pid
        ok -> % 有些函数可能只返回 `ok` 原子表示成功
            register_cli_and_log(),
            {ok, Sup};
        {error, Reason} -> % 如果加载失败
            ?SLOG(error, #{msg => "failed_to_load_mongodb_plugin", reason => Reason}),
            {error, Reason};
        Other -> % 捕获其他未预期的返回值
            ?SLOG(error, #{msg => "unexpected_error_loading_mongodb_plugin", error => Other}),
            {error, {unexpected_error, Other}}
    end.

%% @doc 异步启动各个模块
%% 这个函数在独立的进程中异步启动插件的各个功能模块
%%
%% 功能说明：
%% 1. 按顺序启动各个功能模块，确保依赖关系正确
%% 2. 使用异步方式避免阻塞主启动流程
%% 3. 提供完整的错误处理和日志记录
%% 4. 预热连接和初始化持久化功能
%%
%% 参数说明：
%% - Env: 配置环境映射，包含所有模块的配置信息
%%
%% 返回值：
%% - ok: 所有模块启动成功
%%
%% Java等价概念：
%% 类似于Spring Boot中的@Async方法或CompletableFuture.runAsync()
%% 在后台线程中初始化各个服务组件
start_modules_async(Env) ->
    %% 记录异步启动开始的日志
    ?SLOG(info, #{msg => "starting_mongodb_plugin_modules_async"}),

    try
        %% 启动稳定性增强模块，包括熔断器、错误处理等
        %% 类似于Java中的Hystrix或Resilience4j组件
        %% 在Java中相当于：
        %% @Component
        %% public class StabilityModules {
        %%     @PostConstruct
        %%     public void initialize() { ... }
        %% }
        start_stability_modules(Env),

        %% 启动连接管理优化模块
        %% 负责MongoDB连接池的管理、健康检查、故障转移等
        %% 类似于Java中的HikariCP或Apache Commons DBCP
        %% 在Java中相当于：
        %% @Service
        %% public class MongoConnectionManager {
        %%     public void startConnectionPool() { ... }
        %% }
        {ok, _} = emqx_plugin_mongodb_connection:start_link(Env),

        %% 启动自适应批处理模块
        %% 根据系统负载动态调整批处理大小，优化性能
        %% 类似于Java中的自适应线程池或批处理框架
        %% 在Java中相当于：
        %% @Component
        %% public class AdaptiveBatchProcessor {
        %%     public void startBatchProcessing() { ... }
        %% }
        {ok, _} = emqx_plugin_mongodb_adaptive_batch:start_link(),

        %% 初始化零拷贝模块
        %% 优化内存使用，减少数据复制开销
        %% 类似于Java NIO的DirectByteBuffer或Netty的零拷贝
        %% 在Java中相当于：
        %% ZeroCopyOptimizer.initialize();
        ok = emqx_plugin_mongodb_zero_copy:init(),

        %% 启动模块协调器
        %% 协调各个模块之间的交互和数据流
        %% 类似于Java中的EventBus或消息中介者模式
        %% 在Java中相当于：
        %% @Component
        %% public class ModuleCoordinator {
        %%     public void startCoordination() { ... }
        %% }
        {ok, _} = emqx_plugin_mongodb_coordinator:start_link(),

        %% 集成所有模块到协调器
        %% 建立模块间的通信通道和依赖关系
        %% 类似于Java Spring的依赖注入和Bean装配
        try_integrate_modules(),

        %% 扩展API集成模块已移除（之前删除的模块）
        ?SLOG(info, #{msg => "api_integration_module_removed"}),

        %% 启动并行处理管道
        %% 提供高并发的数据处理能力
        %% 类似于Java中的CompletableFuture管道或RxJava流
        %% 在Java中相当于：
        %% @Service
        %% public class ParallelPipeline {
        %%     public void startPipeline() { ... }
        %% }
        {ok, _} = emqx_plugin_mongodb_pipeline:start_link(),

        %% 预热连接
        %% 提前建立MongoDB连接，减少首次请求延迟
        %% 类似于Java中的连接池预热或缓存预加载
        %% 在Java中相当于：
        %% connectionPool.preheatConnections();
        emqx_plugin_mongodb_connection:preheat_connections(),

        %% 不在这里初始化持久化模块，等待连接建立后再初始化
        %% 采用事件驱动的初始化策略，确保连接可用后再启动持久化功能
        ?SLOG(info, #{msg => "skipping_early_persistence_initialization_waiting_for_connection"}),

        %% 基于源码分析的最佳解决方案：使用EMQX跟踪点系统
        %% 发现：EMQX资源管理器在连接成功时会触发 ?tp(resource_connected_enter, #{})
        %% 解决方案：订阅EMQX跟踪点，监听资源连接事件
        spawn_link(fun() ->
            ?SLOG(info, #{msg => "starting_emqx_tracepoint_based_data_restoration_trigger"}),
            ?SLOG(info, #{msg => "using_emqx_resource_connected_enter_tracepoint"}),

            %% 使用EMQX跟踪点系统监听资源连接事件
            setup_tracepoint_based_trigger(Env)
        end)
    catch
        %% 捕获启动过程中的所有异常
        %% 在Java中相当于：
        %% try {
        %%     startAllModules();
        %% } catch (Exception e) {
        %%     logger.error("Failed to start modules", e);
        %% }
        Error:Reason:Stacktrace ->
            ?SLOG(error, #{
                msg => "error_starting_mongodb_plugin_modules_async",
                error => Error,           % 异常类型
                reason => Reason,         % 错误原因
                stacktrace => Stacktrace  % 堆栈跟踪
            })
    end.

%% @doc 尝试集成模块到协调器
%% 这个函数尝试将所有启动的模块集成到协调器中，建立模块间的通信
%%
%% 功能说明：
%% 1. 检查集成模块是否可用
%% 2. 调用集成函数建立模块间的连接
%% 3. 处理集成过程中的错误
%%
%% 返回值：
%% - ok: 集成成功或模块不可用
%%
%% Java等价概念：
%% 类似于Spring的ApplicationContext装配Bean依赖关系
%% 或者依赖注入容器的初始化过程
try_integrate_modules() ->
    try
        %% 检查集成模块是否导出了integrate_all_modules/0函数
        %% erlang:function_exported/3类似于Java的反射检查方法是否存在
        %% 在Java中相当于：
        %% if (IntegrationModule.class.getMethod("integrateAllModules") != null) {
        %%     IntegrationModule.integrateAllModules();
        %% }
        case erlang:function_exported(emqx_plugin_mongodb_coordinator_integration, integrate_all_modules, 0) of
            true ->
                %% 函数存在，调用集成方法
                emqx_plugin_mongodb_coordinator_integration:integrate_all_modules();
            false ->
                %% 函数不存在，记录警告但不影响启动
                ?SLOG(warning, #{msg => "integration_module_not_available"})
        end
    catch
        %% 捕获集成过程中的异常
        Error:Reason:Stacktrace ->
            ?SLOG(error, #{
                msg => "failed_to_integrate_modules",
                error => Error,
                reason => Reason,
                stacktrace => Stacktrace
            })
    end.

%% @doc 设置资源事件监听器
%% 这个函数设置MongoDB连接状态的事件监听器，在连接建立后初始化持久化模块
%%
%% 功能说明：
%% 1. 检查MongoDB连接是否已经建立
%% 2. 如果已建立，立即初始化持久化模块
%% 3. 如果未建立，设置事件监听器等待连接建立
%% 4. 提供轮询机制作为事件订阅的回退方案
%%
%% 参数说明：
%% - Env: 配置环境映射，包含持久化模块的配置信息
%%
%% 返回值：
%% - ok: 监听器设置成功
%%
%% Java等价概念：
%% 类似于Spring的ApplicationEventListener或观察者模式
%% 监听数据库连接状态变化事件
-spec setup_resource_event_listener(map()) -> ok.
setup_resource_event_listener(Env) ->
    ?SLOG(info, #{msg => "deprecated_setup_resource_event_listener_redirecting_to_tracepoint_trigger"}),
    % 重定向到基于跟踪点的触发机制
    setup_tracepoint_based_trigger(Env).

%% @doc 设置资源事件订阅
-spec setup_resource_event_subscription(map()) -> ok | {error, term()}.
setup_resource_event_subscription(Env) ->
    try
        % 启动一个进程来监听资源状态变化
        EventListenerPid = spawn_link(fun() ->
            resource_event_subscription_loop(Env)
        end),

        % 注册进程名以便后续管理
        case catch register(mongodb_resource_event_listener, EventListenerPid) of
            true ->
                ?SLOG(info, #{msg => "resource_event_listener_registered", pid => EventListenerPid}),
                ok;
            {'EXIT', {badarg, _}} ->
                % 进程名已存在，说明已经有监听器在运行
                ?SLOG(info, #{msg => "resource_event_listener_already_running"}),
                ok;
            Error ->
                ?SLOG(error, #{msg => "failed_to_register_resource_event_listener", error => Error}),
                {error, registration_failed}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_setting_up_resource_event_subscription",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 资源事件订阅循环
-spec resource_event_subscription_loop(map()) -> ok.
resource_event_subscription_loop(Env) ->
    resource_event_subscription_loop(Env, 180). % 最多等待360秒 (180次 × 2秒)

%% @doc 资源事件订阅循环（带重试计数）
-spec resource_event_subscription_loop(map(), integer()) -> ok.
resource_event_subscription_loop(_Env, 0) ->
    ?SLOG(error, #{msg => "resource_event_subscription_timeout_giving_up"}),
    ok;
resource_event_subscription_loop(Env, Retries) ->
    % 每2秒检查一次连接状态
    timer:sleep(2000),

    case check_connection_already_established() of
        true ->
            ?SLOG(info, #{msg => "mongodb_connection_event_detected_via_subscription"}),
            % 连接建立，立即初始化
            init_persistence_modules(Env),
            ?SLOG(info, #{msg => "persistence_modules_initialized_after_connection_event"}),

            % 修复：在初始化后触发数据恢复
            % 这确保在MongoDB连接建立后能够恢复持久化数据
            ?SLOG(info, #{msg => "triggering_data_restoration_after_persistence_initialization"}),
            spawn(fun() ->
                % 优化：使用智能等待替代固定2秒延迟
                timer:sleep(1000), % 简单等待1秒
                catch emqx_plugin_mongodb_api:trigger_data_restoration_on_connection()
            end),

            % 取消注册监听器
            case catch unregister(mongodb_resource_event_listener) of
                true ->
                    ?SLOG(info, #{msg => "resource_event_listener_unregistered"});
                _ ->
                    ok
            end,
            ok;
        false ->
            ?SLOG(debug, #{msg => "mongodb_connection_not_ready_via_subscription", retries_left => Retries}),
            resource_event_subscription_loop(Env, Retries - 1)
    end.

%% @doc 检查连接是否已经建立
-spec check_connection_already_established() -> boolean().
check_connection_already_established() ->
    try
        % 使用简单的资源健康检查而不是查询
        case emqx_resource:health_check(?PLUGIN_MONGODB_RESOURCE_ID) of
            ok -> true;
            {ok, _} -> true;
            _ -> false
        end
    catch
        _:_ -> false
    end.

%% @doc 资源事件监听循环
-spec resource_event_listener_loop(map()) -> ok.
resource_event_listener_loop(Env) ->
    resource_event_listener_loop(Env, 120). % 最多等待240秒 (120次 × 2秒)

%% @doc 资源事件监听循环（带重试计数）
-spec resource_event_listener_loop(map(), integer()) -> ok.
resource_event_listener_loop(_Env, 0) ->
    ?SLOG(error, #{msg => "resource_event_listener_timeout_giving_up"}),
    ok;
resource_event_listener_loop(Env, Retries) ->
    % 每2秒检查一次连接状态
    timer:sleep(2000),

    case check_connection_already_established() of
        true ->
            ?SLOG(info, #{msg => "mongodb_connection_event_detected_initializing_persistence"}),
            % 连接建立，等待一小段时间确保稳定
            timer:sleep(1000),

            % 在初始化持久化模块前，先检测MongoDB版本
            detect_and_store_mongodb_version(),

            init_persistence_modules(Env),
            ?SLOG(info, #{msg => "persistence_modules_initialized_after_connection_event"}),

            % 修复：在初始化后触发数据恢复
            % 这确保在MongoDB连接建立后能够恢复持久化数据
            ?SLOG(info, #{msg => "triggering_data_restoration_after_persistence_initialization"}),
            spawn(fun() ->
                % 优化：使用智能等待替代固定2秒延迟
                timer:sleep(1000), % 简单等待1秒
                catch emqx_plugin_mongodb_api:trigger_data_restoration_on_connection()
            end),
            ok;
        false ->
            ?SLOG(debug, #{msg => "mongodb_connection_not_ready_continuing_to_listen", retries_left => Retries}),
            resource_event_listener_loop(Env, Retries - 1)
    end.

%% @doc 基于EMQX跟踪点的数据恢复触发机制
%% 利用EMQX资源管理器的resource_connected_enter跟踪点
%% 这是基于源码分析的最佳解决方案
-spec setup_tracepoint_based_trigger(map()) -> ok.
setup_tracepoint_based_trigger(Env) ->
    ?SLOG(info, #{msg => "setting_up_tracepoint_subscription_for_resource_connected_enter"}),

    % 首先检查MongoDB是否已经连接
    case check_mongodb_already_connected() of
        true ->
            ?SLOG(info, #{msg => "mongodb_already_connected_triggering_immediate_data_restoration"}),
            trigger_data_restoration_immediately(Env);
        false ->
            ?SLOG(info, #{msg => "mongodb_not_connected_setting_up_tracepoint_listener"}),
            % 设置跟踪点监听器
            setup_resource_connected_tracepoint_listener(Env),
            % 同时启动备用轮询机制，防止跟踪点失效
            setup_backup_polling_trigger(Env)
    end.

%% @doc 检查MongoDB是否已经连接
-spec check_mongodb_already_connected() -> boolean().
check_mongodb_already_connected() ->
    try
        case emqx_resource:health_check(?PLUGIN_MONGODB_RESOURCE_ID) of
            ok -> true;
            {ok, _} -> true;
            _ -> false
        end
    catch
        _:_ -> false
    end.

%% @doc 立即触发数据恢复
-spec trigger_data_restoration_immediately(map()) -> ok.
trigger_data_restoration_immediately(Env) ->
    ?SLOG(info, #{msg => "triggering_immediate_data_restoration"}),

    % 初始化持久化模块
    init_persistence_modules(Env),

    % 触发数据恢复
    spawn(fun() ->
        ?SLOG(info, #{msg => "immediate_data_restoration_started"}),
        catch emqx_plugin_mongodb_api:trigger_data_restoration_on_connection()
    end),
    ok.

%% @doc 设置资源连接跟踪点监听器
-spec setup_resource_connected_tracepoint_listener(map()) -> ok.
setup_resource_connected_tracepoint_listener(Env) ->
    ?SLOG(info, #{msg => "setting_up_resource_connected_tracepoint_listener"}),

    % 使用EMQX的跟踪点系统监听resource_connected_enter事件
    % 这是基于emqx_resource_manager.erl源码分析的结果
    try
        % 订阅跟踪点（如果EMQX支持）
        case erlang:function_exported(snabbkaffe, subscribe, 1) of
            true ->
                ?SLOG(info, #{msg => "subscribing_to_resource_connected_enter_tracepoint"}),
                % 使用简化的跟踪点订阅，避免宏依赖
                try
                    snabbkaffe:subscribe(
                        fun(Event) ->
                            case Event of
                                #{kind := resource_connected_enter} ->
                                    ?SLOG(info, #{msg => "received_resource_connected_enter_tracepoint"}),
                                    trigger_data_restoration_immediately(Env);
                                _ ->
                                    ok
                            end
                        end
                    )
                catch
                    _:_ ->
                        ?SLOG(warning, #{msg => "snabbkaffe_subscription_failed"})
                end;
            false ->
                ?SLOG(warning, #{msg => "snabbkaffe_not_available_falling_back_to_polling"})
        end
    catch
        _:_ ->
            ?SLOG(warning, #{msg => "tracepoint_subscription_failed_using_polling_only"})
    end,
    ok.

%% @doc 备用轮询触发机制
%% 防止跟踪点系统失效时的备用方案
-spec setup_backup_polling_trigger(map()) -> ok.
setup_backup_polling_trigger(Env) ->
    ?SLOG(info, #{msg => "setting_up_backup_polling_trigger"}),

    spawn(fun() ->
        % 等待10秒后开始轮询，给跟踪点系统时间工作
        timer:sleep(10000),
        backup_polling_loop(Env, 20) % 最多轮询20次（20秒）
    end),
    ok.

%% @doc 备用轮询循环
-spec backup_polling_loop(map(), integer()) -> ok.
backup_polling_loop(_Env, 0) ->
    ?SLOG(warning, #{msg => "backup_polling_timeout_data_restoration_may_not_have_triggered"}),
    ok;
backup_polling_loop(Env, Retries) ->
    timer:sleep(1000),

    case check_mongodb_already_connected() of
        true ->
            ?SLOG(info, #{msg => "backup_polling_detected_mongodb_connection_triggering_data_restoration"}),
            trigger_data_restoration_immediately(Env);
        false ->
            backup_polling_loop(Env, Retries - 1)
    end.

%% @doc 监听MongoDB连接状态并在连接建立后初始化持久化模块（已弃用）
-spec monitor_connection_and_init_persistence(map()) -> ok.
monitor_connection_and_init_persistence(Env) ->
    ?SLOG(info, #{msg => "deprecated_monitor_function_redirecting_to_event_listener"}),
    setup_resource_event_listener(Env).



% 等待MongoDB资源完全可用
% @spec wait_for_mongodb_resource_available(Retries :: integer()) -> ok | {error, Reason :: term()}
wait_for_mongodb_resource_available(_Retries) ->
    % 这个函数已被弃用，使用新的连接监听机制
    ?SLOG(info, #{msg => "deprecated_function_called_returning_success"}),
    ok.

%% @doc 验证MongoDB连接稳定性
-spec verify_mongodb_connection_stable() -> ok | {error, term()}.
verify_mongodb_connection_stable() ->
    try
        % 尝试多次ping来验证连接稳定性
        verify_connection_attempts(3)
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_verifying_mongodb_connection_stability",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {E, R}}
    end.

%% @doc 多次尝试验证连接
-spec verify_connection_attempts(integer()) -> ok | {error, term()}.
verify_connection_attempts(0) ->
    {error, max_verification_attempts_reached};
verify_connection_attempts(Attempts) ->
    case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID, {call, ping}) of
        {ok, _} ->
            ?SLOG(debug, #{msg => "mongodb_ping_successful", attempts_left => Attempts}),
            % 短暂等待后再次验证
            timer:sleep(500),
            case Attempts > 1 of
                true -> verify_connection_attempts(Attempts - 1);
                false -> ok
            end;
        {async_return, {ok, _}} ->
            ?SLOG(debug, #{msg => "mongodb_ping_successful_async", attempts_left => Attempts}),
            % 短暂等待后再次验证
            timer:sleep(500),
            case Attempts > 1 of
                true -> verify_connection_attempts(Attempts - 1);
                false -> ok
            end;
        {async_return, {error, Reason}} ->
            ?SLOG(warning, #{
                msg => "mongodb_ping_failed_async_during_verification",
                reason => Reason,
                attempts_left => Attempts
            }),
            case Attempts > 1 of
                true ->
                    timer:sleep(1000),
                    verify_connection_attempts(Attempts - 1);
                false ->
                    {error, {ping_failed, Reason}}
            end;
        {error, Reason} ->
            ?SLOG(warning, #{
                msg => "mongodb_ping_failed_during_verification",
                reason => Reason,
                attempts_left => Attempts
            }),
            case Attempts > 1 of
                true ->
                    timer:sleep(1000),
                    verify_connection_attempts(Attempts - 1);
                false ->
                    {error, {ping_failed, Reason}}
            end
    end.

% 初始化所有持久化模块
% @spec init_persistence_modules(Config :: map()) -> ok
init_persistence_modules(Env) ->
    % 修复：将配置保存到application环境变量中，供数据恢复函数使用
    application:set_env(emqx_plugin_mongodb, config, Env),

    % 首先获取会话配置，因为其他模块可能需要引用
    SessionConfig = maps:get(session_persistence, Env, #{}),
    SessionPersistenceEnabled = maps:get(enabled, SessionConfig, false),

    % 初始化和加载消息持久化模块
    MessageConfig = maps:get(message_persistence, Env, #{}),
    MessagePersistenceEnabled = maps:get(enabled, MessageConfig, false),

    % 设置应用程序环境变量，供消息模块使用
    application:set_env(emqx_plugin_mongodb, message_persistence_enabled, MessagePersistenceEnabled),

    case MessagePersistenceEnabled of
        true ->
            ?SLOG(info, #{msg => "initializing_message_persistence_module"}),
            % 初始化消息持久化模块
            ok = emqx_plugin_mongodb_message:init(),
            % 加载消息持久化配置
            ok = emqx_plugin_mongodb_message:load(Env),
            % 消息恢复现在通过MongoDB连接事件触发，不再使用时间延迟
            ?SLOG(info, #{msg => "message_restoration_will_be_triggered_on_mongodb_connection"});
        false ->
            ?SLOG(info, #{msg => "message_persistence_disabled"})
    end,

    % 初始化和加载会话持久化模块
    case SessionPersistenceEnabled of
        true ->
            ?SLOG(info, #{msg => "initializing_session_persistence_module"}),
            % 初始化会话持久化模块
            ok = emqx_plugin_mongodb_session:init(),
            % 加载会话持久化配置
            ok = emqx_plugin_mongodb_session:load(Env),
            % 会话恢复现在通过MongoDB连接事件触发，不再使用时间延迟
            ?SLOG(info, #{msg => "session_restoration_will_be_triggered_on_mongodb_connection"});
        false ->
            ?SLOG(info, #{msg => "session_persistence_disabled"})
    end,

    % 初始化和加载订阅持久化模块
    SubscriptionConfig = maps:get(subscription_persistence, Env, #{}),
    SubscriptionPersistenceEnabled = maps:get(enabled, SubscriptionConfig, false),
    case SubscriptionPersistenceEnabled of
        true ->
            ?SLOG(info, #{msg => "initializing_subscription_persistence_module"}),
            % 初始化订阅持久化模块
            ok = emqx_plugin_mongodb_subscription:init(),
            % 加载订阅持久化配置
            ok = emqx_plugin_mongodb_subscription:load(Env),
            % 如果配置了启动时恢复订阅，则执行恢复
            % 订阅恢复现在通过MongoDB连接事件触发，不再使用时间延迟
            ?SLOG(info, #{msg => "subscription_restoration_will_be_triggered_on_mongodb_connection"});
        false ->
            ?SLOG(info, #{msg => "subscription_persistence_disabled"})
    end,

    % 初始化和加载保留消息持久化模块
    RetainedConfig = maps:get(retained_persistence, Env, #{}),
    RetainedPersistenceEnabled = maps:get(enabled, RetainedConfig, false),
    case RetainedPersistenceEnabled of
        true ->
            ?SLOG(info, #{msg => "initializing_retained_persistence_module"}),
            % 初始化保留消息持久化模块
            ok = emqx_plugin_mongodb_retained:init(),
            % 加载保留消息持久化配置
            ok = emqx_plugin_mongodb_retained:load(Env);
        false ->
            ?SLOG(info, #{msg => "retained_persistence_disabled"})
    end,

    % 初始化和加载遗嘱消息持久化模块
    WillConfig = maps:get(will_persistence, Env, #{}),
    WillPersistenceEnabled = maps:get(enabled, WillConfig, false),
    case WillPersistenceEnabled of
        true ->
            ?SLOG(info, #{msg => "initializing_will_persistence_module"}),
            % 初始化遗嘱消息持久化模块
            ok = emqx_plugin_mongodb_will:init(),
            % 加载遗嘱消息持久化配置
            ok = emqx_plugin_mongodb_will:load(Env);
        false ->
            ?SLOG(info, #{msg => "will_persistence_disabled"})
    end,

    % 初始化和加载包ID管理模块
    PacketIdConfig = maps:get(packet_id_persistence, Env, #{}),
    PacketIdPersistenceEnabled = maps:get(enabled, PacketIdConfig, false),
    case PacketIdPersistenceEnabled of
        true ->
            ?SLOG(info, #{msg => "initializing_packet_id_persistence_module"}),
            % 初始化包ID管理模块
            ok = emqx_plugin_mongodb_packet_id:init(),
            % 加载包ID管理配置
            ok = emqx_plugin_mongodb_packet_id:load(Env);
        false ->
            ?SLOG(info, #{msg => "packet_id_persistence_disabled"})
    end,

    ok.

% 处理消息
handle_info(restore_sessions, State) ->
    ?SLOG(info, #{msg => "handling_restore_sessions_message"}),
    % 异步执行会话恢复
    spawn(fun() -> emqx_plugin_mongodb_session:handle_persisted_sessions_after_restart() end),
    {noreply, State};
handle_info(restore_subscriptions, State) ->
    ?SLOG(info, #{msg => "handling_restore_subscriptions_message"}),
    % 异步执行订阅恢复
    spawn(fun() -> emqx_plugin_mongodb_subscription:handle_persisted_subscriptions_after_restart() end),
    {noreply, State};
handle_info(restore_messages, State) ->
    ?SLOG(info, #{msg => "handling_restore_messages_message"}),
    % 异步执行消息恢复
    spawn(fun() -> emqx_plugin_mongodb_message:handle_unacked_messages_after_restart() end),
    {noreply, State};
handle_info(_Info, State) ->
    {noreply, State}.

% 启动稳定性增强模块
% @spec start_stability_modules(Env :: map()) -> ok
start_stability_modules(Env) ->
    % 启动recon监控模块替代os_mon
    ReconAvailable = try
        case emqx_plugin_mongodb_monitor:start() of
            ok ->
                ?SLOG(info, #{
                    msg => "recon_monitor_started_successfully",
                    recon_available => true
                }),
                true;
            {error, Reason} ->
                ?SLOG(warning, #{
                    msg => "recon_monitor_start_failed",
                    reason => Reason,
                    fallback => "using_default_monitoring"
                }),
                false
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "recon_monitor_start_error",
                error => E,
                reason => R,
                stacktrace => S,
                fallback => "using_default_monitoring"
            }),
            false
    end,

    % 检查传统OS_MON应用作为备用（如果recon不可用）
    OsMonAvailable = case ReconAvailable of
        true ->
            false; % 如果recon可用，不需要os_mon
        false ->
            try
                case application:ensure_all_started(os_mon) of
                    {ok, _} ->
                        ?SLOG(info, #{
                            msg => "fallback_to_os_mon",
                            reason => "recon_not_available"
                        }),
                        true;
                    {error, OsMonReason} ->
                        ?SLOG(warning, #{
                            msg => "os_mon_start_failed",
                            reason => OsMonReason
                        }),
                        false
                end
            catch
                _:_ -> false
            end
    end,

    % 设置应用环境变量，供其他模块使用
    application:set_env(emqx_plugin_mongodb, recon_monitor_available, ReconAvailable),
    application:set_env(emqx_plugin_mongodb, os_mon_available, OsMonAvailable),

    % 为了向后兼容，保留旧的环境变量
    application:set_env(emqx_plugin_mongodb, memsup_available, ReconAvailable orelse OsMonAvailable),
    application:set_env(emqx_plugin_mongodb, cpu_sup_available, ReconAvailable orelse OsMonAvailable),

    MonitoringAvailable = ReconAvailable orelse OsMonAvailable,

    % 根据监控可用性修改配置
    EnvWithOsMonFlag = case MonitoringAvailable of
        true ->
            Env;
        false ->
            % 创建一个新的环境配置，禁用依赖os_mon的功能
            ?SLOG(warning, #{
                msg => "disabling_os_mon_dependent_features",
                reason => "os_mon_not_available"
            }),

            % 禁用自适应批处理中的CPU相关功能
            NewEnv1 = case maps:get(adaptive_batch, Env, #{}) of
                #{} = BatchConfig ->
                    NewBatchConfig = BatchConfig#{cpu_based_adaptation => false},
                    maps:put(adaptive_batch, NewBatchConfig, Env);
                _ ->
                    Env
            end,

            % 禁用资源管理器中的CPU/内存监控
            NewEnv2 = case maps:get(resource_manager, maps:get(connection, NewEnv1, #{}), #{}) of
                #{} = ResourceConfig ->
                    NewResourceConfig = ResourceConfig#{
                        enable_cpu_monitoring => false,
                        enable_memory_monitoring => false
                    },
                    maps:put(connection,
                            maps:put(resource_manager, NewResourceConfig, maps:get(connection, NewEnv1, #{})),
                            NewEnv1);
                _ ->
                    NewEnv1
            end,

            NewEnv2
    end,

    % 获取断路器配置
    CircuitBreakerConfig = maps:get(circuit_breaker, maps:get(connection, EnvWithOsMonFlag, #{}), #{}),

    % 启动高级熔断器模块，安全启动
    try
        {ok, _} = emqx_plugin_mongodb_circuit_breaker:start_link(CircuitBreakerConfig)
    catch
        E1:R1:S1 ->
            ?SLOG(error, #{
                msg => "failed_to_start_circuit_breaker",
                error => E1,
                reason => R1,
                stacktrace => S1
            })
    end,

    % 获取错误处理配置
    ErrorHandlerConfig = maps:get(error_handler, maps:get(connection, EnvWithOsMonFlag, #{}), #{}),

    % 启动异常处理增强模块，安全启动
    try
        {ok, _} = emqx_plugin_mongodb_error_handler:start_link(ErrorHandlerConfig)
    catch
        E2:R2:S2 ->
            ?SLOG(error, #{
                msg => "failed_to_start_error_handler",
                error => E2,
                reason => R2,
                stacktrace => S2
            })
    end,

    % 启动资源管理优化模块，安全启动
    try
        {ok, _} = emqx_plugin_mongodb_resource_manager:start_link(
            maps:get(resource_manager, maps:get(connection, EnvWithOsMonFlag, #{}), #{})
        )
    catch
        E3:R3:S3 ->
            ?SLOG(error, #{
                msg => "failed_to_start_resource_manager",
                error => E3,
                reason => R3,
                stacktrace => S3
            })
    end,

    % 获取背压机制配置
    BackpressureConfig = maps:get(backpressure, maps:get(connection, EnvWithOsMonFlag, #{}), #{}),

    % 启动背压机制模块，安全启动
    try
        {ok, _} = emqx_plugin_mongodb_backpressure:start_link(BackpressureConfig)
    catch
        E4:R4:S4 ->
            ?SLOG(error, #{
                msg => "failed_to_start_backpressure",
                error => E4,
                reason => R4,
                stacktrace => S4
            })
    end,

    ok.

% 注册CLI命令并记录成功日志
% @spec register_cli_and_log() -> ok
register_cli_and_log() ->
    % `emqx_ctl:register_command/2` 用于向EMQX的管理控制台注册新的命令行命令。
    % `emqx_plugin_mongodb` 是命令的命名空间/前缀。
    % `{emqx_plugin_mongodb_cli, cmd}` 指定了处理命令的模块和函数。
    % 当用户在emqx_ctl中输入 `emqx_plugin_mongodb <sub_command>` 时，会调用 `emqx_plugin_mongodb_cli:cmd([...])`。
    emqx_ctl:register_command(emqx_plugin_mongodb, {emqx_plugin_mongodb_cli, cmd}),
    ?SLOG(info, #{msg => "mongodb_plugin_started_successfully"}).

%% @doc 停止应用
%% 这是OTP应用程序的停止回调函数，负责优雅地关闭插件和释放资源
%%
%% 功能说明：
%% 1. 注销命令行接口
%% 2. 安全地停止所有子模块
%% 3. 卸载主模块和释放资源
%% 4. 处理停止过程中的异常
%%
%% 参数说明：
%% - _State: 应用当前状态，通常是start/2返回的监督者Pid（这里不使用）
%%
%% 返回值：
%% - ok: 应用已成功停止
%%
%% Java等价概念：
%% 类似于Spring的@PreDestroy注解方法或DisposableBean.destroy()
%% 用于应用关闭前的清理工作
stop(_State) ->
    ?SLOG(info, #{msg => "stopping_mongodb_plugin"}),

    %% 注销之前注册的MongoDB插件CLI命令
    %% 类似于Java中注销JMX MBean或关闭管理端点
    %% 在Java中相当于：
    %% @PreDestroy
    %% public void unregisterManagementEndpoints() {
    %%     managementRegistry.unregister("mongodb-plugin");
    %% }
    try
        emqx_ctl:unregister_command(emqx_plugin_mongodb)
    catch
        %% 捕获注销过程中的异常，不影响其他清理工作
        Class0:Error0:Stack0 ->
            ?SLOG(warning, #{
                msg => "failed_to_unregister_mongodb_cli",
                class => Class0,        % 异常类别
                error => Error0,        % 错误信息
                stacktrace => Stack0    % 堆栈跟踪
            })
    end,

    %% 卸载MongoDB插件，包括关闭连接、释放资源等
    %% 使用try-catch确保即使某个步骤失败也能继续执行其他清理工作
    %% 类似于Java中的资源清理和连接池关闭
    Result = try
        %% 安全地停止所有子模块，忽略各个模块停止时可能发生的错误
        %% 类似于Java中的ExecutorService.shutdown()和资源清理
        safe_stop_all_modules(),

        %% 清理persistent_term中的所有MongoDB相关数据
        %% 这是关键修复：确保插件重启时不会使用失效的连接信息
        cleanup_persistent_terms(),

        %% 最后卸载主模块
        case emqx_plugin_mongodb:unload() of
        ok ->
            ?SLOG(info, #{msg => "mongodb_plugin_stopped_successfully"}),
            ok;
        {error, Reason} ->
            % 即使卸载失败，也记录警告并返回ok，以允许EMQX正常关闭。
            ?SLOG(warning, #{msg => "failed_to_unload_mongodb_plugin", reason => Reason}),
            ok % 返回ok以允许应用正常停止，但记录警告
        end
    catch
        ClassE:ErrorE:StackE ->
            ?SLOG(error, #{
                msg => "critical_error_during_mongodb_plugin_stop",
                class => ClassE,
                error => ErrorE,
                stacktrace => StackE
            }),
            ok % 即使在严重错误的情况下，也返回ok以允许EMQX继续关闭
    end,

    % 应用停止时不删除配置文件，只在插件卸载时删除
    % delete_config_file(),

    Result.

% 安全停止所有子模块
% @spec safe_stop_all_modules() -> ok
% 依次安全地停止所有子模块，确保每个停止操作都被单独try-catch保护
safe_stop_all_modules() ->
    % 停止模块顺序与启动顺序相反

    % 1. 首先停止连接管理模块
    try
        emqx_plugin_mongodb_connection:stop()
    catch
        C1:E1:S1 ->
            ?SLOG(warning, #{
                msg => "failed_to_stop_connection_module",
                class => C1,
                error => E1,
                stacktrace => S1
            })
    end,

    % 2. 停止消息持久化模块
    try
        emqx_plugin_mongodb_message:unload()
    catch
        CMsg:EMsg:SMsg ->
            ?SLOG(warning, #{
                msg => "failed_to_stop_message_persistence_module",
                class => CMsg,
                error => EMsg,
                stacktrace => SMsg
            })
    end,

    % 3. 停止自适应批处理模块
    try
        emqx_plugin_mongodb_adaptive_batch:stop()
    catch
        C2:E2:S2 ->
            ?SLOG(warning, #{
                msg => "failed_to_stop_adaptive_batch_module",
                class => C2,
                error => E2,
                stacktrace => S2
            })
    end,

    % 4. 停止并行处理管道
    try
        emqx_plugin_mongodb_pipeline:stop()
    catch
        C3:E3:S3 ->
            ?SLOG(warning, #{
                msg => "failed_to_stop_pipeline_module",
                class => C3,
                error => E3,
                stacktrace => S3
            })
    end,

    % 5. 停止模块协调器
    try
        emqx_plugin_mongodb_coordinator:stop()
    catch
        C4:E4:S4 ->
            ?SLOG(warning, #{
                msg => "failed_to_stop_coordinator_module",
                class => C4,
                error => E4,
                stacktrace => S4
            })
    end,

    % 6. 停止稳定性增强模块
    try
        stop_stability_modules()
    catch
        C5:E5:S5 ->
            ?SLOG(warning, #{
                msg => "failed_to_stop_stability_modules",
                class => C5,
                error => E5,
                stacktrace => S5
            })
    end,

    % 7. 释放持久化的term，确保不影响下次启动
    try
        cleanup_persistent_terms()
    catch
        C6:E6:S6 ->
            ?SLOG(warning, #{
                msg => "failed_to_cleanup_persistent_terms",
                class => C6,
                error => E6,
                stacktrace => S6
            })
    end,

    ok.

% 停止稳定性增强模块
% @spec stop_stability_modules() -> ok
stop_stability_modules() ->
    % 停止高级熔断器模块
    try
        gen_server:call(emqx_plugin_mongodb_circuit_breaker, stop, 5000)
    catch
        Class1:Error1:Stack1 ->
            ?SLOG(warning, #{
                msg => "failed_to_stop_circuit_breaker",
                class => Class1,
                error => Error1,
                stacktrace => Stack1
            })
    end,

    % 停止异常处理增强模块
    try
        gen_server:call(emqx_plugin_mongodb_error_handler, stop, 5000)
    catch
        Class2:Error2:Stack2 ->
            ?SLOG(warning, #{
                msg => "failed_to_stop_error_handler",
                class => Class2,
                error => Error2,
                stacktrace => Stack2
            })
    end,

    % 停止资源管理优化模块
    try
        gen_server:call(emqx_plugin_mongodb_resource_manager, stop, 5000)
    catch
        Class3:Error3:Stack3 ->
            ?SLOG(warning, #{
                msg => "failed_to_stop_resource_manager",
                class => Class3,
                error => Error3,
                stacktrace => Stack3
            })
    end,

    % 停止背压机制模块
    try
        gen_server:call(emqx_plugin_mongodb_backpressure, stop, 5000)
    catch
        Class4:Error4:Stack4 ->
            ?SLOG(warning, #{
                msg => "failed_to_stop_backpressure",
                class => Class4,
                error => Error4,
                stacktrace => Stack4
            })
    end,

    ok.

% 清理持久化的term
% @spec cleanup_persistent_terms() -> ok
cleanup_persistent_terms() ->
    % 清理与MongoDB插件相关的所有persistent_term
    Keys = [
        % 核心资源键
        ?PLUGIN_MONGODB_RESOURCE_ID,
        mongodb_circuit_breaker,
        mongodb_version,
        mongodb_message_policy,

        % 指标相关键
        {mongodb_metric, insert_count},
        {mongodb_metric, insert_error},
        {mongodb_metric, query_latency},
        {mongodb_metric, connection_failure},
        {mongodb_metric, batch_size},
        {mongodb_metric, queue_length},
        {mongodb_metric, circuit_breaker_trips}
    ],

    % 尝试清理每个键
    [begin
        try
            persistent_term:erase(Key)
        catch
            _:_ -> ok
        end
     end || Key <- Keys],

    % 尝试清理所有错误计数器
    ErrorCounters = [
        connection_error,
        authentication_error,
        timeout_error,
        query_error,
        network_error,
        unknown_error
    ],

    [begin
        try
            persistent_term:erase({mongodb_error_counter, Counter})
        catch
            _:_ -> ok
        end
     end || Counter <- ErrorCounters],

    % 额外的彻底清理：尝试清理所有可能的MongoDB相关键
    % 这是一个安全网，确保没有遗漏的键
    try
        % 获取所有persistent_term键并清理MongoDB相关的
        AllKeys = persistent_term:get(),
        MongoKeys = [Key || {Key, _Value} <- AllKeys, is_mongodb_related_key(Key)],
        [begin
            try
                persistent_term:erase(Key)
            catch
                _:_ -> ok
            end
         end || Key <- MongoKeys]
    catch
        _:_ -> ok  % 如果获取所有键失败，忽略错误
    end,

    ?SLOG(info, #{msg => "persistent_terms_cleanup_completed"}),
    ok.

%% @doc 检查键是否与MongoDB相关
is_mongodb_related_key(Key) when is_atom(Key) ->
    KeyStr = atom_to_list(Key),
    string:prefix(KeyStr, "mongodb") =/= nomatch;
is_mongodb_related_key({mongodb_metric, _}) -> true;
is_mongodb_related_key({mongodb_error_counter, _}) -> true;
is_mongodb_related_key(?PLUGIN_MONGODB_RESOURCE_ID) -> true;
is_mongodb_related_key(_) -> false.

%% @doc 复制配置文件到etc目录
%% 这个函数将插件的默认配置文件从priv目录复制到EMQX的etc目录
%%
%% 功能说明：
%% 1. 查找插件priv目录中的配置文件
%% 2. 获取EMQX的etc目录路径
%% 3. 如果目标文件已存在，先创建带时间戳的备份
%% 4. 复制配置文件到目标位置
%%
%% 返回值：
%% - ok: 复制成功或无需复制
%% - undefined: 找不到必要的目录或文件
%%
%% Java等价概念：
%% 类似于Spring Boot的配置文件自动复制或Maven的资源过滤
%% 将默认配置部署到运行时目录
copy_config_to_etc() ->
    try %% Erlang的异常处理块，类似于Java的try-catch
        %% 获取priv目录中的配置文件路径
        %% code:priv_dir/1返回应用程序的priv目录路径
        %% 类似于Java中的ClassLoader.getResource()
        %% 在Java中相当于：
        %% String privDir = getClass().getClassLoader().getResource("").getPath();
        PrivConfigPath = case code:priv_dir(emqx_plugin_mongodb) of
            {error, _} -> %% 如果找不到priv目录
                ?SLOG(warning, #{msg => "cannot_find_priv_dir"}),
                undefined; %% 表示未定义或失败
            PrivDir -> %% 如果找到，PrivDir是priv目录的路径字符串
                %% 构建完整的配置文件路径
                %% filename:join/2类似于Java的Paths.get().resolve()
                filename:join(PrivDir, "emqx_plugin_mongodb.hocon")
        end,

        %% 检查priv目录中的配置文件是否存在且是一个普通文件
        %% andalso是短路与操作符，类似于Java的&&
        %% filelib:is_regular/1类似于Java的Files.isRegularFile()
        case PrivConfigPath =/= undefined andalso filelib:is_regular(PrivConfigPath) of
            true -> %% 如果配置文件存在
                %% 获取EMQX的etc目录
                %% 类似于Java中获取应用程序的配置目录
                case get_emqx_etc_dir() of
                    undefined ->
                        ?SLOG(warning, #{msg => "cannot_find_emqx_etc_dir"});
                    EtcDir -> %% EtcDir是EMQX的etc目录路径
                        %% 构造目标配置文件路径
                        TargetConfigPath = filename:join(EtcDir, "emqx_plugin_mongodb.hocon"),

                        %% 检查目标文件是否已存在，如果存在则先备份
                        %% 类似于Java中的文件备份操作
                        case filelib:is_regular(TargetConfigPath) of
                            true ->
                                %% 生成带时间戳的备份文件名
                                %% calendar:local_time/0类似于Java的LocalDateTime.now()
                                {{Year, Month, Day}, {Hour, Minute, Second}} = calendar:local_time(),
                                %% io_lib:format/2类似于Java的String.format()
                                BackupSuffix = io_lib:format(".~4..0w~2..0w~2..0w~2..0w~2..0w~2..0w.bak",
                                                           [Year, Month, Day, Hour, Minute, Second]),
                                BackupPath = TargetConfigPath ++ BackupSuffix,

                                %% 复制现有文件为备份
                                %% file:copy/2类似于Java的Files.copy()
                                case file:copy(TargetConfigPath, BackupPath) of
                                    {ok, _} ->
                                        ?SLOG(info, #{msg => "config_file_backed_up",
                                                    original => TargetConfigPath,
                                                    backup => BackupPath});
                                    {error, BackupReason} ->
                                        ?SLOG(warning, #{msg => "failed_to_backup_config_file",
                                                       reason => BackupReason,
                                                       original => TargetConfigPath,
                                                       backup => BackupPath})
                                end;
                            false ->
                                ok % 文件不存在，不需要备份
                        end,

                        % 复制新配置文件
                        case file:copy(PrivConfigPath, TargetConfigPath) of
                            {ok, _BytesCopied} ->
                                ?SLOG(info, #{msg => "config_file_copied_to_etc",
                                            source => PrivConfigPath,
                                            target => TargetConfigPath});
                            {error, CopyReason} ->
                                ?SLOG(warning, #{msg => "failed_to_copy_config_file",
                                               reason => CopyReason,
                                               source => PrivConfigPath,
                                               target => TargetConfigPath})
                        end
                end;
            false -> % 如果priv目录中没有配置文件
                ?SLOG(info, #{msg => "no_config_file_in_priv_dir"})
        end
    catch % 捕获异常
        % Type:Error:Stacktrace 是Erlang中捕获异常的模式
        % Type: 异常类型 (e.g., error, throw, exit)
        % Error: 异常原因
        % Stacktrace: 堆栈跟踪信息
        Type:Error:Stack ->
            ?SLOG(warning, #{msg => "error_copying_config_file",
                           type => Type,
                           error => Error,
                           stacktrace => Stack})
    end.

% 获取EMQX的etc目录
% @spec get_emqx_etc_dir() -> string() | undefined
% 这个函数尝试通过多种方式确定EMQX的 `etc` 目录路径。
% 优先级：环境变量 -> 应用配置 -> 代码路径推断 -> 默认路径。
% 类似于Java中从不同来源（系统属性、配置文件、默认值）获取配置项。
get_emqx_etc_dir() ->
    % 1. 通过环境变量 `EMQX_ETC_DIR`
    case os:getenv("EMQX_ETC_DIR") of
        false -> % `os:getenv` 在环境变量未设置时返回 `false` 原子
            % 2. 通过应用配置 `emqx` 应用的 `etc_dir` 参数
            % `application:get_env(AppName, Key)` 获取应用的配置项。
            case application:get_env(emqx, etc_dir) of
                {ok, EtcDir} when is_list(EtcDir) -> % `when is_list(EtcDir)` 是一个卫语句，确保EtcDir是字符串
                    EtcDir;
                _ -> % 如果应用配置中也没有
                    % 3. 通过代码路径推断
                    % `code:lib_dir(AppName)` 返回 `AppName` 应用的库目录路径 (ebin的上级目录)。
                    case code:lib_dir(emqx) of
                        {error, _} -> % 如果找不到emqx的库目录
                            % 4. 默认使用相对于当前模块的路径 `../etc`
                            % `code:which(?MODULE)` 返回当前模块的beam文件路径。
                            % `filename:dirname/1` 获取目录名。
                            filename:join(filename:dirname(code:which(?MODULE)), "../etc");
                        EmqxPath -> % EmqxPath是emqx库的路径
                            % 使用EMQX安装路径下的etc，通常是 `lib/emqx-<version>/etc`
                            filename:join(filename:dirname(EmqxPath), "etc")
                    end
            end;
        EtcDir -> % 如果环境变量已设置，EtcDir是环境变量的值
            EtcDir
    end.

% 删除配置文件
% @spec delete_config_file() -> ok
% 这个函数尝试删除EMQX的 `etc` 目录中的插件配置文件。
% 通常在插件卸载时调用。
delete_config_file() ->
    try
        % 获取EMQX的etc目录
        case get_emqx_etc_dir() of
            undefined ->
                ?SLOG(warning, #{msg => "cannot_find_emqx_etc_dir_for_deletion"});
            EtcDir ->
                % 构造配置文件路径
                ConfigPath = filename:join(EtcDir, "emqx_plugin_mongodb.hocon"),

                % 检查文件是否存在并删除
                case filelib:is_regular(ConfigPath) of
                    true ->
                        case file:delete(ConfigPath) of
                            ok ->
                                ?SLOG(info, #{msg => "config_file_deleted", path => ConfigPath});
                            {error, DeleteReason} ->
                                ?SLOG(warning, #{msg => "failed_to_delete_config_file",
                                               reason => DeleteReason,
                                               path => ConfigPath})
                        end;
                    false ->
                        ?SLOG(info, #{msg => "config_file_not_found_for_deletion", path => ConfigPath})
                end
        end
    catch
        Type:Error:Stack ->
            ?SLOG(warning, #{msg => "error_deleting_config_file",
                           type => Type,
                           error => Error,
                           stacktrace => Stack})
    end.

% 卸载插件
% @spec uninstall() -> ok
% 这个函数在插件卸载时调用，用于清理配置文件和其他资源。
uninstall() ->
    ?SLOG(info, #{msg => "uninstalling_mongodb_plugin"}),
    delete_config_file(),
    ok.

%% @doc 验证MongoDB资源是否可用
%% @return true | false
verify_mongodb_resource_available() ->
    try
        case emqx_plugin_mongodb_connection:check_connection() of
            {ok, _} ->
                ?SLOG(info, #{msg => "mongodb_resource_available"}),
                true;
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "mongodb_resource_not_available",
                    reason => Reason
                }),
                false
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_checking_mongodb_resource",
                error => E,
                reason => R,
                stacktrace => S
            }),
            false
    end.

%% @doc 解析延迟配置，支持时间单位后缀
%% @spec parse_delay_config(binary() | integer(), integer()) -> integer()
parse_delay_config(Delay, Default) when is_integer(Delay) ->
    Delay;
parse_delay_config(<<"immediate">>, _Default) ->
    0;
parse_delay_config(<<Num:1/binary, "s">>, Default) ->
    try
        binary_to_integer(Num) * 1000
    catch
        _:_ -> Default
    end;
parse_delay_config(<<Num:2/binary, "s">>, Default) ->
    try
        binary_to_integer(Num) * 1000
    catch
        _:_ -> Default
    end;
parse_delay_config(<<Num:1/binary, "m">>, Default) ->
    try
        binary_to_integer(Num) * 60 * 1000
    catch
        _:_ -> Default
    end;
parse_delay_config(<<Num:1/binary, "h">>, Default) ->
    try
        binary_to_integer(Num) * 60 * 60 * 1000
    catch
        _:_ -> Default
    end;
parse_delay_config(BinDelay, Default) when is_binary(BinDelay) ->
    try
        binary_to_integer(BinDelay)
    catch
        _:_ -> Default
    end;
parse_delay_config(_, Default) ->
    Default.

%% @doc 检测并存储MongoDB版本信息
%% 在连接建立后异步执行版本检测
-spec detect_and_store_mongodb_version() -> ok.
detect_and_store_mongodb_version() ->
    ?SLOG(info, #{msg => "starting_mongodb_version_detection_after_connection"}),

    % 异步执行版本检测，避免阻塞主流程
    spawn(fun() ->
        try
            % 获取MongoDB连接进程ID
            case persistent_term:get(?PLUGIN_MONGODB_RESOURCE_ID, undefined) of
                undefined ->
                    ?SLOG(warning, #{
                        msg => "mongodb_version_detection_skipped_no_connection_pid"
                    });
                Pid when is_pid(Pid) ->
                    % 执行版本检测
                    MongoVersion = emqx_plugin_mongodb_connector:detect_mongodb_version_with_retry(Pid),

                    % 更新资源状态中的版本信息
                    update_resource_mongo_version(MongoVersion),

                    ?SLOG(info, #{
                        msg => "mongodb_version_detected_and_stored",
                        version => MongoVersion
                    });
                Other ->
                    ?SLOG(warning, #{
                        msg => "mongodb_version_detection_skipped_invalid_pid",
                        pid => Other
                    })
            end
        catch
            Class:Error:Stack ->
                ?SLOG(error, #{
                    msg => "mongodb_version_detection_failed",
                    class => Class,
                    error => Error,
                    stack => Stack
                })
        end
    end),
    ok.

%% @doc 更新资源状态中的MongoDB版本信息
-spec update_resource_mongo_version(binary()) -> ok.
update_resource_mongo_version(Version) ->
    try
        % 将版本信息存储到persistent_term中供其他模块使用
        persistent_term:put(mongodb_version, Version),

        ?SLOG(info, #{
            msg => "mongodb_version_updated_in_persistent_term",
            version => Version
        }),
        ok
    catch
        Class:Error ->
            ?SLOG(warning, #{
                msg => "failed_to_update_resource_mongo_version",
                class => Class,
                error => Error,
                version => Version
            }),
            ok
    end.
