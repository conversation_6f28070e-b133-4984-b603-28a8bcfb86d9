%% @doc MQTT会话持久化模块 - 企业级会话管理和持久化系统
%% 这个模块是MQTT会话持久化的核心组件，提供完整的会话生命周期管理
%%
%% 功能概述：
%% 1. 会话生命周期管理 - 完整的MQTT会话创建、恢复、终止管理
%% 2. 持久化存储 - 将会话状态持久化到MongoDB，确保系统重启后不丢失
%% 3. 会话恢复 - 系统启动时自动恢复持久化的会话到EMQX内存
%% 4. 过期清理 - 定期清理过期的会话，防止存储膨胀
%% 5. 统计监控 - 提供详细的会话统计信息和监控指标
%% 6. 会话接管 - 处理客户端重连时的会话接管逻辑
%% 7. 遗嘱消息管理 - 集成遗嘱消息的持久化和处理
%%
%% MQTT会话机制：
%% - 持久会话：客户端断开后保持会话状态，重连时恢复
%% - 临时会话：客户端断开后立即清理会话状态
%% - 会话接管：同一客户端ID的新连接接管旧会话
%% - 会话恢复：系统重启后从持久化存储恢复会话
%%
%% 架构设计：
%% - 事件驱动：通过EMQX钩子系统响应会话生命周期事件
%% - 异步处理：所有持久化操作都是异步的，不阻塞会话处理
%% - 状态同步：确保MongoDB和EMQX内存中的会话状态一致
%% - 容错恢复：支持系统故障后的会话状态恢复
%% - 性能优化：使用批量操作和索引优化，提高处理性能
%%
%% Java等价概念：
%% 类似于Spring Boot中的会话管理服务：
%% @Service
%% @Component
%% @Transactional
%% public class SessionPersistenceService {
%%     @Autowired private MongoTemplate mongoTemplate;
%%     @Autowired private SessionRepository sessionRepository;
%%     @Autowired private RedisTemplate redisTemplate; // 缓存层
%%
%%     @EventListener
%%     @Async
%%     public void onSessionCreated(SessionCreatedEvent event) {
%%         // 异步保存会话状态
%%         Session session = convertToSession(event);
%%         sessionRepository.save(session);
%%     }
%%
%%     @EventListener
%%     @Async
%%     public void onSessionTerminated(SessionTerminatedEvent event) {
%%         // 异步更新会话状态或删除临时会话
%%         if (event.getSession().isPersistent()) {
%%             sessionRepository.updateStatus(event.getClientId(), "disconnected");
%%         } else {
%%             sessionRepository.delete(event.getClientId());
%%         }
%%     }
%%
%%     @Scheduled(fixedDelay = 300000) // 5分钟清理过期会话
%%     public void cleanupExpiredSessions() {
%%         sessionRepository.deleteExpiredSessions();
%%     }
%%
%%     @PostConstruct
%%     public void restoreSessions() {
%%         // 系统启动时恢复持久化会话
%%         List<Session> sessions = sessionRepository.findActiveSessions();
%%         for (Session session : sessions) {
%%             emqxSessionManager.restoreSession(session);
%%         }
%%     }
%% }
%%
%% 设计模式：
%% - 观察者模式：监听EMQX的会话生命周期事件
%% - 策略模式：不同类型会话使用不同的处理策略
%% - 模板方法模式：统一的会话处理流程模板
%% - 工厂模式：会话对象的创建和转换
%% @end
-module(emqx_plugin_mongodb_session).

-include("emqx_plugin_mongodb.hrl").
% 包含EMQX持久会话的记录定义
-include_lib("emqx/src/emqx_persistent_session_ds.hrl").

%% ============================================================================
%% 生命周期管理API - 类似于Java的@PostConstruct和@PreDestroy
%% 在Java中相当于：
%% @PostConstruct public void init() { ... }
%% @PreDestroy public void cleanup() { ... }
%% ============================================================================
-export([
    init/0,     % 初始化模块 - 类似于@PostConstruct
    load/1,     % 加载配置 - 类似于配置加载
    unload/0    % 卸载模块 - 类似于@PreDestroy
]).

%% ============================================================================
%% MQTT会话事件监听器 - 类似于Java的@EventListener
%% 在Java中相当于：
%% @EventListener
%% public void onSessionCreated(SessionCreatedEvent event) { ... }
%% ============================================================================
-export([
    on_client_authenticate/2,   % 客户端认证事件 - onClientAuthenticate(ClientInfo, AuthResult)
    on_client_connect/2,        % 客户端连接事件 - onClientConnect(ClientInfo, ConnInfo)
    on_session_created/2,       % 会话创建事件 - onSessionCreated(ClientInfo, SessionInfo)
    on_session_resumed/2,       % 会话恢复事件 - onSessionResumed(ClientInfo, SessionInfo)
    on_session_discarded/2,     % 会话丢弃事件 - onSessionDiscarded(ClientInfo, SessionInfo)
    % on_session_takenover/2 已删除 - 让EMQX自然处理会话恢复
    on_session_terminated/3,    % 会话终止事件 - onSessionTerminated(ClientInfo, Reason, SessionInfo)
    on_client_connected/2,      % 客户端连接事件 - onClientConnected(ClientInfo, ConnInfo)
    on_client_disconnected/3    % 客户端断开事件 - onClientDisconnected(ClientInfo, Reason, ConnInfo)
]).

%% ============================================================================
%% 会话管理业务API - 类似于Java的Service层方法
%% 在Java中相当于：
%% @Service
%% public class SessionService {
%%     public void saveSession(ClientInfo clientInfo, SessionInfo sessionInfo);
%%     public List<Session> restoreSessions();
%%     public void cleanupExpiredSessions();
%% }
%% ============================================================================
-export([
    save_session/2,             % 保存会话 - saveSession(ClientInfo, SessionInfo)
    handle_clean_start_session/3,   % 处理Clean Start会话
                                   % 功能：根据MQTT协议处理Clean Start = true的会话
                                   % Java等价：public void handleCleanStartSession(String clientId, ClientInfo info, SessionInfo sessInfo)

    handle_persistent_session/3,    % 处理持久会话
                                   % 功能：根据MQTT协议和Session Expiry Interval处理持久会话
                                   % Java等价：public void handlePersistentSession(String clientId, ClientInfo info, SessionInfo sessInfo)

    create_persistent_session/4,    % 创建持久会话
                                   % 功能：创建符合MQTT协议的持久会话
                                   % Java等价：public void createPersistentSession(String clientId, ClientInfo info, SessionInfo sessInfo, int expiryInterval)



    handle_persisted_sessions_after_restart/0, % 处理系统重启后的持久化会话
    cleanup_expired_sessions/0 % 清理过期会话
]).

%% 内部函数
-export([
    start_cleanup_timer/0,
    do_cleanup_expired_sessions/0,
    get_session_collection/0,
    get_will_message_collection/0,
    integrate/0
]).

%% 注意：集合名称获取函数已在内部函数中导出，避免重复导出

%% ============================================================================
%% 配置常量定义 - 会话持久化的核心配置参数
%% 这些常量控制会话持久化的行为和性能特征
%% 类似于Java中的配置常量或@ConfigurationProperties
%% ============================================================================

%% 默认会话过期时间：2小时（7200000毫秒）
%% 功能：控制持久化会话在MongoDB中的保存时间
%% 超过此时间的会话将被自动清理，防止存储无限增长
%% Java等价：@Value("${session.expiry.time:7200000}")
%% 或者：spring.session.expiry-time=7200000
-define(DEFAULT_SESSION_EXPIRY, 7200000).

%% 默认清理间隔：5分钟（300000毫秒）
%% 功能：控制过期会话清理任务的执行频率
%% 定时任务会按此间隔清理过期的持久化会话
%% Java等价：@Scheduled(fixedDelay = 300000)
%% 或者：spring.task.scheduling.pool.size=1
-define(DEFAULT_CLEANUP_INTERVAL, 300000).

%% 默认批次大小：100条会话记录
%% 功能：控制批量操作的大小，平衡性能和内存使用
%% 清理任务每次处理的会话数量，避免一次性处理过多数据
%% Java等价：@Value("${session.cleanup.batch-size:100}")
%% 或者：spring.data.mongodb.batch-size=100
-define(DEFAULT_CLEANUP_BATCH_SIZE, 100).

%% ============================================================================
%% 模块初始化函数 - 会话持久化模块的启动入口
%% ============================================================================

%% @doc 初始化会话持久化模块
%% 这个函数是模块的启动入口，负责初始化所有必要的资源和服务
%%
%% 功能说明：
%% 1. 检查会话持久化功能是否启用
%% 2. 等待MongoDB资源就绪
%% 3. 创建必要的集合和索引
%% 4. 启动过期会话清理定时器
%% 5. 记录初始化状态日志
%%
%% 返回值：
%% - ok: 初始化成功
%%
%% Java等价概念：
%% @PostConstruct
%% @ConditionalOnProperty(name = "session.persistence.enabled", havingValue = "true")
%% public void initializeSessionPersistence() {
%%     if (sessionPersistenceEnabled) {
%%         waitForMongoDBReady();
%%         createCollectionsAndIndexes();
%%         startCleanupScheduler();
%%         logger.info("Session persistence initialized successfully");
%%     }
%% }
%%
%% 设计特点：
%% - 条件初始化：只有在启用会话持久化时才执行初始化
%% - 资源等待：确保MongoDB资源可用后再进行后续操作
%% - 完整设置：一次性完成所有必要的初始化工作
init() ->
    %% 记录模块初始化开始的日志
    %% 这有助于系统启动过程的跟踪和调试
    ?SLOG(info, #{msg => "initializing_mongodb_session_module"}),

    %% 注意：实际的初始化逻辑现在在load/1函数中执行
    %% 这是因为init/0在load/1之前被调用，但需要load/1设置的环境变量
    ?SLOG(info, #{msg => "session_module_init_completed_actual_initialization_in_load"}),
    ok.

%% 加载会话模块
load(Config) ->
    ?SLOG(info, #{msg => "loading_mongodb_session_module", config => Config}),
    % 检查会话持久化是否启用
    SessionConfig = maps:get(session_persistence, Config, #{}),
    SessionPersistenceEnabled = maps:get(enabled, SessionConfig, false),

    % 保存启用状态到应用环境变量中
    application:set_env(emqx_plugin_mongodb, session_persistence_enabled, SessionPersistenceEnabled),

    % 注册会话相关钩子
    register_hooks(),

    % 如果会话持久化启用，检查是否需要启动时恢复会话
    case SessionPersistenceEnabled of
        true ->
            ?SLOG(info, #{msg => "session_persistence_enabled_checking_connection"}),
            % 检查MongoDB连接是否已建立
            case check_mongodb_connection_ready() of
                true ->
                    ?SLOG(info, #{msg => "mongodb_connection_ready_initializing_session_persistence"}),
                    initialize_session_persistence();
                false ->
                    ?SLOG(info, #{msg => "mongodb_connection_not_ready_deferring_session_persistence_initialization"}),
                    % 启动后台任务等待连接建立
                    spawn_link(fun() -> wait_and_initialize_session_persistence() end),
                    ok
            end,
                % 如果配置了启动时恢复会话，则执行恢复
                % 会话恢复现在通过MongoDB连接事件触发，不再使用时间延迟
                ?SLOG(info, #{msg => "session_restoration_will_be_triggered_on_mongodb_connection"}),
                ok;
        false ->
            ?SLOG(info, #{msg => "session_persistence_disabled"})
    end,
    ok.

%% @doc 检查MongoDB连接是否已建立
-spec check_mongodb_connection_ready() -> boolean().
check_mongodb_connection_ready() ->
    try
        % 使用简单的资源健康检查而不是查询
        case emqx_resource:health_check(?PLUGIN_MONGODB_RESOURCE_ID) of
            ok ->
                true;
            {ok, _} ->
                true;
            _ ->
                false
        end
    catch
        _:_ ->
            false
    end.

%% @doc 等待MongoDB连接建立并初始化会话持久化
-spec wait_and_initialize_session_persistence() -> ok.
wait_and_initialize_session_persistence() ->
    wait_and_initialize_session_persistence(60). % 最多等待60次，每次2秒

%% @doc 等待循环
-spec wait_and_initialize_session_persistence(integer()) -> ok.
wait_and_initialize_session_persistence(0) ->
    ?SLOG(error, #{msg => "session_persistence_initialization_timeout"}),
    ok;
wait_and_initialize_session_persistence(Retries) ->
    timer:sleep(2000), % 每2秒检查一次
    case check_mongodb_connection_ready() of
        true ->
            ?SLOG(info, #{msg => "mongodb_connection_ready_initializing_session_persistence_delayed"}),
            initialize_session_persistence();
        false ->
            wait_and_initialize_session_persistence(Retries - 1)
    end.

%% @doc 初始化会话持久化
-spec initialize_session_persistence() -> ok.
initialize_session_persistence() ->
    try
        % 确保集合存在
        ensure_collections(),
        % 启动清理定时器
        start_cleanup_timer(),

        % 检查是否需要在启动时恢复会话
        Config = emqx_plugin_mongodb:read_config(),
        ?SLOG(info, #{msg => "checking_session_restoration_config", config_type => get_type_name(Config)}),

        case Config of
            {error, Reason} ->
                ?SLOG(warning, #{
                    msg => "failed_to_read_config_for_session_restoration",
                    reason => Reason,
                    note => "using_default_restore_on_startup_true"
                }),
                % 配置读取失败，使用默认值
                ?SLOG(info, #{msg => "starting_session_restoration_on_startup_default"}),
                spawn(fun() ->
                    timer:sleep(5000),
                    handle_persisted_sessions_after_restart()
                end);
            ConfigMap when is_map(ConfigMap) ->
                SessionConfig = maps:get(session_persistence, ConfigMap, #{}),
                RestoreOnStartup = maps:get(restore_on_startup, SessionConfig, true),

                ?SLOG(info, #{
                    msg => "session_restoration_config_read",
                    restore_on_startup => RestoreOnStartup,
                    session_config_keys => maps:keys(SessionConfig)
                }),

                case RestoreOnStartup of
                    true ->
                        ?SLOG(info, #{msg => "starting_session_restoration_on_startup"}),
                        % 异步执行会话恢复，确保EMQX核心组件完全初始化
                        spawn(fun() ->
                            wait_for_emqx_and_mongodb_ready(), % 等待EMQX和MongoDB完全就绪
                            handle_persisted_sessions_after_restart()
                        end);
                    false ->
                        ?SLOG(info, #{msg => "session_restoration_on_startup_disabled"})
                end;
            Other ->
                ?SLOG(warning, #{
                    msg => "unexpected_config_format",
                    config => Other,
                    note => "using_default_restore_on_startup_true"
                }),
                ?SLOG(info, #{msg => "starting_session_restoration_on_startup_fallback"}),
                spawn(fun() ->
                    timer:sleep(5000),
                    handle_persisted_sessions_after_restart()
                end)
        end,

        ?SLOG(info, #{msg => "session_persistence_initialized_successfully"}),
        ok
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "failed_to_initialize_session_persistence",
                error => E,
                reason => R,
                stacktrace => S
            }),
            ok
    end.

%% @doc 获取值的类型名称（用于调试）
get_type_name(Value) when is_map(Value) -> map;
get_type_name(Value) when is_list(Value) -> list;
get_type_name(Value) when is_binary(Value) -> binary;
get_type_name(Value) when is_atom(Value) -> atom;
get_type_name(Value) when is_integer(Value) -> integer;
get_type_name(Value) when is_tuple(Value) -> tuple;
get_type_name(_) -> unknown.

%% 卸载会话模块
unload() ->
    ?SLOG(info, #{msg => "unloading_mongodb_session_module"}),
    % 停止清理定时器
    stop_cleanup_timer(),
    % 注销会话相关钩子
    unregister_hooks(),
    ok.

%% 确保会话相关的集合存在
ensure_collections() ->
    try
        % 获取集合名称
        SessionCollection = get_session_collection(),
        WillMessageCollection = get_will_message_collection(),

        ?SLOG(info, #{
            msg => "ensuring_session_collections",
            session_collection => SessionCollection,
            will_message_collection => WillMessageCollection
        }),

        % 集合会在第一次插入时自动创建，所以我们只需要记录日志
        % 不需要显式创建集合，避免连接器的查询处理问题

        % 索引会在需要时自动创建，暂时跳过
        ?SLOG(info, #{msg => "session_collections_ensured",
                     session_collection => SessionCollection,
                     will_message_collection => WillMessageCollection})
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_ensuring_collections",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {ensure_collections_failed, R}}
    end.

%% 创建必要的索引
create_indexes() ->
    % 获取集合名称
    SessionCollection = get_session_collection(),

    % 生成集合缩写 emqx_mqtt_sessions -> ems
    SessionAbbr = generate_collection_abbreviation(SessionCollection),

    ?SLOG(info, #{
        msg => "creating_session_indexes",
        collection => SessionCollection,
        collection_abbr => SessionAbbr
    }),

    % 定义会话集合索引
    SessionIndexes = [
        % 客户端ID唯一索引
        #{
            <<"key">> => #{<<"client_id">> => 1},
            <<"unique">> => true,
            <<"name">> => <<SessionAbbr/binary, "_client_id_unique">>
        },
        % 过期时间TTL索引
        #{
            <<"key">> => #{<<"expiry_time">> => 1},
            <<"expireAfterSeconds">> => 0,
            <<"name">> => <<SessionAbbr/binary, "_expiry_time_ttl">>
        },
        % 会话状态索引
        #{
            <<"key">> => #{<<"status">> => 1},
            <<"name">> => <<SessionAbbr/binary, "_status_asc">>
        },
        % 创建时间索引
        #{
            <<"key">> => #{<<"created_at">> => 1},
            <<"name">> => <<SessionAbbr/binary, "_created_at_asc">>
        }
    ],

    % 逐个创建索引
    lists:foreach(fun(IndexSpec) ->
        IndexName = maps:get(<<"name">>, IndexSpec),
        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {create_index, SessionCollection, IndexSpec}) of
            {ok, _} ->
                ?SLOG(info, #{
                    msg => "session_index_created",
                    collection => SessionCollection,
                    index_name => IndexName
                });
            {async_return, ok} ->
                ?SLOG(info, #{
                    msg => "session_index_created_async",
                    collection => SessionCollection,
                    index_name => IndexName
                });
            {async_return, {ok, _}} ->
                ?SLOG(info, #{
                    msg => "session_index_created_async",
                    collection => SessionCollection,
                    index_name => IndexName
                });
            {async_return, {error, Reason}} ->
                handle_session_index_error(SessionCollection, IndexName, Reason);
            {error, Reason} ->
                handle_session_index_error(SessionCollection, IndexName, Reason)
        end
    end, SessionIndexes).

%% 注册会话相关钩子
register_hooks() ->
    % 多层次钩子策略：确保在不同连接方式下都能正确处理会话持久化
    ?SLOG(info, #{msg => "registering_session_persistence_hooks"}),

    % 第一层：认证钩子 - 处理有用户名密码的连接
    % 优先级1，确保在EMQX查找会话之前执行
    emqx_hooks:add('client.authenticate', {?MODULE, on_client_authenticate, []}, 1),

    % 第二层：连接钩子 - 处理匿名连接和认证后的连接
    % 优先级5，在认证之后但在会话创建之前执行
    emqx_hooks:add('client.connect', {?MODULE, on_client_connect, []}, 5),

    % 会话相关钩子 - 使用更高的优先级
    emqx_hooks:add('session.created', {?MODULE, on_session_created, []}, 10),
    emqx_hooks:add('session.resumed', {?MODULE, on_session_resumed, []}, 10),
    emqx_hooks:add('session.discarded', {?MODULE, on_session_discarded, []}, 10),
    % 删除session.takenover钩子 - 让EMQX自然处理会话恢复
    % emqx_hooks:add('session.takenover', {?MODULE, on_session_takenover, []}, 10),
    emqx_hooks:add('session.terminated', {?MODULE, on_session_terminated, []}, 10),

    % 客户端连接/断开钩子 - 使用最高优先级确保会话恢复在其他操作之前执行
    emqx_hooks:add('client.connected', {?MODULE, on_client_connected, []}, 1),
    emqx_hooks:add('client.disconnected', {?MODULE, on_client_disconnected, []}, 1),

    ?SLOG(info, #{msg => "session_persistence_hooks_registered_successfully"}).

%% 注销会话相关钩子
unregister_hooks() ->
    try
        % 客户端认证和连接钩子
        safe_unhook('client.authenticate', {?MODULE, on_client_authenticate}),
        safe_unhook('client.connect', {?MODULE, on_client_connect}),

        % 会话相关钩子
        safe_unhook('session.created', {?MODULE, on_session_created}),
        safe_unhook('session.resumed', {?MODULE, on_session_resumed}),
        safe_unhook('session.discarded', {?MODULE, on_session_discarded}),
        % 删除session.takenover钩子注销 - 因为我们不再注册这个钩子
        % safe_unhook('session.takenover', {?MODULE, on_session_takenover}),
        safe_unhook('session.terminated', {?MODULE, on_session_terminated}),

        % 客户端连接/断开钩子
        safe_unhook('client.connected', {?MODULE, on_client_connected}),
        safe_unhook('client.disconnected', {?MODULE, on_client_disconnected}),

        ?SLOG(info, #{msg => "session_hooks_unregistered_successfully"})
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_unregistering_session_hooks",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% 安全地注销钩子
safe_unhook(HookPoint, Callback) ->
    try
        emqx_hooks:del(HookPoint, Callback)
    catch
        E:R ->
            ?SLOG(warning, #{
                msg => "failed_to_unregister_hook",
                hook_point => HookPoint,
                callback => Callback,
                error => E,
                reason => R
            })
    end.

%% ============================================================================
%% 辅助函数 - MongoDB到Mnesia数据同步
%% ============================================================================

%% @doc 从MongoDB查找持久会话数据
find_persistent_session_in_mongodb(ClientId) ->
    try
        Query = #{<<"client_id">> => ClientId},
        % 获取MongoDB连接
        case emqx_plugin_mongodb_api:get_mongodb_connection() of
            {ok, Connection} ->
                case emqx_plugin_mongodb_api:find_one(Connection, ?DEFAULT_SESSION_COLLECTION, Query) of
                    {ok, SessionDoc} when SessionDoc =/= null, SessionDoc =/= undefined ->
                        {ok, SessionDoc};
                    {ok, _} ->
                        {error, not_found};
                    {error, Reason} ->
                        {error, Reason}
                end;
            {error, Reason} ->
                {error, {connection_failed, Reason}}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "exception_querying_mongodb_for_session",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {exception, E, R}}
    end.

%% @doc 将MongoDB会话数据注入到EMQX的持久会话存储中
%% 这样EMQX在查找会话时就能找到数据
inject_session_to_emqx_storage(ClientId, SessionData, ClientInfo) ->
    try
        % 检查EMQX持久会话是否启用
        case emqx_persistent_message:is_persistence_enabled() of
            true ->
                % 从MongoDB数据中提取必要信息
                Now = erlang:system_time(millisecond),
                CreatedAt = maps:get(<<"created_at">>, SessionData, Now),
                % 关键修复：使用当前时间作为last_alive_at，确保会话不会被认为已过期
                LastAliveAt = Now,
                SessionExpiryInterval = maps:get(<<"session_expiry_interval">>, SessionData, 7200),

                % 构造ConnInfo
                ConnInfo = #{
                    clean_start => false,
                    expiry_interval => SessionExpiryInterval * 1000,  % 转换为毫秒
                    receive_maximum => 65535,
                    proto_ver => 4
                },

                % 构造session记录（使用EMQX持久会话的格式）
                SessionRecord = #session{
                    id = ClientId,
                    created_at = CreatedAt,
                    last_alive_at = LastAliveAt,
                    conninfo = ConnInfo,
                    props = #{}
                },

                ?SLOG(info, #{
                    msg => "injecting_session_to_emqx_persistent_storage",
                    client_id => ClientId,
                    session_expiry_interval => SessionExpiryInterval
                }),

                % 直接写入EMQX的持久会话存储
                case mnesia:transaction(fun() ->
                    mnesia:write(emqx_ds_session, SessionRecord, write)
                end) of
                    {atomic, ok} ->
                        ?SLOG(info, #{
                            msg => "successfully_injected_session_to_emqx_storage",
                            client_id => ClientId
                        }),
                        ok;
                    {aborted, Reason} ->
                        ?SLOG(error, #{
                            msg => "failed_to_inject_session_to_emqx_storage",
                            client_id => ClientId,
                            reason => Reason
                        }),
                        {error, Reason}
                end;
            false ->
                ?SLOG(warning, #{
                    msg => "emqx_persistent_session_disabled_cannot_inject",
                    client_id => ClientId
                }),
                ok
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "exception_injecting_session_to_emqx_storage",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {exception, E, R}}
    end.

%% @doc 从MongoDB恢复订阅信息
restore_subscriptions_from_mongodb(ClientId, SessionData) ->
    try
        % 检查是否有订阅数据
        case maps:get(<<"subscriptions">>, SessionData, #{}) of
            Subscriptions when map_size(Subscriptions) > 0 ->
                ?SLOG(info, #{
                    msg => "restoring_subscriptions_from_mongodb",
                    client_id => ClientId,
                    subscription_count => map_size(Subscriptions)
                }),

                % 这里可以添加订阅恢复逻辑
                % 但是通常订阅会在客户端重连时自动恢复
                ok;
            _ ->
                ?SLOG(debug, #{
                    msg => "no_subscriptions_to_restore",
                    client_id => ClientId
                }),
                ok
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "exception_restoring_subscriptions",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            ok
    end.

%% @doc 等待EMQX核心组件和MongoDB完全就绪
%% 这是解决会话恢复时机问题的关键函数
wait_for_emqx_and_mongodb_ready() ->
    ?SLOG(info, #{msg => "waiting_for_emqx_and_mongodb_ready"}),

    % 等待基本时间，确保EMQX核心组件启动
    timer:sleep(3000),

    % 检查EMQX持久会话是否启用
    wait_for_emqx_persistent_session_ready(),

    % 检查MongoDB连接是否就绪
    wait_for_mongodb_connection_ready(),

    ?SLOG(info, #{msg => "emqx_and_mongodb_ready_session_restoration_can_proceed"}).

%% @doc 等待EMQX持久会话功能就绪
wait_for_emqx_persistent_session_ready() ->
    wait_for_emqx_persistent_session_ready(30). % 最多等待30秒

wait_for_emqx_persistent_session_ready(0) ->
    ?SLOG(warning, #{msg => "timeout_waiting_for_emqx_persistent_session"}),
    ok;
wait_for_emqx_persistent_session_ready(Retries) ->
    try
        case emqx_persistent_message:is_persistence_enabled() of
            true ->
                % 检查持久会话表是否存在
                case lists:member(emqx_ds_session, mnesia:system_info(tables)) of
                    true ->
                        ?SLOG(info, #{msg => "emqx_persistent_session_ready"}),
                        ok;
                    false ->
                        ?SLOG(debug, #{msg => "waiting_for_emqx_ds_session_table"}),
                        timer:sleep(1000),
                        wait_for_emqx_persistent_session_ready(Retries - 1)
                end;
            false ->
                ?SLOG(warning, #{msg => "emqx_persistent_session_disabled"}),
                ok
        end
    catch
        _:_ ->
            timer:sleep(1000),
            wait_for_emqx_persistent_session_ready(Retries - 1)
    end.

%% @doc 等待MongoDB连接就绪
wait_for_mongodb_connection_ready() ->
    wait_for_mongodb_connection_ready(30). % 最多等待30秒

wait_for_mongodb_connection_ready(0) ->
    ?SLOG(warning, #{msg => "timeout_waiting_for_mongodb_connection"}),
    ok;
wait_for_mongodb_connection_ready(Retries) ->
    try
        case emqx_plugin_mongodb_api:get_mongodb_connection() of
            {ok, _Connection} ->
                ?SLOG(info, #{msg => "mongodb_connection_ready"}),
                ok;
            {error, _Reason} ->
                ?SLOG(debug, #{msg => "waiting_for_mongodb_connection"}),
                timer:sleep(1000),
                wait_for_mongodb_connection_ready(Retries - 1)
        end
    catch
        _:_ ->
            timer:sleep(1000),
            wait_for_mongodb_connection_ready(Retries - 1)
    end.

%% @doc 在断开连接时预先加载会话数据到EMQX存储
%% 这是解决session_present时机问题的关键方法
preload_session_for_next_connection(ClientInfo) ->
    ClientId = maps:get(clientid, ClientInfo, <<>>),

    ?SLOG(info, #{
        msg => "preloading_session_for_next_connection",
        client_id => ClientId
    }),

    try
        % 异步执行，避免阻塞断开连接流程
        spawn(fun() ->
            % 等待一小段时间，确保MongoDB更新完成
            timer:sleep(100),

            % 从MongoDB获取最新的会话数据
            case find_persistent_session_in_mongodb(ClientId) of
                {ok, SessionData} ->
                    % 检查是否为持久会话
                    case maps:get(<<"session_expiry_interval">>, SessionData, 0) of
                        ExpiryInterval when ExpiryInterval > 0 ->
                            % 预先注入到EMQX存储中
                            inject_session_to_emqx_storage(ClientId, SessionData, ClientInfo),

                            ?SLOG(info, #{
                                msg => "session_preloaded_for_next_connection",
                                client_id => ClientId,
                                expiry_interval => ExpiryInterval
                            });
                        _ ->
                            ?SLOG(debug, #{
                                msg => "non_persistent_session_not_preloaded",
                                client_id => ClientId
                            })
                    end;
                {error, not_found} ->
                    ?SLOG(debug, #{
                        msg => "no_session_to_preload",
                        client_id => ClientId
                    });
                {error, Reason} ->
                    ?SLOG(warning, #{
                        msg => "failed_to_preload_session",
                        client_id => ClientId,
                        reason => Reason
                    })
            end
        end)
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "exception_preloading_session",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% ============================================================================
%% 钩子回调函数
%% ============================================================================

%% 客户端认证钩子回调 - 在会话查找之前恢复持久化会话
on_client_authenticate(ClientInfo, AuthResult) ->
    ClientId = maps:get(clientid, ClientInfo, <<>>),

    ?SLOG(info, #{
        msg => "client_authenticate_checking_persistent_session",
        client_id => ClientId
    }),

    % 关键修复：使用延迟注入，确保在EMQX会话查找之前注入数据
    % 这是解决时机问题的关键方法
    spawn(fun() ->
        % 短暂延迟，让认证完成但在会话查找之前注入
        timer:sleep(10),
        try
            restore_session_before_emqx_lookup(ClientInfo)
        catch
            E:R:S ->
                ?SLOG(error, #{
                    msg => "failed_to_inject_session_with_delay_in_authenticate",
                    client_id => ClientId,
                    error => E,
                    reason => R,
                    stacktrace => S
                })
        end
    end),

    % 返回认证结果，不修改认证逻辑
    AuthResult.

%% 客户端连接钩子回调 - 处理匿名连接和认证后的连接
%% 这个钩子在client.authenticate之后、session.created之前触发
%% 确保无论是否有认证都能正确处理会话持久化
on_client_connect(ClientInfo, _ConnInfo) ->
    ClientId = maps:get(clientid, ClientInfo, <<>>),
    Username = maps:get(username, ClientInfo, undefined),

    ?SLOG(info, #{
        msg => "client_connect_hook_called",
        client_id => ClientId,
        username => Username
    }),

    % 判断连接类型
    ConnectionType = case Username of
        undefined -> anonymous;
        <<>> -> anonymous;
        _ -> authenticated
    end,

    ?SLOG(info, #{
        msg => "client_connect_session_check",
        client_id => ClientId,
        connection_type => ConnectionType,
        username => Username
    }),

    % 对于匿名连接，也需要检查持久化会话
    % 因为client.authenticate钩子可能不会被触发
    case ConnectionType of
        anonymous ->
            % 对于匿名连接，在client.connect钩子中注入会话数据
            % 因为匿名连接不会触发client.authenticate钩子
            try
                restore_session_before_emqx_lookup(ClientInfo)
            catch
                E:R:S ->
                    ?SLOG(error, #{
                        msg => "failed_to_inject_session_before_emqx_lookup_in_connect",
                        client_id => ClientId,
                        connection_type => anonymous,
                        error => E,
                        reason => R,
                        stacktrace => S
                    })
            end;
        authenticated ->
            % 认证连接的会话注入在client.authenticate钩子中处理
            % 这里不需要重复注入，避免竞争条件
            ?SLOG(debug, #{
                msg => "authenticated_connection_session_injection_handled_in_authenticate_hook",
                client_id => ClientId
            })
    end,

    % 返回连接信息，不修改连接逻辑
    ok.

%% 检查并恢复持久化会话（如果存在）
restore_persistent_session_if_exists(ClientId) ->
    try
        SessionCollection = get_session_collection(),
        Filter = #{
            <<"client_id">> => ClientId,
            <<"clean_start">> => false,
            <<"expiry_time">> => #{<<"$gt">> => erlang:system_time(millisecond)}
        },

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {find_one, SessionCollection, Filter}) of
            {ok, SessionDoc} when SessionDoc =/= null ->
                ?SLOG(info, #{
                    msg => "found_persistent_session_restoring_to_emqx",
                    client_id => ClientId
                }),

                % 记录会话信息供调试
                % 实际的会话恢复在EMQX启动时批量进行
                try
                    % 恢复订阅信息
                    Subscriptions = maps:get(<<"subscriptions">>, SessionDoc, []),

                    % 使用emqx_broker API恢复订阅
                    lists:foreach(fun(SubDoc) ->
                        Topic = maps:get(<<"topic">>, SubDoc, <<>>),
                        QoS = maps:get(<<"qos">>, SubDoc, 0),
                        SubOpts = #{
                            qos => QoS,
                            rh => maps:get(<<"rh">>, SubDoc, 0),
                            rap => maps:get(<<"rap">>, SubDoc, 0),
                            nl => maps:get(<<"nl">>, SubDoc, 0)
                        },

                        % 预先注册订阅到路由表
                        case emqx_broker:subscribe(Topic, ClientId, SubOpts) of
                            ok ->
                                ?SLOG(debug, #{
                                    msg => "subscription_restored_to_broker",
                                    client_id => ClientId,
                                    topic => Topic,
                                    qos => QoS
                                });
                            {error, Reason} ->
                                ?SLOG(warning, #{
                                    msg => "failed_to_restore_subscription",
                                    client_id => ClientId,
                                    topic => Topic,
                                    reason => Reason
                                })
                        end
                    end, Subscriptions),

                    ?SLOG(info, #{
                        msg => "persistent_session_subscriptions_restored",
                        client_id => ClientId,
                        subscriptions_count => length(Subscriptions)
                    })
                catch
                    E:R:S ->
                        ?SLOG(error, #{
                            msg => "failed_to_restore_persistent_session",
                            client_id => ClientId,
                            error => E,
                            reason => R,
                            stacktrace => S
                        })
                end;
            {ok, null} ->
                ?SLOG(debug, #{
                    msg => "no_persistent_session_found",
                    client_id => ClientId
                });
            ok ->
                % 处理某些MongoDB驱动返回ok而不是{ok, null}的情况
                ?SLOG(debug, #{
                    msg => "no_persistent_session_found_ok_response",
                    client_id => ClientId
                });
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "error_querying_persistent_session",
                    client_id => ClientId,
                    reason => Reason
                })
        end
    catch
        Error:ErrorReason:Stack ->
            ?SLOG(error, #{
                msg => "error_in_restore_persistent_session_if_exists",
                client_id => ClientId,
                error => Error,
                reason => ErrorReason,
                stacktrace => Stack
            })
    end.

%% 会话创建钩子回调
on_session_created(ClientInfo, SessInfo) ->
    ClientId = maps:get(clientid, ClientInfo, <<>>),

    % 修复MQTT 5.0会话持久化问题：
    % 1. 从SessInfo获取基本的clean_start信息
    % 2. 检查是否有Session Expiry Interval（MQTT 5.0特性）
    % 3. 如果有Session Expiry Interval > 0，则认为是持久会话
    BasicCleanStart = maps:get(clean_start, SessInfo, true),

    % 获取连接信息以检查MQTT 5.0属性
    CleanStart = case emqx_cm:lookup_channels(ClientId) of
        [] ->
            % 没有找到活跃连接，使用基本信息
            BasicCleanStart;
        [Pid | _] ->
            % 从活跃连接中获取详细信息
            try
                case emqx_connection:info(Pid, [clean_start, expiry_interval, proto_ver, conn_props]) of
                    #{proto_ver := 5, expiry_interval := ExpiryInterval} when ExpiryInterval > 0 ->
                        % MQTT 5.0且有Session Expiry Interval，这是持久会话
                        false;
                    #{clean_start := CS} ->
                        CS;
                    _ ->
                        BasicCleanStart
                end
            catch
                _:_ -> BasicCleanStart
            end
    end,

    ?SLOG(info, #{
        msg => "session_created",
        client_id => ClientId,
        clean_start => CleanStart,
        basic_clean_start => BasicCleanStart
    }),

    % 异步处理会话创建和可能的恢复
    spawn(fun() ->
        try
            case CleanStart of
                true ->
                    % 清理会话：删除旧的持久化数据，但不需要再次调用save_session
                    % 因为save_session已经在session.created钩子中被调用了
                    ?SLOG(info, #{msg => "clean_start_session_deleting_old_data", client_id => ClientId}),
                    delete_session_data(ClientId);
                false ->
                    % 持久会话：检查是否有需要恢复的数据
                    ?SLOG(info, #{msg => "persistent_session_checking_for_restoration", client_id => ClientId}),
                    case check_and_restore_session_data(ClientId) of
                        {restored, _} ->
                            ?SLOG(info, #{msg => "session_data_restored_from_mongodb", client_id => ClientId}),
                            % 更新现有会话数据
                            update_session_after_restoration(ClientInfo, SessInfo);
                        no_data ->
                            ?SLOG(info, #{msg => "no_session_data_to_restore_saving_new", client_id => ClientId}),
                            % 没有旧数据，保存新会话
                            save_session(ClientInfo, SessInfo)
                    end
            end,

            % 更新会话统计信息
            update_session_stats(ClientId, connect)
        catch
            E:R:S ->
                ?SLOG(error, #{
                    msg => "error_in_session_created_handler",
                    client_id => ClientId,
                    error => E,
                    reason => R,
                    stacktrace => S
                })
        end
    end),
    ok.

%% 会话恢复钩子回调
on_session_resumed(ClientInfo, _SessInfo) ->
    ClientId = maps:get(clientid, ClientInfo, <<>>),
    ?SLOG(info, #{msg => "session_resumed_restoring_from_mongodb", client_id => ClientId}),

    % 异步恢复会话数据
    spawn(fun() ->
        try
            % 1. 从MongoDB恢复订阅数据
            restore_subscriptions_for_client(ClientId),

            % 2. 从MongoDB恢复未确认消息
            restore_inflight_messages_for_client(ClientId),

            % 3. 更新会话状态，包含历史记录
            update_session_status_with_history(ClientInfo, <<"resumed">>, normal),

            % 4. 更新会话统计信息
            update_session_stats(ClientId, connect),

            ?SLOG(info, #{msg => "session_data_restored_successfully", client_id => ClientId})
        catch
            E:R:S ->
                ?SLOG(error, #{
                    msg => "error_restoring_session_data",
                    client_id => ClientId,
                    error => E,
                    reason => R,
                    stacktrace => S
                })
        end
    end),
    ok.

%% 会话丢弃钩子回调
on_session_discarded(ClientInfo, SessInfo) ->
    ?SLOG(debug, #{msg => "session_discarded", client_info => ClientInfo, session_info => SessInfo}),
    % 更新会话状态，包含历史记录
    spawn(fun() ->
        % 使用增强的状态更新函数
        update_session_status_with_history(ClientInfo, <<"discarded">>, normal),
        % 更新会话统计信息
        ClientId = maps:get(clientid, ClientInfo, <<>>),
        update_session_stats(ClientId, disconnect)
    end),
    ok.

%% 会话接管钩子回调 - 已删除，让EMQX自然处理会话恢复
%% 这样可以避免干扰EMQX的session_present逻辑
%% on_session_takenover函数已删除

%% 会话终止钩子回调
on_session_terminated(ClientInfo, Reason, SessInfo) ->
    ?SLOG(debug, #{msg => "session_terminated", client_info => ClientInfo, reason => Reason, session_info => SessInfo}),
    % 更新会话状态，包含历史记录
    spawn(fun() ->
        % 使用增强的状态更新函数
        update_session_status_with_history(ClientInfo, <<"terminated">>, Reason),
        % 更新会话统计信息
        ClientId = maps:get(clientid, ClientInfo, <<>>),
        update_session_stats(ClientId, disconnect)
    end),
    ok.

%% 客户端连接钩子回调
on_client_connected(ClientInfo, ConnInfo) ->
    % 使用EMQX日志系统来验证钩子是否被调用
    ClientId = maps:get(clientid, ClientInfo, <<"unknown">>),
    ?SLOG(warning, #{msg => "CLIENT_CONNECTED_HOOK_TRIGGERED", client_id => ClientId}),

    ?SLOG(info, #{msg => "client_connected_hook_called", client_info => ClientInfo, conn_info => ConnInfo}),
    % 更新会话状态，包含历史记录
    spawn(fun() ->
        CleanStart = maps:get(clean_start, ConnInfo, true),

        ?SLOG(info, #{msg => "client_connected_debug",
                     client_id => ClientId,
                     clean_start => CleanStart,
                     conn_info_keys => maps:keys(ConnInfo)}),

        % 如果是持久会话（clean_start=false），确保会话记录存在
        case CleanStart of
            false ->
                ?SLOG(info, #{msg => "persistent_session_detected_creating_session_record",
                             client_id => ClientId}),
                % 创建会话记录（模拟session.created钩子的功能）
                SessionExpiryInterval = maps:get(session_expiry_interval, ConnInfo, 7200),
                SessInfo = #{
                    clean_start => false,
                    session_expiry_interval => SessionExpiryInterval
                },
                save_session(ClientInfo, SessInfo);
            true ->
                ?SLOG(info, #{msg => "clean_session_no_persistence_needed",
                              client_id => ClientId})
        end,

        % 只有持久会话才需要更新状态和统计信息
        case CleanStart of
            false ->
                % 持久会话：更新状态和统计信息
                update_session_status_with_history(ClientInfo, <<"connected">>, normal),
                update_session_stats(ClientId, connect),

                % 获取集合名称
                SessionCollection = get_session_collection(),

                % 记录连接信息
                ConnDoc = #{
                    <<"$set">> => #{
                        <<"is_online">> => true,
                <<"last_connected_at">> => erlang:system_time(millisecond),
                <<"conn_props">> => #{
                    <<"proto_ver">> => maps:get(proto_ver, ConnInfo, <<>>),
                    <<"proto_name">> => maps:get(proto_name, ConnInfo, <<>>),
                    <<"keepalive">> => maps:get(keepalive, ConnInfo, 0),
                    <<"clean_start">> => maps:get(clean_start, ConnInfo, true),
                    <<"conn_module">> => atom_to_binary(maps:get(conn_mod, ConnInfo, undefined), utf8),
                    <<"ip_address">> => format_peername(maps:get(peername, ConnInfo, undefined))
                }
            }
        },

                % 更新连接信息
                case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                      {update, SessionCollection,
                                       #{<<"client_id">> => ClientId},
                                       ConnDoc}) of
                    {ok, _} ->
                        ?SLOG(debug, #{msg => "connection_info_updated", client_id => ClientId});
                    {async_return, ok} ->
                        ?SLOG(debug, #{msg => "connection_info_update_in_progress", client_id => ClientId});
                    {error, Reason} ->
                        ?SLOG(error, #{msg => "failed_to_update_connection_info",
                                     client_id => ClientId,
                                     reason => Reason})
                end;
            true ->
                % 清理会话：不执行任何持久化操作
                ?SLOG(info, #{msg => "clean_session_no_persistence_operations",
                              client_id => ClientId})
        end
    end),

    % 检查是否需要重发未确认消息
    case application:get_env(emqx_plugin_mongodb, message_persistence_enabled, false) of
        true ->
            % 获取配置
            Config = emqx_plugin_mongodb:read_config(),
            MessageConfig = maps:get(message_persistence, maps:get(connection, Config, #{}), #{}),

            % 检查是否启用了自动重发
            case maps:get(auto_resend_unacked, MessageConfig, true) of
                true ->
                    % 获取客户端ID
                    ClientId = maps:get(clientid, ClientInfo, undefined),
                    case ClientId of
                        undefined -> ok;
                        _ ->
                            % 异步重发未确认消息，使用重试机制
                            spawn(fun() ->
                                % 等待一段时间确保客户端已准备好接收消息
                                timer:sleep(1000),
                                % 确保消息模块已导出重发函数
                                case erlang:function_exported(emqx_plugin_mongodb_message, resend_unacked_messages, 1) of
                                    true ->
                                        % 使用事务支持模块执行重发
                                        ResendFun = fun() ->
                                            emqx_plugin_mongodb_message:resend_unacked_messages(ClientId)
                                        end,
                                        % 使用重试机制
                                        emqx_plugin_mongodb_transaction:with_retry(ResendFun, [], 3);
                                    false ->
                                        ?SLOG(warning, #{msg => "resend_function_not_available", client_id => ClientId})
                                end
                            end)
                    end;
                false ->
                    ok
            end;
        false ->
            ok
    end,
    ok.

%% @doc 格式化对等方名称（IP地址和端口）
format_peername(undefined) -> <<>>;
format_peername({{A, B, C, D}, Port}) ->
    % IPv4
    list_to_binary(io_lib:format("~p.~p.~p.~p:~p", [A, B, C, D, Port]));
format_peername({IP, Port}) when is_tuple(IP), tuple_size(IP) =:= 8 ->
    % IPv6
    list_to_binary(io_lib:format("~s:~p", [inet:ntoa(IP), Port]));
format_peername(Peername) ->
    % 其他格式
    list_to_binary(io_lib:format("~p", [Peername])).

%% 客户端断开连接钩子回调
on_client_disconnected(ClientInfo, Reason, _Env) ->
    ?SLOG(debug, #{msg => "client_disconnected", client_info => ClientInfo, reason => Reason}),
    try
        ClientId = maps:get(clientid, ClientInfo),

        % 更新会话状态，包含历史记录
        update_session_status_with_history(ClientInfo, <<"disconnected">>, Reason),

        % 更新会话统计信息
        update_session_stats(ClientId, disconnect),

        % 关键修复：在断开连接时预先注入会话数据到EMQX存储
        % 这样客户端重连时EMQX就能立即找到会话
        preload_session_for_next_connection(ClientInfo),

        % 记录断开连接时间，用于计算总在线时间
        Now = erlang:system_time(millisecond),

        % 获取集合名称
        SessionCollection = get_session_collection(),

        % 构造更新文档，添加更多信息
        UpdateDoc = #{
            <<"$set">> => #{
                <<"disconnected_at">> => Now,
            <<"disconnect_reason">> => format_reason(Reason),
                <<"updated_at">> => Now,
                <<"is_online">> => false
            }
        },

        % 使用事务支持模块执行更新
        Fun = fun() ->
            emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                {update, SessionCollection,
                 #{<<"client_id">> => ClientId},
                 UpdateDoc})
        end,

        % 使用重试机制执行
        case emqx_plugin_mongodb_transaction:with_retry(Fun, [], 3) of
            {ok, {ok, _}} ->
                ?SLOG(debug, #{msg => "session_status_updated_to_disconnected", client_id => ClientId}),

                % 处理遗嘱消息：根据断开原因和会话中的遗嘱消息标识决定是否删除
                % 注意：遗嘱消息模块的断开钩子优先级是150，会在我们之后执行
                handle_will_message_on_disconnect(ClientId, Reason);
            {error, UpdateReason} ->
                ?SLOG(error, #{msg => "failed_to_update_session_status", client_id => ClientId, reason => UpdateReason})
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_handling_client_disconnected",
                error => E,
                reason => R,
                stacktrace => S
            })
    end,
    ok.



%% @doc 根据断开原因和会话状态处理遗嘱消息
handle_will_message_on_disconnect(ClientId, Reason) ->
    try
        % 首先查询会话信息，确认是否有遗嘱消息
        SessionCollection = get_session_collection(),
        Filter = #{<<"client_id">> => ClientId},

        % 会话持久化不再处理遗嘱消息，遗嘱消息由专门的遗嘱消息模块处理
        ?SLOG(debug, #{
            msg => "session_disconnected_will_message_handled_by_dedicated_module",
            client_id => ClientId,
            reason => Reason
        })
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_handling_will_message_on_disconnect",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.



%% @doc 保存MQTT会话信息到MongoDB - 类似于Java的JPA save操作
%%
%% 在Java中的等价实现：
%% @Service
%% public class SessionPersistenceService {
%%     @Autowired private SessionRepository sessionRepository;
%%
%%     public void saveSession(ClientInfo clientInfo, SessionInfo sessionInfo) {
%%         try {
%%             String clientId = clientInfo.getClientId();
%%
%%             // 构建会话实体对象
%%             SessionEntity session = SessionEntity.builder()
%%                 .clientId(clientId)
%%                 .username(clientInfo.getUsername())
%%                 .cleanStart(sessionInfo.isCleanStart())
%%                 .connectedAt(sessionInfo.getCreatedAt())
%%                 .node(getCurrentNode())
%%                 .status("connected")
%%                 .updatedAt(System.currentTimeMillis())
%%                 .build();
%%
%%             // 计算过期时间
%%             long expiryInterval = sessionInfo.getExpiryInterval();
%%             long expiryTime = (expiryInterval == 0) ? 0 :
%%                 System.currentTimeMillis() + expiryInterval;
%%             session.setExpiryTime(expiryTime);
%%
%%             // 使用upsert操作保存到数据库
%%             sessionRepository.upsert(session);
%%             logger.debug("Session saved for client: {}", clientId);
%%
%%         } catch (Exception e) {
%%             logger.error("Error saving session", e);
%%         }
%%     }
%% }
%%
%% @param ClientInfo MQTT客户端信息 - 类似于Java的ClientInfo对象
%% @param SessInfo MQTT会话信息 - 类似于Java的SessionInfo对象
save_session(ClientInfo, SessInfo) ->
    try
        %% 提取客户端ID和Clean Start标志
        %% 修复：根据EMQX源码，clean_start和expiry_interval在SessInfo中
        ClientId = maps:get(clientid, ClientInfo),
        CleanStart = maps:get(clean_start, SessInfo, true),
        ExpiryInterval = maps:get(expiry_interval, SessInfo, 0),

        ?SLOG(debug, #{
            msg => "saving_session_with_mqtt_protocol_compliance",
            client_id => ClientId,
            clean_start => CleanStart,
            expiry_interval => ExpiryInterval,
            sess_info_keys => maps:keys(SessInfo),
            client_info_keys => maps:keys(ClientInfo),
            sess_info_content => SessInfo,
            client_info_content => ClientInfo
        }),

        %% 根据MQTT协议的Clean Start语义处理会话
        case CleanStart of
            true ->
                %% Clean Start = true: 删除旧会话，创建新的非持久会话
                %% 根据MQTT协议，Clean Start会话不应该被持久化
                handle_clean_start_session(ClientId, ClientInfo, SessInfo);
            false ->
                %% Clean Start = false: 创建或更新持久会话
                handle_persistent_session(ClientId, ClientInfo, SessInfo)
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_saving_session",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 处理Clean Start会话
%% 根据MQTT协议，Clean Start = true的会话不应该被持久化
handle_clean_start_session(ClientId, ClientInfo, SessInfo) ->
    ?SLOG(debug, #{
        msg => "handling_clean_start_session",
        client_id => ClientId,
        note => "clean_start_sessions_not_persisted_per_mqtt_protocol"
    }),

    %% 删除任何现有的持久会话记录
    SessionCollection = get_session_collection(),
    case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                           {delete, SessionCollection, #{<<"client_id">> => ClientId}}) of
        {ok, _} ->
            ?SLOG(debug, #{
                msg => "existing_persistent_session_deleted_for_clean_start",
                client_id => ClientId
            });
        {error, Reason} ->
            ?SLOG(warning, #{
                msg => "failed_to_delete_existing_session_for_clean_start",
                client_id => ClientId,
                reason => Reason
            })
    end.

%% @doc 处理持久会话
%% 根据MQTT协议和Session Expiry Interval处理持久会话
handle_persistent_session(ClientId, ClientInfo, SessInfo) ->
    %% 获取MQTT协议中的Session Expiry Interval
    SessionExpiryInterval = maps:get(session_expiry_interval, SessInfo, 0),

    ?SLOG(debug, #{
        msg => "handling_persistent_session",
        client_id => ClientId,
        session_expiry_interval => SessionExpiryInterval
    }),

    case SessionExpiryInterval of
        0 ->
            %% Session Expiry Interval = 0: 会话在网络连接关闭时结束
            %% 不应该持久化这种会话
            ?SLOG(debug, #{
                msg => "session_expiry_interval_zero_not_persisting",
                client_id => ClientId,
                note => "session_ends_when_network_connection_closes"
            });
        _ ->
            %% Session Expiry Interval > 0: 创建持久会话
            create_persistent_session(ClientId, ClientInfo, SessInfo, SessionExpiryInterval)
    end.

%% @doc 创建持久会话
create_persistent_session(ClientId, ClientInfo, SessInfo, SessionExpiryInterval) ->
    %% 计算会话过期时间（按MQTT协议，单位是秒）
    ExpiryTime = case SessionExpiryInterval of
        16#FFFFFFFF ->
            %% 0xFFFFFFFF = 会话永不过期
            0;
        Seconds when Seconds > 0 ->
            %% 转换为毫秒并计算过期时间
            erlang:system_time(millisecond) + (Seconds * 1000)
    end,

    %% 构建会话文档
    SessionDoc = #{
        <<"client_id">> => ClientId,
        <<"username">> => maps:get(username, ClientInfo, <<>>),
        <<"clean_start">> => false, % 持久会话总是false
        <<"session_expiry_interval">> => SessionExpiryInterval,
        <<"expiry_time">> => ExpiryTime,
        <<"connected_at">> => maps:get(created_at, SessInfo, erlang:system_time(millisecond)),
        <<"node">> => atom_to_binary(node(), utf8),
        <<"status">> => <<"connected">>,
        <<"updated_at">> => erlang:system_time(millisecond)
    },

    %% 保存到MongoDB
    SessionCollection = get_session_collection(),
    case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                           {upsert, SessionCollection, #{<<"client_id">> => ClientId}, SessionDoc}) of
        {ok, _} ->
            ?SLOG(debug, #{
                msg => "persistent_session_saved",
                client_id => ClientId,
                session_expiry_interval => SessionExpiryInterval
            });
        {async_return, ok} ->
            ?SLOG(debug, #{msg => "persistent_session_saved_async", client_id => ClientId});
        {async_return, {ok, _}} ->
            ?SLOG(debug, #{msg => "persistent_session_saved_async", client_id => ClientId});
        {error, Reason} ->
            ?SLOG(error, #{
                msg => "failed_to_save_persistent_session",
                client_id => ClientId,
                reason => Reason
            });
        {async_return, {error, Reason}} ->
            ?SLOG(error, #{
                msg => "failed_to_save_persistent_session_async",
                client_id => ClientId,
                reason => Reason
            })
    end.

%% 更新会话状态
update_session_status(ClientInfo, Status) ->
    update_session_status(ClientInfo, Status, undefined).

%% 更新会话状态（带原因）
update_session_status(ClientInfo, Status, Reason) ->
    try
        ClientId = maps:get(clientid, ClientInfo),

        % 构造更新文档
        UpdateDoc = #{
            <<"status">> => Status,
            <<"updated_at">> => erlang:system_time(millisecond)
        },

        % 如果提供了原因，添加到文档中
        UpdateDoc2 = case Reason of
            undefined -> UpdateDoc;
            _ -> UpdateDoc#{<<"reason">> => format_reason(Reason)}
        end,

        % 获取集合名称
        SessionCollection = get_session_collection(),

        % 更新会话状态
        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                {update_session_status, SessionCollection, ClientId, UpdateDoc2}) of
            {ok, _} ->
                ?SLOG(debug, #{msg => "session_status_updated", client_id => ClientId, status => Status});
            {error, UpdateReason} ->
                ?SLOG(error, #{msg => "failed_to_update_session_status", client_id => ClientId, status => Status, reason => UpdateReason})
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_updating_session_status",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% 格式化断开连接原因
format_reason(normal) -> <<"normal">>;
format_reason(shutdown) -> <<"shutdown">>;
format_reason({shutdown, Reason}) when is_atom(Reason) ->
                atom_to_binary(Reason, utf8);
format_reason({shutdown, Reason}) when is_binary(Reason) ->
    Reason;
format_reason({shutdown, Reason}) ->
    list_to_binary(io_lib:format("~p", [Reason]));
format_reason(Reason) when is_atom(Reason) ->
    atom_to_binary(Reason, utf8);
format_reason(Reason) when is_binary(Reason) ->
    Reason;
format_reason(Reason) ->
    list_to_binary(io_lib:format("~p", [Reason])).

%% @doc 处理系统重启后的持久化会话
%% 修复：采用方案A - 纯持久化方案
%%
%% 功能说明：
%% 1. 不直接恢复会话，避免与EMQX内置会话管理冲突
%% 2. 记录持久化会话的信息供管理员参考
%% 3. 让EMQX在客户端重连时自然恢复会话状态
%%
%% 设计原理：
%% - 插件只负责持久化，不干扰EMQX的会话管理逻辑
%% - EMQX的内置会话管理会在客户端重连时自动处理会话恢复
%% - 避免会话状态冲突和重复管理
%%
%% 修复说明：
%% - 原方案：插件直接创建和恢复EMQX会话 → 可能与EMQX内置功能严重冲突
%% - 新方案：插件只记录信息 → 让EMQX内置功能处理
%%
%% Java等价概念：
%% @PostConstruct
%% public void handlePersistedSessionsAfterRestart() {
%%     List<PersistedSession> sessions = sessionRepository.findActiveSessions();
%%
%%     // 不直接恢复会话，而是记录信息供管理员参考
%%     for (PersistedSession session : sessions) {
%%         logger.warn("Found persisted session after restart: client={}, status={}",
%%                    session.getClientId(), session.getStatus());
%%     }
%%
%%     // 让MQTT broker在客户端重连时自然恢复会话
%% }
handle_persisted_sessions_after_restart() ->
    ?SLOG(info, #{
        msg => "restoring_persistent_sessions_after_emqx_restart",
        approach => "batch_restore_sessions_to_emqx_persistent_storage"
    }),
    try
        %% 关键修复：在EMQX启动时将所有MongoDB中的持久会话数据恢复到EMQX的持久会话存储中
        %% 这样可以处理EMQX异常重启的情况，确保客户端重连时能正确获得session_present=true

        SessionCollection = get_session_collection(),
        Now = erlang:system_time(millisecond),

        %% 第一步：清理过期的会话数据
        ExpiredFilter = #{
            <<"expiry_time">> => #{
                <<"$lt">> => Now,
                <<"$ne">> => 0  % 不删除永不过期的会话
            }
        },

        ?SLOG(info, #{
            msg => "cleaning_expired_sessions",
            filter => ExpiredFilter,
            current_time => Now
        }),

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                {delete, SessionCollection, ExpiredFilter}) of
            {ok, #{<<"deletedCount">> := DeletedCount}} ->
                ?SLOG(info, #{
                    msg => "expired_sessions_cleaned",
                    deleted_count => DeletedCount
                });
            {ok, #{deleted_count := DeletedCount}} ->
                ?SLOG(info, #{
                    msg => "expired_sessions_cleaned",
                    deleted_count => DeletedCount
                });
            {ok, Result} when is_map(Result) ->
                % 处理其他格式的删除结果
                DeletedCount = maps:get(<<"deletedCount">>, Result,
                              maps:get(deleted_count, Result, 0)),
                ?SLOG(info, #{
                    msg => "expired_sessions_cleaned",
                    deleted_count => DeletedCount,
                    result_format => "alternative"
                });
            {error, Reason} ->
                ?SLOG(warning, #{
                    msg => "failed_to_clean_expired_sessions",
                    reason => Reason
                })
        end,

        %% 第二步：简化启动时恢复 - 只清理过期数据，会话恢复在连接时进行
        %% 这样避免了复杂的启动时会话创建，让EMQX自然地处理会话恢复
        ?SLOG(info, #{
            msg => "startup_session_restoration_simplified",
            note => "sessions_will_be_restored_on_client_connection"
        }),

        %% 查询并恢复活跃的持久会话到EMQX内存中
        ActiveFilter = #{
            <<"clean_start">> => false,
            <<"expiry_time">> => #{<<"$gt">> => Now}
        },

        ?SLOG(info, #{
            msg => "querying_active_persistent_sessions_for_restoration",
            filter => ActiveFilter
        }),

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                {find, SessionCollection, ActiveFilter}) of
            {ok, SessionDocs} when is_list(SessionDocs) ->
                ?SLOG(info, #{
                    msg => "found_active_persistent_sessions",
                    count => length(SessionDocs)
                }),

                % 批量恢复会话到EMQX内存
                RestoredCount = restore_sessions_to_emqx_memory(SessionDocs),

                ?SLOG(info, #{
                    msg => "session_restoration_completed",
                    total_sessions => length(SessionDocs),
                    restored_sessions => RestoredCount
                });
            {ok, []} ->
                ?SLOG(info, #{
                    msg => "no_active_persistent_sessions_found"
                });
            {error, Reason2} ->
                ?SLOG(error, #{
                    msg => "failed_to_query_active_sessions",
                    reason => Reason2
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_handling_persistent_sessions_after_restart",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 批量恢复会话到EMQX内存中
%% 这个函数将MongoDB中的持久化会话恢复到EMQX的内存会话存储中
%% 使得客户端重连时能够正确获得session_present=true
restore_sessions_to_emqx_memory(SessionDocs) ->
    ?SLOG(info, #{
        msg => "starting_batch_session_restoration",
        session_count => length(SessionDocs)
    }),

    RestoredCount = lists:foldl(fun(SessionDoc, Acc) ->
        try
            ClientId = maps:get(<<"client_id">>, SessionDoc, <<>>),

            % 创建ClientInfo
            ClientInfo = #{
                clientid => ClientId,
                username => maps:get(<<"username">>, SessionDoc, <<>>),
                peerhost => {127,0,0,1}, % 临时IP，表示从持久化恢复
                sockport => 1883,
                protocol => mqtt,
                mountpoint => undefined,
                zone => default
            },

            % 创建ConnInfo
            ConnInfo = #{
                clean_start => false,
                session_expiry_interval => maps:get(<<"session_expiry_interval">>, SessionDoc, 7200),
                receive_maximum => 32,
                max_packet_size => 1048576,
                topic_alias_maximum => 0,
                request_response_information => 0,
                request_problem_information => 1,
                user_properties => []
            },

            % 使用EMQX的会话管理API创建会话
            % 这样当客户端重连时，EMQX会找到这个会话并设置session_present=true
            case emqx_session:create(ClientInfo, ConnInfo) of
                Session when is_map(Session) ->
                    ?SLOG(debug, #{
                        msg => "session_restored_to_emqx_memory",
                        client_id => ClientId
                    }),

                    % 恢复订阅信息
                    restore_subscriptions_for_session(ClientId, SessionDoc),

                    Acc + 1;
                {error, Reason} ->
                    ?SLOG(warning, #{
                        msg => "failed_to_restore_session_to_memory",
                        client_id => ClientId,
                        reason => Reason
                    }),
                    Acc
            end
        catch
            Error:ErrorReason:Stack ->
                ClientIdForError = maps:get(<<"client_id">>, SessionDoc, <<"unknown">>),
                ?SLOG(error, #{
                    msg => "exception_restoring_session",
                    client_id => ClientIdForError,
                    error => Error,
                    reason => ErrorReason,
                    stacktrace => Stack
                }),
                Acc
        end
    end, 0, SessionDocs),

    ?SLOG(info, #{
        msg => "batch_session_restoration_completed",
        total_sessions => length(SessionDocs),
        restored_sessions => RestoredCount
    }),

    RestoredCount.

%% @doc 为恢复的会话恢复订阅信息
restore_subscriptions_for_session(ClientId, SessionDoc) ->
    try
        Subscriptions = maps:get(<<"subscriptions">>, SessionDoc, []),

        lists:foreach(fun(SubDoc) ->
            Topic = maps:get(<<"topic">>, SubDoc, <<>>),
            QoS = maps:get(<<"qos">>, SubDoc, 0),
            SubOpts = #{
                qos => QoS,
                rh => maps:get(<<"rh">>, SubDoc, 0),
                rap => maps:get(<<"rap">>, SubDoc, 0),
                nl => maps:get(<<"nl">>, SubDoc, 0)
            },

            % 恢复订阅到路由表
            case emqx_broker:subscribe(Topic, ClientId, SubOpts) of
                ok ->
                    ?SLOG(debug, #{
                        msg => "subscription_restored_for_session",
                        client_id => ClientId,
                        topic => Topic,
                        qos => QoS
                    });
                {error, Reason} ->
                    ?SLOG(warning, #{
                        msg => "failed_to_restore_subscription_for_session",
                        client_id => ClientId,
                        topic => Topic,
                        reason => Reason
                    })
            end
        end, Subscriptions),

        ?SLOG(debug, #{
            msg => "subscriptions_restored_for_session",
            client_id => ClientId,
            subscription_count => length(Subscriptions)
        })
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "exception_restoring_subscriptions_for_session",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 检查会话是否已过期
%% 简化：保留这个实用函数，用于会话过期检查
is_session_expired(Session) ->
    ExpiryTime = maps:get(<<"expiry_time">>, Session, 0),
    case ExpiryTime of
        0 -> false; % 永不过期
        _ ->
            Now = erlang:system_time(millisecond),
            ExpiryTime =< Now
    end.

%% @doc 清理过期会话数据
cleanup_expired_session(ClientId) ->
    try
        ?SLOG(info, #{msg => "cleaning_expired_session", client_id => ClientId}),

        % 删除过期的会话数据
        SessionCollection = get_session_collection(),
        SessionFilter = #{<<"client_id">> => ClientId},

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                {delete, SessionCollection, SessionFilter}) of
            {ok, _} ->
                ?SLOG(info, #{msg => "expired_session_cleaned", client_id => ClientId});
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_clean_expired_session",
                    client_id => ClientId,
                    reason => Reason
                })
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_cleaning_expired_session",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 删除会话数据（用于clean_start=true的情况）
delete_session_data(ClientId) ->
    try
        ?SLOG(info, #{msg => "deleting_session_data_for_clean_start", client_id => ClientId}),

        % 删除会话数据
        SessionCollection = get_session_collection(),
        SessionFilter = #{<<"client_id">> => ClientId},
        emqx_plugin_mongodb_api:delete_many(SessionCollection, SessionFilter),

        ?SLOG(info, #{msg => "session_data_deleted_successfully", client_id => ClientId})
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_deleting_session_data",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% 启动清理定时器
start_cleanup_timer() ->
    CleanupInterval = get_cleanup_interval(),
    ?SLOG(info, #{msg => "starting_session_cleanup_timer", interval => CleanupInterval}),
    % 使用独立进程处理定时清理，避免进程依赖问题
    TimerPid = spawn(fun() -> cleanup_timer_loop(CleanupInterval) end),
    % 将定时器进程ID存储到进程字典中，以便后续停止
    put(session_cleanup_timer_pid, TimerPid),
    ok.

%% 停止清理定时器
stop_cleanup_timer() ->
    case get(session_cleanup_timer_pid) of
        TimerPid when is_pid(TimerPid) ->
            ?SLOG(info, #{msg => "stopping_session_cleanup_timer", pid => TimerPid}),
            case is_process_alive(TimerPid) of
                true ->
                    exit(TimerPid, normal),
                    ?SLOG(info, #{msg => "session_cleanup_timer_stopped", pid => TimerPid});
                false ->
                    ?SLOG(debug, #{msg => "session_cleanup_timer_already_dead", pid => TimerPid})
            end,
            erase(session_cleanup_timer_pid);
        _ ->
            ?SLOG(warning, #{msg => "invalid_session_cleanup_timer_pid"}),
            erase(session_cleanup_timer_pid)
    end,
    ok.

%% 清理定时器循环
cleanup_timer_loop(Interval) ->
    receive
        stop ->
            ?SLOG(info, #{msg => "session_cleanup_timer_received_stop_signal"}),
            ok
    after Interval ->
        ?SLOG(info, #{msg => "cleaning_expired_sessions"}),
        % 执行清理
        spawn(fun() -> do_cleanup_expired_sessions() end),
        % 继续循环
        cleanup_timer_loop(Interval)
    end.

%% 清理过期会话（保留原函数用于手动调用）
cleanup_expired_sessions() ->
    ?SLOG(info, #{msg => "manual_cleaning_expired_sessions"}),
    spawn(fun() -> do_cleanup_expired_sessions() end),
    ok.

%% 执行过期会话清理
do_cleanup_expired_sessions() ->
    try
        % 获取集合名称
        SessionCollection = get_session_collection(),

        % 计算过期时间
        Now = erlang:system_time(millisecond),

        % 删除已过期的会话
        Filter = #{<<"expiry_time">> => #{<<"$lt">> => Now}},

        % 获取批次大小
        BatchSize = get_cleanup_batch_size(),

        % 执行删除 (注意：MongoDB delete操作的limit只能是0或1，不能用BatchSize)
        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                {delete_many, SessionCollection, Filter, #{}}) of
            {ok, #{<<"deletedCount">> := DeletedCount}} ->
                ?SLOG(info, #{msg => "expired_sessions_cleaned", count => DeletedCount});
            {error, Reason} ->
                ?SLOG(error, #{msg => "failed_to_clean_expired_sessions", reason => Reason})
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_cleaning_expired_sessions",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% 集成函数
integrate() ->
    ?SLOG(info, #{msg => "integrating_session_module"}),
    % 将此模块注册到协调器
    case erlang:function_exported(emqx_plugin_mongodb_coordinator, register_module, 2) of
        true ->
            emqx_plugin_mongodb_coordinator:register_module(?MODULE, #{
                priority => high,
                description => <<"Session persistence module">>,
                features => [session_persistence, auto_restore]
            });
        false ->
            ok
    end.

%% 获取集合名称
get_session_collection() ->
    % 从配置中获取集合名称
    Config = application:get_env(emqx_plugin_mongodb, config, #{}),
    SessionConfig = maps:get(session_persistence, Config, #{}),
    maps:get(collection, SessionConfig, ?DEFAULT_SESSION_COLLECTION).

%% 获取遗嘱消息集合名称
get_will_message_collection() ->
    ?DEFAULT_WILL_MESSAGE_COLLECTION.

%% 获取会话过期时间
get_session_expiry() ->
    Config = application:get_env(emqx_plugin_mongodb, config, #{}),
    SessionConfig = maps:get(session_persistence, Config, #{}),
    maps:get(session_expiry, SessionConfig, ?DEFAULT_SESSION_EXPIRY).

%% 获取清理间隔
get_cleanup_interval() ->
    Config = application:get_env(emqx_plugin_mongodb, config, #{}),
    SessionConfig = maps:get(session_persistence, Config, #{}),
    maps:get(cleanup_interval, SessionConfig, ?DEFAULT_CLEANUP_INTERVAL).

%% 获取清理批次大小
get_cleanup_batch_size() ->
    Config = application:get_env(emqx_plugin_mongodb, config, #{}),
    SessionConfig = maps:get(session_persistence, Config, #{}),
    maps:get(cleanup_batch_size, SessionConfig, ?DEFAULT_CLEANUP_BATCH_SIZE).

%% 等待MongoDB资源就绪
wait_for_mongodb_ready() ->
    wait_for_resource(30).

wait_for_resource(0) ->
    ?SLOG(error, #{msg => "mongodb_resource_not_ready_after_retries"}),
    {error, timeout};
wait_for_resource(Retries) ->
    case emqx_resource:health_check(?PLUGIN_MONGODB_RESOURCE_ID) of
        ok ->
            ?SLOG(info, #{msg => "mongodb_resource_ready"}),
            ok;
        {ok, _} ->
            ?SLOG(info, #{msg => "mongodb_resource_ready"}),
            ok;
        _ ->
            ?SLOG(warning, #{msg => "mongodb_resource_not_ready", retries_left => Retries}),
            timer:sleep(1000),
            wait_for_resource(Retries - 1)
    end.

%% @doc 更新会话状态（带原因和历史记录）
update_session_status_with_history(ClientInfo, Status, Reason) ->
    try
        ClientId = maps:get(clientid, ClientInfo),
        Now = erlang:system_time(millisecond),

        % 构造历史记录
        HistoryEntry = #{
            <<"status">> => Status,
            <<"timestamp">> => Now,
            <<"reason">> => format_reason(Reason),
            <<"node">> => atom_to_binary(node(), utf8)
        },

        % 更新文档
        UpdateDoc = #{
            <<"$set">> => #{
                <<"status">> => Status,
                <<"updated_at">> => Now
            },
            <<"$push">> => #{
                <<"status_history">> => #{
                    <<"$each">> => [HistoryEntry],
                    <<"$slice">> => -10  % 只保留最近10条历史
                }
            }
        },

        % 获取集合名称
        SessionCollection = get_session_collection(),

        % 使用事务支持模块执行更新
        Fun = fun() ->
            emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                {update, SessionCollection,
                 #{<<"client_id">> => ClientId},
                 UpdateDoc})
        end,

        % 使用重试机制执行
        case emqx_plugin_mongodb_transaction:with_retry(Fun, [], 3) of
            {ok, {ok, _}} ->
                ?SLOG(debug, #{msg => "session_status_updated_with_history",
                             client_id => ClientId,
                             status => Status});
            {ok, async_success} ->
                ?SLOG(debug, #{msg => "session_status_updated_with_history_async",
                             client_id => ClientId,
                             status => Status});
            {ok, {async_return, {ok, _}}} ->
                ?SLOG(debug, #{msg => "session_status_updated_with_history_async",
                             client_id => ClientId,
                             status => Status});
            {ok, {error, Reason}} ->
                ?SLOG(error, #{msg => "failed_to_update_session_status_with_history",
                             client_id => ClientId,
                             status => Status,
                             reason => Reason});
            {ok, {async_return, {error, Reason}}} ->
                ?SLOG(error, #{msg => "failed_to_update_session_status_with_history_async",
                             client_id => ClientId,
                             status => Status,
                             reason => Reason});
            {error, Reason} ->
                ?SLOG(error, #{msg => "failed_to_update_session_status_with_history_retry",
                             client_id => ClientId,
                             status => Status,
                             reason => Reason})
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_updating_session_status_with_history",
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 更新会话统计信息
update_session_stats(ClientId, Action) ->
    try
        Now = erlang:system_time(millisecond),

        % 获取集合名称
        SessionCollection = get_session_collection(),

        % 根据动作执行不同的更新
        case Action of
            connect ->
                % 连接时简单更新计数和状态
                UpdateDoc = #{
                    <<"$inc">> => #{
                        <<"connect_count">> => 1
                    },
                    <<"$set">> => #{
                        <<"last_connected_at">> => Now,
                        <<"is_online">> => true
                    }
                },

                % 执行更新
                case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                      {update, SessionCollection,
                                       #{<<"client_id">> => ClientId},
                                       UpdateDoc}) of
                    {ok, _} ->
                        ?SLOG(debug, #{msg => "session_stats_updated_connect",
                                     client_id => ClientId});
                    {async_return, ok} ->
                        ?SLOG(debug, #{msg => "session_stats_updated_connect_async",
                                     client_id => ClientId});
                    {async_return, {ok, _}} ->
                        ?SLOG(debug, #{msg => "session_stats_updated_connect_async",
                                     client_id => ClientId});
                    {error, Reason} ->
                        ?SLOG(error, #{msg => "failed_to_update_session_stats_connect",
                                     client_id => ClientId,
                                     reason => Reason});
                    {async_return, {error, Reason}} ->
                        ?SLOG(error, #{msg => "failed_to_update_session_stats_connect_async",
                                     client_id => ClientId,
                                     reason => Reason})
                end;

            disconnect ->
                % 先查询当前会话状态
                case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                      {find_one, SessionCollection,
                                       #{<<"client_id">> => ClientId},
                                       #{<<"_id">> => 0,
                                         <<"is_online">> => 1,
                                         <<"last_connected_at">> => 1}}) of
                    {ok, #{<<"is_online">> := true, <<"last_connected_at">> := LastConnected}} ->
                        % 计算在线时长
                        OnlineTime = Now - LastConnected,

                        % 更新断开连接状态和在线时间
                        DisconnectDoc = #{
                            <<"$inc">> => #{
                                <<"disconnect_count">> => 1,
                                <<"total_online_time">> => OnlineTime
                            },
                            <<"$set">> => #{
                                <<"last_disconnected_at">> => Now,
                                <<"is_online">> => false
                            }
                        },

                        % 执行更新
                        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                              {update, SessionCollection,
                                               #{<<"client_id">> => ClientId},
                                               DisconnectDoc}) of
                            {ok, _} ->
                                ?SLOG(debug, #{msg => "session_stats_updated_disconnect",
                                             client_id => ClientId,
                                             online_time => OnlineTime});
                            {async_return, ok} ->
                                ?SLOG(debug, #{msg => "session_stats_updated_disconnect_async",
                                             client_id => ClientId,
                                             online_time => OnlineTime});
                            {async_return, {ok, _}} ->
                                ?SLOG(debug, #{msg => "session_stats_updated_disconnect_async",
                                             client_id => ClientId,
                                             online_time => OnlineTime});
                            {error, Reason2} ->
                                ?SLOG(error, #{msg => "failed_to_update_session_stats_disconnect",
                                             client_id => ClientId,
                                             reason => Reason2});
                            {async_return, {error, Reason2}} ->
                                ?SLOG(error, #{msg => "failed_to_update_session_stats_disconnect_async",
                                             client_id => ClientId,
                                             reason => Reason2})
                        end;

                    {ok, _} ->
                        % 客户端不在线，只更新断开连接计数
                        SimpleDoc = #{
                            <<"$inc">> => #{
                                <<"disconnect_count">> => 1
                            },
                            <<"$set">> => #{
                                <<"last_disconnected_at">> => Now,
                                <<"is_online">> => false
                            }
                        },

                        % 执行更新
                        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                              {update, SessionCollection,
                                               #{<<"client_id">> => ClientId},
                                               SimpleDoc}) of
                            {ok, _} ->
                                ?SLOG(debug, #{msg => "session_stats_updated_simple_disconnect",
                                             client_id => ClientId});
                            {async_return, ok} ->
                                ?SLOG(debug, #{msg => "session_stats_updated_simple_disconnect_async",
                                             client_id => ClientId});
                            {async_return, {ok, _}} ->
                                ?SLOG(debug, #{msg => "session_stats_updated_simple_disconnect_async",
                                             client_id => ClientId});
                            {error, Reason3} ->
                                ?SLOG(error, #{msg => "failed_to_update_session_stats_simple_disconnect",
                                             client_id => ClientId,
                                             reason => Reason3});
                            {async_return, {error, Reason3}} ->
                                ?SLOG(error, #{msg => "failed_to_update_session_stats_simple_disconnect_async",
                                             client_id => ClientId,
                                             reason => Reason3})
                        end;

                    {async_return, ok} ->
                        % 异步查询成功但没有返回数据，按简单断开处理
                        ?SLOG(debug, #{msg => "async_query_no_data_simple_disconnect",
                                     client_id => ClientId}),
                        SimpleDoc = #{
                            <<"$inc">> => #{
                                <<"disconnect_count">> => 1
                            },
                            <<"$set">> => #{
                                <<"last_disconnected_at">> => Now,
                                <<"is_online">> => false
                            }
                        },
                        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                                              {update, SessionCollection,
                                               #{<<"client_id">> => ClientId},
                                               SimpleDoc}) of
                            {ok, _} ->
                                ?SLOG(debug, #{msg => "session_stats_updated_async_simple_disconnect",
                                             client_id => ClientId});
                            {error, Reason5} ->
                                ?SLOG(error, #{msg => "failed_to_update_session_stats_async_simple_disconnect",
                                             client_id => ClientId,
                                             reason => Reason5})
                        end;

                    {error, Reason} ->
                        ?SLOG(error, #{msg => "failed_to_query_session_for_disconnect_stats",
                                     client_id => ClientId,
                                     reason => Reason})
                end;

            _ ->
                ?SLOG(warning, #{msg => "unknown_session_stats_action",
                               client_id => ClientId,
                               action => Action})
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_updating_session_stats",
                client_id => ClientId,
                action => Action,
                error => E,
                reason => R,
                stacktrace => S
            })
    end.

%% @doc 生成集合缩写
generate_collection_abbreviation(CollectionName) when is_binary(CollectionName) ->
    % 简单的缩写生成：取每个单词的首字母
    Parts = binary:split(CollectionName, <<"_">>, [global]),
    Abbr = lists:foldl(fun(Part, Acc) ->
        case Part of
            <<First:8, _/binary>> when First >= $a, First =< $z ->
                <<Acc/binary, First>>;
            <<First:8, _/binary>> when First >= $A, First =< $Z ->
                <<Acc/binary, First>>;
            _ ->
                Acc
        end
    end, <<>>, Parts),
    case Abbr of
        <<>> -> <<"col">>;
        _ -> Abbr
    end;
generate_collection_abbreviation(_) ->
    <<"col">>.

%% @doc 处理会话索引错误
handle_session_index_error(Collection, IndexName, Reason) ->
    ?SLOG(error, #{
        msg => "failed_to_create_session_index",
        collection => Collection,
        index_name => IndexName,
        reason => Reason
    }).

%% @doc 检查并恢复会话数据
check_and_restore_session_data(ClientId) ->
    ?SLOG(info, #{msg => "checking_session_data", client_id => ClientId}),
    try
        % 查询MongoDB中是否有该客户端的持久会话数据
        SessionCollection = get_session_collection(),
        Filter = #{<<"client_id">> => ClientId},

        case emqx_resource:query(?PLUGIN_MONGODB_RESOURCE_ID,
                               {find_one, SessionCollection, Filter}) of
            {ok, SessionDoc} when SessionDoc =/= null, SessionDoc =/= #{} ->
                % 找到持久会话数据
                ?SLOG(info, #{
                    msg => "found_persistent_session_data",
                    client_id => ClientId,
                    session_doc => SessionDoc
                }),

                % 检查会话是否过期
                case is_session_expired(SessionDoc) of
                    true ->
                        ?SLOG(info, #{
                            msg => "session_expired_cleaning_up",
                            client_id => ClientId
                        }),
                        cleanup_expired_session(ClientId),
                        no_data;
                    false ->
                        % 会话未过期，返回恢复数据
                        ?SLOG(info, #{
                            msg => "session_data_available_for_restoration",
                            client_id => ClientId
                        }),
                        {restored, SessionDoc}
                end;
            {ok, null} ->
                ?SLOG(info, #{msg => "no_session_data_found", client_id => ClientId}),
                no_data;
            {ok, #{}} ->
                ?SLOG(info, #{msg => "empty_session_data_found", client_id => ClientId}),
                no_data;
            {error, Reason} ->
                ?SLOG(warning, #{
                    msg => "error_checking_session_data",
                    client_id => ClientId,
                    reason => Reason
                }),
                no_data
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "error_in_check_and_restore_session_data",
                client_id => ClientId,
                error => E,
                reason => R,
                stacktrace => S
            }),
            no_data
    end.

%% @doc 在恢复后更新会话数据
update_session_after_restoration(ClientInfo, _SessInfo) ->
    ClientId = maps:get(clientid, ClientInfo, <<>>),
    ?SLOG(info, #{msg => "session_restoration_completed", client_id => ClientId}).

%% @doc 为特定客户端恢复订阅数据
restore_subscriptions_for_client(ClientId) ->
    ?SLOG(info, #{msg => "restoring_subscriptions", client_id => ClientId}),
    % 简化：让EMQX在客户端重连时自然恢复订阅
    ok.

%% @doc 为特定客户端恢复未确认消息
restore_inflight_messages_for_client(ClientId) ->
    ?SLOG(info, #{msg => "restoring_inflight_messages", client_id => ClientId}),
    % 简化：让EMQX在客户端重连时自然恢复消息
    ok.

%% 会话接管处理函数已删除 - 让EMQX自然处理会话恢复
%% 这样可以避免干扰EMQX的session_present逻辑

%% @doc 使用EMQX API恢复所有MongoDB中的会话数据
%% 这是处理EMQX异常重启的关键函数，使用更简单的方法
restore_all_sessions_using_emqx_api(SessionCollection, Now) ->
    ?SLOG(info, #{
        msg => "starting_batch_restore_sessions_using_emqx_api",
        collection => SessionCollection,
        current_time => Now
    }),

    try
        % 查询所有未过期的会话
        ActiveSessionsFilter = #{
            <<"$or">> => [
                % 永不过期的会话
                #{<<"expiry_time">> => 0},
                % 未过期的会话
                #{<<"expiry_time">> => #{<<"$gt">> => Now}}
            ]
        },

        ?SLOG(info, #{
            msg => "querying_active_sessions_from_mongodb",
            filter => ActiveSessionsFilter
        }),

        % 获取MongoDB连接并查询会话数据
        case emqx_plugin_mongodb_api:get_mongodb_connection() of
            {ok, Connection} ->
                case emqx_plugin_mongodb_api:find(Connection, SessionCollection, ActiveSessionsFilter, #{}, #{}) of
                    {ok, SessionDocs} when is_list(SessionDocs) ->
                        ?SLOG(info, #{
                            msg => "found_active_sessions_in_mongodb",
                            session_count => length(SessionDocs)
                        }),

                        % 批量恢复会话使用EMQX API
                        RestoreResults = lists:map(fun(SessionDoc) ->
                            restore_single_session_using_emqx_api(SessionDoc)
                        end, SessionDocs),

                        % 统计恢复结果
                        SuccessCount = length([ok || ok <- RestoreResults]),
                        FailureCount = length(SessionDocs) - SuccessCount,

                        ?SLOG(info, #{
                            msg => "batch_session_restore_completed",
                            total_sessions => length(SessionDocs),
                            success_count => SuccessCount,
                            failure_count => FailureCount
                        }),

                        ok;
                    {ok, []} ->
                        ?SLOG(info, #{
                            msg => "no_active_sessions_found_in_mongodb"
                        }),
                        ok;
                    {error, Reason} ->
                        ?SLOG(error, #{
                            msg => "failed_to_query_active_sessions_from_mongodb",
                            reason => Reason
                        }),
                        {error, Reason}
                end;
            {error, Reason} ->
                ?SLOG(error, #{
                    msg => "failed_to_get_mongodb_connection_for_session_restore",
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "exception_during_batch_session_restore",
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {exception, E, R}}
    end.

%% @doc 使用EMQX API恢复单个会话
restore_single_session_using_emqx_api(SessionDoc) ->
    try
        ClientId = maps:get(<<"client_id">>, SessionDoc),

        % 构造ClientInfo
        ClientInfo = #{
            clientid => ClientId,
            username => maps:get(<<"username">>, SessionDoc, undefined),
            zone => default
        },

        % 构造ConnInfo
        SessionExpiryInterval = maps:get(<<"session_expiry_interval">>, SessionDoc, 7200),
        ConnInfo = #{
            clean_start => false,  % 持久会话
            expiry_interval => SessionExpiryInterval * 1000,  % 转换为毫秒
            receive_maximum => 65535,
            proto_ver => 4
        },

        ?SLOG(debug, #{
            msg => "restoring_session_using_emqx_api",
            client_id => ClientId,
            session_expiry_interval => SessionExpiryInterval
        }),

        % 使用EMQX API创建会话
        case emqx_session:create(ClientInfo, ConnInfo) of
            Session when is_map(Session) ->
                ?SLOG(debug, #{
                    msg => "successfully_restored_session_using_emqx_api",
                    client_id => ClientId
                }),
                ok;
            {error, Reason} ->
                ?SLOG(warning, #{
                    msg => "failed_to_restore_session_using_emqx_api",
                    client_id => ClientId,
                    reason => Reason
                }),
                {error, Reason}
        end
    catch
        E:R:S ->
            ?SLOG(error, #{
                msg => "exception_restoring_single_session",
                session_doc => SessionDoc,
                error => E,
                reason => R,
                stacktrace => S
            }),
            {error, {exception, E, R}}
    end.

%% @doc 在EMQX会话查找之前注入MongoDB会话数据
%% 这是解决session_present问题的关键方法
restore_session_before_emqx_lookup(ClientInfo) ->
    ClientId = maps:get(clientid, ClientInfo, <<>>),

    ?SLOG(info, #{
        msg => "attempting_to_inject_session_before_emqx_lookup",
        client_id => ClientId
    }),

    % 从MongoDB查找持久会话数据
    case find_persistent_session_in_mongodb(ClientId) of
        {ok, SessionData} ->
            ?SLOG(info, #{
                msg => "found_persistent_session_in_mongodb",
                client_id => ClientId,
                session_data => SessionData
            }),

            % 将会话数据注入到EMQX的持久会话存储中
            inject_session_to_emqx_storage(ClientId, SessionData, ClientInfo);
        {error, not_found} ->
            ?SLOG(debug, #{
                msg => "no_persistent_session_found_in_mongodb",
                client_id => ClientId
            }),
            ok;
        {error, Reason} ->
            ?SLOG(error, #{
                msg => "failed_to_query_mongodb_for_session",
                client_id => ClientId,
                reason => Reason
            }),
            ok
    end.


