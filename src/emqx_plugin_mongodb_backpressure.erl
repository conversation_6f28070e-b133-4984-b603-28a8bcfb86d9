%%%-------------------------------------------------------------------
%%% @doc
%%% MongoDB插件的背压机制模块
%%%
%%% 这个模块实现了自适应背压控制机制，用于防止系统过载
%%% 背压（Backpressure）是一种流量控制技术，当下游处理能力不足时，
%%% 通过限制上游的数据流入速度来保护系统稳定性
%%%
%%% 功能特性：
%%% 1. 实时监控系统负载（队列长度、处理时间、系统资源）
%%% 2. 根据负载情况自动调整背压级别（正常、轻微、中等、高、严重）
%%% 3. 执行相应的背压动作（限流、降低批处理大小、拒绝请求等）
%%% 4. 提供指标统计和监控接口
%%%
%%% Java等价概念：
%%% - 类似于Hystrix的熔断器和限流器
%%% - 类似于RateLimiter（如Guava RateLimiter）
%%% - 类似于Reactive Streams的背压机制
%%% - 类似于Netty的流量控制
%%%
%%% 设计模式：
%%% - 使用gen_server行为实现状态管理和并发控制
%%% - 采用策略模式定义不同级别的背压动作
%%% - 使用观察者模式监控系统指标变化
%%%
%%% @end
%%%-------------------------------------------------------------------
-module(emqx_plugin_mongodb_backpressure).

%% 实现gen_server行为，提供状态管理和并发控制
%% 类似于Java中实现Runnable接口或继承Thread类
-behaviour(gen_server).

%% API - 公共接口函数
%% 这些函数提供背压机制的主要功能，类似于Java中的public方法
-export([
    start_link/1,           % 启动背压服务器，类似于Java的服务启动方法
    check_pressure/2,       % 检查当前背压状态，类似于限流器的tryAcquire()
    update_metrics/3,       % 更新系统指标，类似于监控系统的metric更新
    get_pressure_status/0,  % 获取背压状态，类似于健康检查接口
    set_pressure_threshold/2, % 设置背压阈值，类似于配置更新接口
    reset_pressure/0,       % 重置背压状态，类似于重置计数器
    integrate/0             % 集成到系统中，类似于模块注册方法
]).

%% gen_server callbacks
-export([
    init/1,
    handle_call/3,
    handle_cast/2,
    handle_info/2,
    terminate/2,
    code_change/3
]).

%% 内部导出函数
-export([
    pressure_monitor/0,
    calculate_pressure/1
]).

-include("emqx_plugin_mongodb.hrl").

%% @doc 背压状态记录
%% 这个记录定义了背压机制的完整状态信息
%% 类似于Java中的状态类或配置类，包含所有必要的字段和配置
%%
%% 在Java中相当于：
%% public class BackpressureState {
%%     private BackpressureLevel level;
%%     private int queueLength;
%%     private long processingTime;
%%     private double rejectionRate;
%%     // ... 其他字段
%% }
-record(pressure_state, {
    %% 当前背压级别，类似于Java枚举
    %% 可能的值：normal, mild, moderate, high, critical
    level = ?BACKPRESSURE_LEVEL_NORMAL,

    %% 当前队列长度，类似于Java中的BlockingQueue.size()
    %% 用于监控待处理消息的积压情况
    queue_length = 0,

    %% 平均处理时间（毫秒），类似于Java中的响应时间监控
    %% 用于评估系统处理能力
    processing_time = 0,

    %% 拒绝率（0.0-1.0），类似于Java中的错误率统计
    %% 表示被拒绝请求占总请求的比例
    rejection_rate = 0.0,

    %% 上次更新时间戳，类似于Java中的System.currentTimeMillis()
    %% 用于计算指标更新间隔
    last_update_time = 0,

    %% 综合压力得分（0.0-1.0），类似于Java中的健康度评分
    %% 综合考虑队列长度、处理时间、系统负载等因素
    pressure_score = 0.0,

    %% 各级别的阈值配置，类似于Java中的配置映射
    %% 定义了触发不同背压级别的条件
    thresholds = #{
        %% 队列长度阈值配置
        %% 类似于Java中的Map<BackpressureLevel, Integer>
        queue_length => #{
            ?BACKPRESSURE_LEVEL_MILD => ?BACKPRESSURE_DEFAULT_QUEUE_THRESHOLD_MILD,
            ?BACKPRESSURE_LEVEL_MODERATE => ?BACKPRESSURE_DEFAULT_QUEUE_THRESHOLD_MODERATE,
            ?BACKPRESSURE_LEVEL_HIGH => ?BACKPRESSURE_DEFAULT_QUEUE_THRESHOLD_HIGH,
            ?BACKPRESSURE_LEVEL_CRITICAL => ?BACKPRESSURE_DEFAULT_QUEUE_THRESHOLD_CRITICAL
        },
        %% 处理时间阈值配置（毫秒）
        %% 类似于Java中的响应时间SLA配置
        processing_time => #{
            ?BACKPRESSURE_LEVEL_MILD => ?BACKPRESSURE_DEFAULT_TIME_THRESHOLD_MILD,
            ?BACKPRESSURE_LEVEL_MODERATE => ?BACKPRESSURE_DEFAULT_TIME_THRESHOLD_MODERATE,
            ?BACKPRESSURE_LEVEL_HIGH => ?BACKPRESSURE_DEFAULT_TIME_THRESHOLD_HIGH,
            ?BACKPRESSURE_LEVEL_CRITICAL => ?BACKPRESSURE_DEFAULT_TIME_THRESHOLD_CRITICAL
        },
        %% 系统负载阈值配置（0.0-1.0）
        %% 类似于Java中的CPU使用率或内存使用率阈值
        system_load => #{
            ?BACKPRESSURE_LEVEL_MILD => 0.6,      % 60%负载触发轻微背压
            ?BACKPRESSURE_LEVEL_MODERATE => 0.7,  % 70%负载触发中等背压
            ?BACKPRESSURE_LEVEL_HIGH => 0.8,      % 80%负载触发高背压
            ?BACKPRESSURE_LEVEL_CRITICAL => 0.9   % 90%负载触发严重背压
        }
    },
    %% 背压动作配置，类似于Java中的策略模式
    %% 定义了不同背压级别下应该执行的具体动作
    %% 在Java中相当于：
    %% Map<BackpressureLevel, List<BackpressureAction>>
    actions = #{
        %% 轻微背压级别的动作
        %% 类似于Java中的轻量级限流策略
        ?BACKPRESSURE_LEVEL_MILD => [
            {throttle_rate, 0.9},        % 限制接收速率为90%，类似于RateLimiter.setRate()
            {batch_size, 0.8}            % 降低批处理大小至80%，减少内存压力
        ],
        %% 中等背压级别的动作
        %% 类似于Java中的中等强度限流策略
        ?BACKPRESSURE_LEVEL_MODERATE => [
            {throttle_rate, 0.7},        % 限制接收速率为70%
            {batch_size, 0.6},           % 降低批处理大小至60%
            {reject_low_priority, true}  % 开始拒绝低优先级消息，类似于优先级队列
        ],
        %% 高背压级别的动作
        %% 类似于Java中的严格限流策略
        ?BACKPRESSURE_LEVEL_HIGH => [
            {throttle_rate, 0.4},        % 限制接收速率为40%
            {batch_size, 0.3},           % 降低批处理大小至30%
            {reject_low_priority, true}, % 拒绝低优先级消息
            {reject_medium_priority, true} % 拒绝中优先级消息
        ],
        %% 严重背压级别的动作
        %% 类似于Java中的紧急保护模式
        ?BACKPRESSURE_LEVEL_CRITICAL => [
            {throttle_rate, 0.1},        % 限制接收速率为10%，几乎停止接收
            {batch_size, 0.1},           % 降低批处理大小至10%
            {reject_low_priority, true}, % 拒绝低优先级消息
            {reject_medium_priority, true}, % 拒绝中优先级消息
            {persist_only, true}         % 只持久化不处理，保护系统稳定性
        ]
    },

    %% 指标统计信息，类似于Java中的Metrics或监控数据
    %% 用于监控背压机制的运行状态和效果
    %% 在Java中相当于：
    %% public class BackpressureMetrics {
    %%     private long messagesReceived;
    %%     private long messagesProcessed;
    %%     // ... 其他指标
    %% }
    metrics = #{
        messages_received => 0,      % 接收的消息总数，类似于计数器
        messages_processed => 0,     % 处理的消息总数
        messages_rejected => 0,      % 拒绝的消息总数，用于计算拒绝率
        avg_processing_time => 0,    % 平均处理时间，类似于响应时间监控
        max_queue_length => 0,       % 最大队列长度，用于容量规划
        pressure_changes => []       % 背压级别变化历史，用于趋势分析
    }
}).

%% 消息优先级
-define(PRIORITY_HIGH, high).
-define(PRIORITY_MEDIUM, medium).
-define(PRIORITY_LOW, low).

%% 背压级别
-define(PRESSURE_NORMAL, normal).
-define(PRESSURE_MILD, mild).
-define(PRESSURE_MODERATE, moderate).
-define(PRESSURE_HIGH, high).
-define(PRESSURE_CRITICAL, critical).

%%%===================================================================
%%% API
%%%===================================================================

%% @doc 启动背压控制服务
%% 这个函数启动一个gen_server进程来管理背压状态和执行背压动作
%%
%% 功能说明：
%% 1. 创建一个本地注册的gen_server进程
%% 2. 使用提供的配置初始化背压状态
%% 3. 开始监控系统指标和执行背压控制
%%
%% 参数说明：
%% - Options: 背压配置映射，包含阈值、动作等配置信息
%%   格式示例：#{
%%     queue_threshold => 1000,
%%     time_threshold => 5000,
%%     enable_metrics => true
%%   }
%%
%% 返回值：
%% - {ok, Pid}: 启动成功，返回进程ID
%% - {error, Reason}: 启动失败，返回错误原因
%%
%% Java等价概念：
%% 类似于Spring的@Service注解的Bean启动
%% 或者ExecutorService的创建和启动
%%
%% 示例：
%% Options = #{queue_threshold => 1000, time_threshold => 5000},
%% {ok, Pid} = emqx_plugin_mongodb_backpressure:start_link(Options)
start_link(Options) ->
    %% 启动gen_server进程
    %% {local, ?MODULE} 表示本地注册，进程名为模块名
    %% ?MODULE 是当前模块名的宏
    %% [Options] 是传递给init/1回调的参数列表
    %% [] 是gen_server的选项列表（这里使用默认选项）
    %%
    %% 在Java中相当于：
    %% @Service
    %% public class BackpressureService {
    %%     public BackpressureService(BackpressureConfig config) { ... }
    %% }
    gen_server:start_link({local, ?MODULE}, ?MODULE, [Options], []).

%% @doc 检查背压状态并决定是否允许消息处理
%% 这是背压机制的核心函数，决定是否允许处理特定的消息
%%
%% 功能说明：
%% 1. 根据当前背压级别和消息优先级决定是否允许处理
%% 2. 实现优先级队列的概念，高优先级消息优先处理
%% 3. 在高背压情况下拒绝低优先级消息
%% 4. 返回处理决策和相关的背压动作
%%
%% 参数说明：
%% - Topic: 消息主题，用于主题级别的背压控制
%% - Priority: 消息优先级（high/medium/low）
%%
%% 返回值：
%% - {allow, Actions}: 允许处理，返回应用的背压动作
%% - {reject, Reason}: 拒绝处理，返回拒绝原因
%%
%% Java等价概念：
%% 类似于RateLimiter.tryAcquire()或Semaphore.tryAcquire()
%% 或者优先级队列的offer()方法
%%
%% 示例：
%% case check_pressure(<<"sensor/data">>, high) of
%%     {allow, Actions} -> process_message_with_actions(Message, Actions);
%%     {reject, Reason} -> log_rejection(Message, Reason)
%% end
check_pressure(Topic, Priority) ->
    %% 发送同步调用到gen_server进程
    %% gen_server:call/2会等待服务器处理并返回结果
    %% 类似于Java中的同步方法调用或Future.get()
    %%
    %% 在Java中相当于：
    %% public BackpressureDecision checkPressure(String topic, Priority priority) {
    %%     return backpressureService.checkPressure(topic, priority);
    %% }
    gen_server:call(?MODULE, {check_pressure, Topic, Priority}).

%% @doc 更新指标
%% 这个函数用于更新背压机制的监控指标，是背压决策的数据基础
%%
%% 功能说明：
%% 1. 更新队列长度、处理时间等关键指标
%% 2. 根据新指标重新计算背压级别
%% 3. 触发背压级别变化时的相应动作
%% 4. 维护指标历史用于趋势分析
%%
%% 参数说明：
%% - QueueLength: 当前队列长度，反映系统积压情况
%% - ProcessingTime: 消息处理时间（毫秒），反映系统处理能力
%% - Success: 处理是否成功，用于计算成功率和错误率
%%
%% 返回值：
%% - ok: 异步调用，立即返回
%%
%% Java等价概念：
%% 类似于Micrometer的Metrics.counter().increment()
%% 或者Prometheus的指标更新
%%
%% 示例：
%% update_metrics(150, 250, true)  % 队列长度150，处理时间250ms，成功
%% update_metrics(200, 500, false) % 队列长度200，处理时间500ms，失败
update_metrics(QueueLength, ProcessingTime, Success) ->
    %% 发送异步消息到gen_server进程
    %% gen_server:cast/2是异步调用，不等待返回结果
    %% 类似于Java中的异步方法调用或CompletableFuture.runAsync()
    %%
    %% 在Java中相当于：
    %% @Async
    %% public void updateMetrics(int queueLength, long processingTime, boolean success) {
    %%     metricsService.updateMetrics(queueLength, processingTime, success);
    %% }
    gen_server:cast(?MODULE, {update_metrics, QueueLength, ProcessingTime, Success}).

%% @doc 获取背压状态
get_pressure_status() ->
    gen_server:call(?MODULE, get_pressure_status).

%% @doc 设置背压阈值
set_pressure_threshold(Type, Thresholds) ->
    gen_server:call(?MODULE, {set_threshold, Type, Thresholds}).

%% @doc 重置背压状态
reset_pressure() ->
    gen_server:call(?MODULE, reset_pressure).

%% @doc 集成到协调器
integrate() ->
    ?SLOG(info, #{msg => "integrating_backpressure_module"}),
    % 将此模块注册到协调器
    case erlang:function_exported(emqx_plugin_mongodb_coordinator, register_module, 2) of
        true ->
            emqx_plugin_mongodb_coordinator:register_module(?MODULE, #{
                priority => high,
                description => <<"Backpressure control module">>,
                features => [pressure_detection, throttling, adaptive_control]
            });
        false ->
            ok
    end.

%%%===================================================================
%%% gen_server callbacks
%%%===================================================================

%% @doc 初始化回调
%% 这是gen_server的初始化回调函数，在进程启动时被调用
%%
%% 功能说明：
%% 1. 创建ETS表用于高效的状态存储和并发访问
%% 2. 初始化背压状态和配置
%% 3. 启动监控进程用于实时监控系统指标
%% 4. 设置定期状态检查机制
%%
%% 参数说明：
%% - [Options]: 启动选项列表，包含背压配置
%%
%% 返回值：
%% - {ok, State}: 初始化成功，返回初始状态
%% - {stop, Reason}: 初始化失败，停止进程
%%
%% Java等价概念：
%% 类似于Spring的@PostConstruct方法或构造函数
%% 用于初始化服务的状态和依赖
init([Options]) ->
    %% 创建ETS表用于存储背压状态
    %% ETS（Erlang Term Storage）是Erlang的内存数据库
    %% 类似于Java中的ConcurrentHashMap或Redis的内存存储
    %%
    %% 选项说明：
    %% - named_table: 使用宏名作为表名，便于访问
    %% - public: 允许其他进程读写，类似于public字段
    %% - {read_concurrency, true}: 优化并发读取性能
    %%
    %% 在Java中相当于：
    %% private final ConcurrentHashMap<String, Object> stateStorage = new ConcurrentHashMap<>();
    ets:new(?BACKPRESSURE_TAB, [named_table, public, {read_concurrency, true}]),

    %% 初始化背压状态
    %% 根据配置选项创建初始的背压状态记录
    %% 类似于Java中的配置对象初始化
    State = init_pressure_state(Options),

    %% 存储初始状态到ETS表
    %% 使用ETS存储状态可以提供高并发访问性能
    %% 类似于将状态存储到共享缓存中
    ets:insert(?BACKPRESSURE_TAB, {pressure_state, State}),

    %% 启动监控进程
    %% spawn_link创建一个链接的子进程用于监控系统指标
    %% 类似于Java中启动一个监控线程
    %% 在Java中相当于：
    %% ExecutorService.submit(() -> monitorSystemMetrics())
    spawn_link(?MODULE, pressure_monitor, []),

    %% 启动定期状态检查
    %% erlang:send_after/3设置定时器，定期发送消息给自己
    %% 类似于Java中的ScheduledExecutorService.scheduleAtFixedRate()
    %% 在Java中相当于：
    %% scheduler.scheduleAtFixedRate(this::checkPressureState, 0, interval, TimeUnit.MILLISECONDS)
    erlang:send_after(?BACKPRESSURE_MONITOR_INTERVAL, self(), check_pressure_state),

    %% 返回初始化成功和初始状态
    {ok, State}.

%% @doc 处理同步调用
%% 这是gen_server的同步调用处理回调，处理客户端的同步请求
%%
%% 功能说明：
%% 1. 处理背压检查请求，决定是否允许消息处理
%% 2. 根据当前背压级别和消息优先级做出决策
%% 3. 应用相应的背压动作和限制策略
%% 4. 更新相关的统计指标
%%
%% Java等价概念：
%% 类似于Spring MVC的Controller方法或RPC服务方法
%% 处理客户端请求并返回响应
handle_call({check_pressure, _Topic, Priority}, _From, State) ->
    %% 获取当前背压级别
    %% 从状态记录中提取当前的背压级别
    %% 类似于Java中获取当前的限流状态
    Level = State#pressure_state.level,

    %% 根据背压级别和优先级决定是否允许处理
    %% 使用case表达式实现策略模式，不同级别执行不同策略
    %% 类似于Java中的switch语句或策略模式
    %%
    %% 在Java中相当于：
    %% switch (currentLevel) {
    %%     case NORMAL: return allowAll();
    %%     case MILD: return checkWithMildRestrictions(priority);
    %%     // ... 其他情况
    %% }
    Result = case Level of
        ?PRESSURE_NORMAL ->
            %% 正常状态，允许所有消息通过
            %% 类似于Java中的无限制状态
            {allow, State#pressure_state.actions};
        ?PRESSURE_MILD ->
            %% 轻微背压，开始应用轻微限制
            %% 获取轻微背压级别的动作配置
            Actions = maps:get(?PRESSURE_MILD, State#pressure_state.actions),
            %% 检查消息优先级是否符合当前限制策略
            {check_priority(Priority, Actions), Actions};
        ?PRESSURE_MODERATE ->
            %% 中等背压，应用中等强度的限制
            %% 类似于Java中的中等限流策略
            Actions = maps:get(?PRESSURE_MODERATE, State#pressure_state.actions),
            {check_priority(Priority, Actions), Actions};
        ?PRESSURE_HIGH ->
            %% 高背压，应用严格的限制策略
            %% 类似于Java中的严格限流或熔断状态
            Actions = maps:get(?PRESSURE_HIGH, State#pressure_state.actions),
            {check_priority(Priority, Actions), Actions};
        ?PRESSURE_CRITICAL ->
            %% 临界背压，应用最严格的保护策略
            %% 类似于Java中的紧急保护模式或熔断器打开状态
            Actions = maps:get(?PRESSURE_CRITICAL, State#pressure_state.actions),
            {check_priority(Priority, Actions), Actions}
    end,

    % 更新指标
    NewState = case Result of
        {allow, _} ->
            % 允许处理，增加接收计数
            update_metrics_state(State, messages_received, 1);
        {reject, _} ->
            % 拒绝处理，增加拒绝计数
            update_metrics_state(State, messages_rejected, 1)
    end,

    {reply, Result, NewState};

handle_call(get_pressure_status, _From, State) ->
    % 构建状态报告
    Status = #{
        level => State#pressure_state.level,
        queue_length => State#pressure_state.queue_length,
        processing_time => State#pressure_state.processing_time,
        rejection_rate => State#pressure_state.rejection_rate,
        pressure_score => State#pressure_state.pressure_score,
        metrics => State#pressure_state.metrics
    },
    {reply, {ok, Status}, State};

handle_call({set_threshold, Type, Thresholds}, _From, State) ->
    % 更新阈值配置
    NewThresholds = maps:put(Type, Thresholds, State#pressure_state.thresholds),
    NewState = State#pressure_state{thresholds = NewThresholds},

    % 更新ETS中的状态
    ets:insert(?BACKPRESSURE_TAB, {pressure_state, NewState}),

    {reply, ok, NewState};

handle_call(reset_pressure, _From, State) ->
    % 重置背压状态
    NewState = State#pressure_state{
        level = ?PRESSURE_NORMAL,
        queue_length = 0,
        processing_time = 0,
        rejection_rate = 0.0,
        pressure_score = 0.0
    },

    % 更新ETS中的状态
    ets:insert(?BACKPRESSURE_TAB, {pressure_state, NewState}),

    {reply, ok, NewState};

handle_call(_Request, _From, State) ->
    {reply, {error, unknown_call}, State}.

%% @doc 处理异步调用
handle_cast({update_metrics, QueueLength, ProcessingTime, Success}, State) ->
    % 更新队列长度
    State1 = State#pressure_state{queue_length = QueueLength},

    % 更新处理时间
    State2 = State1#pressure_state{processing_time = ProcessingTime},

    % 更新指标
    State3 = case Success of
        true ->
            % 成功处理，增加处理计数
            update_metrics_state(State2, messages_processed, 1);
        false ->
            % 处理失败，不增加计数
            State2
    end,

    % 更新最大队列长度
    CurrentMax = maps:get(max_queue_length, State3#pressure_state.metrics, 0),
    State4 = if
        QueueLength > CurrentMax ->
            update_metrics_state(State3, max_queue_length, QueueLength);
        true ->
            State3
    end,

    % 更新平均处理时间
    CurrentAvg = maps:get(avg_processing_time, State4#pressure_state.metrics, 0),
    CurrentProcessed = maps:get(messages_processed, State4#pressure_state.metrics, 0),
    NewAvg = case CurrentProcessed of
        0 ->
            % 第一条消息，直接使用当前处理时间
            ProcessingTime;
        _ ->
            % 计算加权平均值
            (CurrentAvg * (CurrentProcessed - 1) + ProcessingTime) / CurrentProcessed
    end,
    State5 = update_metrics_state(State4, avg_processing_time, NewAvg),

    % 更新ETS中的状态
    ets:insert(?BACKPRESSURE_TAB, {pressure_state, State5}),

    {noreply, State5};

handle_cast(_Msg, State) ->
    {noreply, State}.

%% @doc 处理消息
handle_info(check_pressure_state, State) ->
    % 计算当前压力得分
    PressureScore = calculate_pressure(State),

    % 确定背压级别
    NewLevel = determine_pressure_level(PressureScore, State#pressure_state.thresholds),

    % 检查级别是否变化
    NewState = case NewLevel =:= State#pressure_state.level of
        true ->
            % 级别未变，仅更新得分
            State#pressure_state{pressure_score = PressureScore};
        false ->
            % 级别变化，记录变化
            Now = erlang:system_time(millisecond),
            PressureChanges = [{Now, State#pressure_state.level, NewLevel} |
                              lists:sublist(maps:get(pressure_changes, State#pressure_state.metrics, []), 9)],

            % 记录日志
            ?SLOG(info, #{
                msg => "mongodb_backpressure_level_changed",
                from => State#pressure_state.level,
                to => NewLevel,
                pressure_score => PressureScore,
                queue_length => State#pressure_state.queue_length,
                processing_time => State#pressure_state.processing_time
            }),

            % 更新状态
            NewMetrics = maps:put(pressure_changes, PressureChanges, State#pressure_state.metrics),
            State#pressure_state{
                level = NewLevel,
                pressure_score = PressureScore,
                last_update_time = Now,
                metrics = NewMetrics
            }
    end,

    % 更新ETS中的状态
    ets:insert(?BACKPRESSURE_TAB, {pressure_state, NewState}),

    % 重置定时器
    erlang:send_after(?BACKPRESSURE_MONITOR_INTERVAL, self(), check_pressure_state),

    {noreply, NewState};

handle_info(_Info, State) ->
    {noreply, State}.

%% @doc 终止回调
terminate(_Reason, _State) ->
    % 删除ETS表
    catch ets:delete(?BACKPRESSURE_TAB),
    ok.

%% @doc 代码更新回调
code_change(_OldVsn, State, _Extra) ->
    {ok, State}.

%%%===================================================================
%%% 内部函数
%%%===================================================================

%% @doc 初始化背压状态
init_pressure_state(Options) ->
    % 获取配置的阈值
    QueueThresholds = maps:get(queue_thresholds, Options, #{
        mild => 1000,
        moderate => 3000,
        high => 5000,
        critical => 8000
    }),

    % 获取配置的处理时间阈值
    TimeThresholds = maps:get(time_thresholds, Options, #{
        mild => 100,
        moderate => 300,
        high => 500,
        critical => 1000
    }),

    % 获取配置的系统负载阈值
    LoadThresholds = maps:get(load_thresholds, Options, #{
        mild => 0.6,
        moderate => 0.7,
        high => 0.8,
        critical => 0.9
    }),

    % 获取配置的背压动作
    Actions = maps:get(actions, Options, #{
        mild => [
            {throttle_rate, 0.9},
            {batch_size, 0.8}
        ],
        moderate => [
            {throttle_rate, 0.7},
            {batch_size, 0.6},
            {reject_low_priority, true}
        ],
        high => [
            {throttle_rate, 0.4},
            {batch_size, 0.3},
            {reject_low_priority, true},
            {reject_medium_priority, true}
        ],
        critical => [
            {throttle_rate, 0.1},
            {batch_size, 0.1},
            {reject_low_priority, true},
            {reject_medium_priority, true},
            {persist_only, true}
        ]
    }),

    % 创建初始状态
    #pressure_state{
        level = ?PRESSURE_NORMAL,
        last_update_time = erlang:system_time(millisecond),
        thresholds = #{
            queue_length => QueueThresholds,
            processing_time => TimeThresholds,
            system_load => LoadThresholds
        },
        actions = Actions
    }.

%% @doc 更新指标状态
update_metrics_state(State, Key, Value) when is_number(Value) ->
    NewMetrics = maps:update_with(Key, fun(OldValue) -> OldValue + Value end, Value, State#pressure_state.metrics),
    State#pressure_state{metrics = NewMetrics};
update_metrics_state(State, Key, Value) ->
    NewMetrics = maps:put(Key, Value, State#pressure_state.metrics),
    State#pressure_state{metrics = NewMetrics}.

%% @doc 检查优先级是否允许处理
check_priority(?PRIORITY_HIGH, _Actions) ->
    % 高优先级消息始终允许
    allow;
check_priority(?PRIORITY_MEDIUM, Actions) ->
    % 中优先级消息，检查是否拒绝中优先级
    case proplists:get_value(reject_medium_priority, Actions, false) of
        true -> reject;
        false -> allow
    end;
check_priority(?PRIORITY_LOW, Actions) ->
    % 低优先级消息，检查是否拒绝低优先级
    case proplists:get_value(reject_low_priority, Actions, false) of
        true -> reject;
        false -> allow
    end;
check_priority(_, _) ->
    % 默认允许
    allow.

%% @doc 确定背压级别
determine_pressure_level(Score, _) ->
    % 根据得分确定级别
    if
        Score >= 0.9 -> ?PRESSURE_CRITICAL;
        Score >= 0.7 -> ?PRESSURE_HIGH;
        Score >= 0.5 -> ?PRESSURE_MODERATE;
        Score >= 0.3 -> ?PRESSURE_MILD;
        true -> ?PRESSURE_NORMAL
    end.

%% @doc 计算压力得分
calculate_pressure(State) ->
    % 获取当前指标
    QueueLength = State#pressure_state.queue_length,
    ProcessingTime = State#pressure_state.processing_time,

    % 获取系统负载
    SystemLoad = try
        get_system_load()
    catch
        _:_ -> 0.5  % 默认中等负载
    end,

    % 获取阈值 (添加默认值防止maps:get失败)
    QueueThresholds = maps:get(queue_length, State#pressure_state.thresholds, #{}),
    TimeThresholds = maps:get(processing_time, State#pressure_state.thresholds, #{}),
    LoadThresholds = maps:get(system_load, State#pressure_state.thresholds, #{}),

    % 计算队列得分 (添加安全处理)
    QueueScore = try
        calculate_score(QueueLength, QueueThresholds)
    catch
        Exception1:Reason1:Stack1 ->
            ?SLOG(warning, #{
                msg => "queue_score_calculation_failed",
                exception => Exception1,
                reason => Reason1,
                stacktrace => Stack1,
                queue_length => QueueLength
            }),
            min(1.0, QueueLength / 10000)  % 安全默认值
    end,

    % 计算处理时间得分 (添加安全处理)
    TimeScore = try
        calculate_score(ProcessingTime, TimeThresholds)
    catch
        Exception2:Reason2:Stack2 ->
            ?SLOG(warning, #{
                msg => "time_score_calculation_failed",
                exception => Exception2,
                reason => Reason2,
                stacktrace => Stack2,
                processing_time => ProcessingTime
            }),
            min(1.0, ProcessingTime / 2000)  % 安全默认值
    end,

    % 计算系统负载得分 (添加安全处理)
    LoadScore = try
        calculate_score(SystemLoad, LoadThresholds)
    catch
        Exception3:Reason3:Stack3 ->
            ?SLOG(warning, #{
                msg => "load_score_calculation_failed",
                exception => Exception3,
                reason => Reason3,
                stacktrace => Stack3,
                system_load => SystemLoad
            }),
            SystemLoad  % 使用负载值作为得分
    end,

    % 综合得分 (加权平均)，确保结果在[0,1]范围内
    Score = 0.4 * QueueScore + 0.3 * TimeScore + 0.3 * LoadScore,
    max(0.0, min(1.0, Score)).

%% @doc 计算单项指标得分
calculate_score(Value, Thresholds) ->
    MildThreshold = maps:get(mild, Thresholds, 1),
    ModerateThreshold = maps:get(moderate, Thresholds, MildThreshold * 2),
    HighThreshold = maps:get(high, Thresholds, ModerateThreshold * 1.5),
    CriticalThreshold = maps:get(critical, Thresholds, HighThreshold * 1.5),

    try
        if
            Value >= CriticalThreshold -> 1.0;
            Value >= HighThreshold andalso (CriticalThreshold > HighThreshold) ->
                0.75 + 0.25 * (Value - HighThreshold) / (CriticalThreshold - HighThreshold);
            Value >= ModerateThreshold andalso (HighThreshold > ModerateThreshold) ->
                0.5 + 0.25 * (Value - ModerateThreshold) / (HighThreshold - ModerateThreshold);
            Value >= MildThreshold andalso (ModerateThreshold > MildThreshold) ->
                0.25 + 0.25 * (Value - MildThreshold) / (ModerateThreshold - MildThreshold);
            MildThreshold > 0 ->
                0.25 * Value / MildThreshold;
            true ->
                0.0
        end
    catch
        _:_ ->
            % 防止除零或其他计算错误
            case Value > 0 of
                true -> min(1.0, Value / 1000);  % 默认以1000作为基准值
                false -> 0.0
            end
    end.

%% @doc 获取系统负载
get_system_load() ->
    % 使用recon监控模块获取系统负载
    try
        emqx_plugin_mongodb_recon_monitor:get_system_load()
    catch
        _:_ ->
            % 出错时返回默认值
            0.5
    end.

%% @doc 背压监控工作进程
pressure_monitor() ->
    % 每秒检查一次系统状态
    timer:sleep(?BACKPRESSURE_MONITOR_INTERVAL),

    % 获取当前状态
    State = case ets:lookup(?BACKPRESSURE_TAB, pressure_state) of
        [{pressure_state, S}] -> S;
        [] -> #pressure_state{}
    end,

    % 计算压力得分
    PressureScore = calculate_pressure(State),

    % 记录到ETS
    ets:insert(?BACKPRESSURE_TAB, {current_pressure, PressureScore}),

    % 递归调用
    pressure_monitor().
