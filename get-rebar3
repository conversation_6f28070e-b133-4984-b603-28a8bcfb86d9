#!/usr/bin/env bash

set -euo pipefail

VERSION="$1"

# ensure dir
cd -P -- "$(dirname -- "${BASH_SOURCE[0]}")/"

DOWNLOAD_URL='https://github.com/emqx/rebar3/releases/download'

download() {
    local url="${DOWNLOAD_URL}/${VERSION}/rebar3"

    echo "Downloading rebar3 from '${url}' ..."
    curl --silent --show-error -f -L "${url}" -o ./rebar3
}

# get the version number from the second line of the escript
# because command `rebar3 -v` tries to load rebar.config
# which is slow and may print some logs
version() {
    head -n 2 ./rebar3 | tail -n 1 | tr ' ' '\n' | grep -E '^.+-emqx-.+'
}

if [ -f 'rebar3' ] && [ "$(version)" = "$VERSION" ]; then
    exit 0
fi

download
chmod +x ./rebar3
