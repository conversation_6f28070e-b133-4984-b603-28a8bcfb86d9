%% -*- mode: erlang -*-

%% 项目依赖配置
{deps,
    [
        %% EMQX 核心依赖
        {emqx, {git_subdir, "https://github.com/emqx/emqx.git", {tag, "v5.4.0"}, "apps/emqx"}}
        %% EMQX 控制台工具
        , {emqx_ctl, {git_subdir, "https://github.com/emqx/emqx.git", {tag, "v5.4.0"}, "apps/emqx_ctl"}}
        %% EMQX 工具库
        , {emqx_utils, {git_subdir, "https://github.com/emqx/emqx.git", {tag, "v5.4.0"}, "apps/emqx_utils"}}
        %% EMQX 持久化存储
        , {emqx_durable_storage, {git_subdir, "https://github.com/emqx/emqx.git", {tag, "v5.4.0"}, "apps/emqx_durable_storage"}}
        %% EMQX 资源管理
        , {emqx_resource, {git_subdir, "https://github.com/emqx/emqx.git", {tag, "v5.4.0"}, "apps/emqx_resource"}}
        %% MongoDB Erlang驱动
        , {mongodb, {git, "https://github.com/igit-cn/mongodb-erlang.git", {tag, "v3.0.26"}}}
        %% 添加jsx依赖
        , {jsx, {git, "https://github.com/talentdeficit/jsx.git", {tag, "3.1.0"}}}
    ]}.

%% 插件配置
{plugins, [
    {emqx_plugrel, {git, "https://github.com/emqx/emqx_plugrel.git", {tag, "0.3.0"}}}
]}.

%% Erlang编译选项
{erl_opts, [debug_info, {feature, maybe_expr, disable}]}.

%% 发布配置
{relx, [{release, {emqx_plugin_mongodb, "v5.4.0"},
    [
        emqx_plugin_mongodb
    ]}
    , {include_src, false}  %% 不包含源代码
    , {dev_mode, false}     %% 非开发模式
    , {include_erts, false} %% 不包含ERTS
]}.

%% EMQX插件发布相关配置
{emqx_plugrel,
    [{authors, ["WXW"]}
        , {builder,
        [{name, ""}
            , {contact, ""}
            , {website, ""}
        ]}
        , {repo, "https://git.bonahl.com/sys/emqx_plugin_mongodb"}
        , {functionality, ["MQTT MSG Bridge TO MongoDB"]}
        , {compatibility,
        [{emqx, "~>= v5.4.0 && <= v5.6.1"}
        ]}
        , {description, "MongoDB Plugin For EMQX."}
    ]
}.

%% 交叉引用检查配置
{xref_checks, [undefined_function_calls, undefined_functions, locals_not_used,
    deprecated_function_calls, warnings_as_errors, deprecated_functions]}.
